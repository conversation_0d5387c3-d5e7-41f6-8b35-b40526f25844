<?xml version="1.0" encoding="UTF-8"?>
<!-- scan 配置文件如果发生改变，将会被重新加载  scanPeriod 检测间隔时间-->
<configuration debug="false" scan="true" scanPeriod="60 seconds">

  <springProperty scope="context" name="springLevel" source="logging.level.spring" defaultValue="INFO"/>
  <property name="DIAN_MONITOR_LEVEL" value="ERROR"/>
  <property name="LOG_PATH" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}"/>
  <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(traceId=%X{trace_id:-}){yellow} %clr(spanId=%X{span_id:-}){yellow} %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

  <!--引入父配置-->
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
  <include resource="included-config.xml"/>

  <appender name="OpenTelemetry"
            class="io.opentelemetry.instrumentation.logback.mdc.v1_0.OpenTelemetryAppender">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="DIAN_MONITOR_SURPLUS"/>
    <appender-ref ref="DIAN_MONITOR"/>
  </appender>

  <springProfile name="!local">
    <!--日志topic为error，即 @Slf4j(topic = "error") 或 LoggerFactory.getLogger("error") 方式打印的日志-->
    <logger name="error" level="ERROR" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--日志topic为biz，即 @Slf4j 或 LoggerFactory.getLogger("biz") 方式打印的日志-->
    <logger name="biz" level="${bizLevel}" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--日志topic为web，即 @Slf4j(topic = "web") 或 LoggerFactory.getLogger("web") 方式打印的日志-->
    <logger name="web" level="INFO" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--日志topic为remote，即 @Slf4j 或 LoggerFactory.getLogger("remote") 方式打印的日志-->
    <logger name="remote" level="INFO" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--日志topic为rt，即 @Slf4j(topic = "rt") 或 LoggerFactory.getLogger("rt") 方式打印的日志-->
    <logger name="rt" level="INFO" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--日志topic为sqlRt，即 @Slf4j(topic = "sqlRt") 或 LoggerFactory.getLogger("sqlRt") 方式打印的日志-->
    <logger name="sqlRt" level="${sqlLevel}" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--日志topic为cache，即 @Slf4j 或 LoggerFactory.getLogger("cache") 方式打印的日志-->
    <logger name="cache" level="INFO" additivity="false">
      <appender-ref ref="OpenTelemetry"/>
    </logger>

    <!--默认日志topic，即 @Slf4j 或 LoggerFactory.getLogger(ClassName.class}) 方式打印的日志-->
    <root level="${springLevel}">
      <appender-ref ref="OpenTelemetry"/>
    </root>
  </springProfile>

  <springProfile name="local">
    <root level="${springLevel}">
      <appender-ref ref="OpenTelemetry"/>
    </root>
  </springProfile>

  <springProfile name="stable">
    <root level="${springLevel}">
      <appender-ref ref="OpenTelemetry"/>
    </root>
  </springProfile>


  <springProfile name="dev">
    <root level="${springLevel}">
      <appender-ref ref="OpenTelemetry"/>
    </root>
  </springProfile>

</configuration>