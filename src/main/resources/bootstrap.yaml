feign:
  httpclient:
    enabled: false
    hc5:
      enabled: true
spring:
  application:
    name: huashan
  profiles:
    active: local
  cloud:
    nacos:
      config:
        enabled: false
        server-addr: http://nacos-dev.dian.so:80
        file-extension: yaml
        shared-configs: global-hystrix.yaml,global22X.yaml,global22X-management.yaml
server:
  port: 8080

---
spring:
  config:
    activate:
      on-profile:
        - local
  cloud:
    nacos:
      config:
        enabled: false               # enable nacos配置
        namespace: dev              # 配置对应环境的namespace, ns参考下面的图片
        server-addr: http://nacos-dev.dian.so:80
---
spring:
  config:
    activate:
      on-profile:
        - dev
  cloud:
    nacos:
      config:
        enabled: true               # enable nacos配置
        namespace: dev              # 配置对应环境的namespace, ns参考下面的图片
        server-addr: http://nacos-dev.dian.so:80

---
spring:
  config:
    activate:
      on-profile:
        - stable
  cloud:
    nacos:
      config:
        enabled: true
        namespace: 4061bdcb-a707-4b81-b689-a035ad009309           #配置对应环境的namespace, ns参考下面的图片
        server-addr: http://nacos-dev.dian.so:80

---
spring:
  config:
    activate:
      on-profile:
        - pre
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: nacos-real.dian.so:80
        namespace: c1f38994-50a4-482f-bd30-95d65188cf9d            #配置对应环境的namespace, ns参考下面的图片

---
spring:
  config:
    activate:
      on-profile:
        - real
  cloud:
    nacos:
      config:
        enabled: true
        server-addr: nacos-real.dian.so:80                            # 配置对应的nacos地址
        namespace: fcf1f0b9-4c31-43e5-80ef-f33a8d5d79dc               # 配置对应环境的namespace, ns参考下面的图片
