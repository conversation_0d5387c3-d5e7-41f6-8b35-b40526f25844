<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.newyork.tidb.TidbTradeOrderMapper">

  <sql id="Base_Column_List">
    id, biz_order_no, biz_type, trade_type, pay_amount, ext6
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="so.dian.huashan.common.mapper.newyork.dos.TidbTradeOrderDO">
    select 
    <include refid="Base_Column_List" />
    from trade_order
    where id = #{id,jdbcType=BIGINT}
  </select>

</mapper>