<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.newyork.sharding.ShardingRefundOrderMapper">

  <sql id="Base_Column_List">
    id, biz_order_no, biz_refund_order_no, refund_type, refund_amount, actual_refund_amount
  </sql>

  <select id="selectByBizOrderNo" parameterType="java.lang.String" resultType="so.dian.huashan.common.mapper.newyork.dos.ShardingRefundOrderDO">
    select 
    <include refid="Base_Column_List" />
    from refund_order
    where biz_order_no = #{bizOrderNo,jdbcType=VARCHAR}
  </select>

</mapper>