<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.contract.ContractShopDivideMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="settle_subject_type" jdbcType="TINYINT" property="settleSubjectType" />
    <result column="settle_subject_id" jdbcType="BIGINT" property="settleSubjectId" />
    <result column="main_biz_type" jdbcType="TINYINT" property="mainBizType" />
    <result column="main_biz_id" jdbcType="BIGINT" property="mainBizId" />
    <result column="effect_time" jdbcType="BIGINT" property="effectTime" />
    <result column="invalid_time" jdbcType="BIGINT" property="invalidTime" />
    <result column="ratio" jdbcType="INTEGER" property="ratio" />
    <result column="brand_contract_id" jdbcType="BIGINT" property="brandContractId" />
    <result column="expired" jdbcType="TINYINT" property="expired" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, shop_id, contract_id, settle_subject_type, settle_subject_id, main_biz_type, 
    main_biz_id, effect_time, invalid_time, ratio, brand_contract_id, expired, gmt_create, 
    gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contract_shop_divide
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from contract_shop_divide
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO" useGeneratedKeys="true">
    insert into contract_shop_divide (shop_id, contract_id, settle_subject_type, 
      settle_subject_id, main_biz_type, main_biz_id, 
      effect_time, invalid_time, ratio, 
      brand_contract_id, expired, gmt_create, 
      gmt_update, deleted)
    values (#{shopId,jdbcType=BIGINT}, #{contractId,jdbcType=BIGINT}, #{settleSubjectType,jdbcType=TINYINT}, 
      #{settleSubjectId,jdbcType=BIGINT}, #{mainBizType,jdbcType=TINYINT}, #{mainBizId,jdbcType=BIGINT}, 
      #{effectTime,jdbcType=BIGINT}, #{invalidTime,jdbcType=BIGINT}, #{ratio,jdbcType=INTEGER}, 
      #{brandContractId,jdbcType=BIGINT}, #{expired,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO" useGeneratedKeys="true">
    insert into contract_shop_divide
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="settleSubjectType != null">
        settle_subject_type,
      </if>
      <if test="settleSubjectId != null">
        settle_subject_id,
      </if>
      <if test="mainBizType != null">
        main_biz_type,
      </if>
      <if test="mainBizId != null">
        main_biz_id,
      </if>
      <if test="effectTime != null">
        effect_time,
      </if>
      <if test="invalidTime != null">
        invalid_time,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="brandContractId != null">
        brand_contract_id,
      </if>
      <if test="expired != null">
        expired,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=BIGINT},
      </if>
      <if test="settleSubjectType != null">
        #{settleSubjectType,jdbcType=TINYINT},
      </if>
      <if test="settleSubjectId != null">
        #{settleSubjectId,jdbcType=BIGINT},
      </if>
      <if test="mainBizType != null">
        #{mainBizType,jdbcType=TINYINT},
      </if>
      <if test="mainBizId != null">
        #{mainBizId,jdbcType=BIGINT},
      </if>
      <if test="effectTime != null">
        #{effectTime,jdbcType=BIGINT},
      </if>
      <if test="invalidTime != null">
        #{invalidTime,jdbcType=BIGINT},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=INTEGER},
      </if>
      <if test="brandContractId != null">
        #{brandContractId,jdbcType=BIGINT},
      </if>
      <if test="expired != null">
        #{expired,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO">
    update contract_shop_divide
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=BIGINT},
      </if>
      <if test="settleSubjectType != null">
        settle_subject_type = #{settleSubjectType,jdbcType=TINYINT},
      </if>
      <if test="settleSubjectId != null">
        settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      </if>
      <if test="mainBizType != null">
        main_biz_type = #{mainBizType,jdbcType=TINYINT},
      </if>
      <if test="mainBizId != null">
        main_biz_id = #{mainBizId,jdbcType=BIGINT},
      </if>
      <if test="effectTime != null">
        effect_time = #{effectTime,jdbcType=BIGINT},
      </if>
      <if test="invalidTime != null">
        invalid_time = #{invalidTime,jdbcType=BIGINT},
      </if>
      <if test="ratio != null">
        ratio = #{ratio,jdbcType=INTEGER},
      </if>
      <if test="brandContractId != null">
        brand_contract_id = #{brandContractId,jdbcType=BIGINT},
      </if>
      <if test="expired != null">
        expired = #{expired,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO">
    update contract_shop_divide
    set shop_id = #{shopId,jdbcType=BIGINT},
      contract_id = #{contractId,jdbcType=BIGINT},
      settle_subject_type = #{settleSubjectType,jdbcType=TINYINT},
      settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      main_biz_type = #{mainBizType,jdbcType=TINYINT},
      main_biz_id = #{mainBizId,jdbcType=BIGINT},
      effect_time = #{effectTime,jdbcType=BIGINT},
      invalid_time = #{invalidTime,jdbcType=BIGINT},
      ratio = #{ratio,jdbcType=INTEGER},
      brand_contract_id = #{brandContractId,jdbcType=BIGINT},
      expired = #{expired,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectUsable" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from contract_shop_divide
    where deleted = 0
    and shop_id = #{shopId,jdbcType=BIGINT}
    and effect_time <![CDATA[<=]]> #{time,jdbcType=BIGINT}
    and invalid_time <![CDATA[>=]]> #{time,jdbcType=BIGINT}
    and gmt_create <![CDATA[<=]]> #{time,jdbcType=BIGINT}
  </select>
</mapper>