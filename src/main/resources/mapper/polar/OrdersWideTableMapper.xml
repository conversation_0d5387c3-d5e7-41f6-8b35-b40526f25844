<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.polar.OrdersWideTableMapper">
  <select id="selectByOrderNos" resultType="so.dian.huashan.common.service.entity.ControlOrderBO">
    select order_no,control_result
    from orders_wide_table
    where order_no in
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
  </select>
</mapper>