<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.PayBillThirdMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.PayBillThirdDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_id" jdbcType="BIGINT" property="fileId" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="trade_amount" jdbcType="BIGINT" property="tradeAmount" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="third_status" jdbcType="TINYINT" property="thirdStatus" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="pay_subject_id" jdbcType="BIGINT" property="paySubjectId" />
    <result column="pay_subject_type" jdbcType="TINYINT" property="paySubjectType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, file_id, trade_type, trade_no, channel_trade_no, trade_time, trade_amount, `source`,
    remark, third_status, `status`, deleted, gmt_create, gmt_update, channel_id,pay_subject_id, pay_subject_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pay_bill_third
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pay_bill_third
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.PayBillThirdDO" useGeneratedKeys="true">
    insert into pay_bill_third (file_id, trade_type, trade_no,
      channel_trade_no, trade_time, trade_amount,
      `source`, remark, third_status, 
      `status`, deleted, gmt_create, 
      gmt_update, channel_id, pay_subject_id, pay_subject_type)
    values (#{fileId,jdbcType=BIGINT}, #{tradeType,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, 
      #{channelTradeNo,jdbcType=VARCHAR}, #{tradeTime,jdbcType=TIMESTAMP}, #{tradeAmount,jdbcType=BIGINT},
      #{source,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{thirdStatus,jdbcType=TINYINT}, 
      #{status,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT}, #{channelId,jdbcType=BIGINT}, #{paySubjectId,jdbcType=BIGINT}, #{paySubjectType,jdbcType=TINYINT})
  </insert>
  <insert id="insertBatch" keyProperty="id">
  insert into pay_bill_third (file_id, trade_type, trade_no,
  channel_trade_no, trade_time, trade_amount,
  `source`, remark, third_status,
  `status`, deleted, gmt_create,
  gmt_update, channel_id, pay_subject_id, pay_subject_type)
  values
  <foreach collection="list" item="item" separator=",">
    (#{item.fileId,jdbcType=BIGINT}, #{item.tradeType,jdbcType=VARCHAR}, #{item.tradeNo,jdbcType=VARCHAR},
    #{item.channelTradeNo,jdbcType=VARCHAR}, #{item.tradeTime,jdbcType=TIMESTAMP}, #{item.tradeAmount,jdbcType=BIGINT},
    #{item.source,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR}, #{item.thirdStatus,jdbcType=TINYINT},
    #{item.status,jdbcType=INTEGER}, #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT},
    #{item.gmtUpdate,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, #{item.paySubjectId,jdbcType=BIGINT}, #{item.paySubjectType,jdbcType=TINYINT})
  </foreach>
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.PayBillThirdDO" useGeneratedKeys="true">
    insert into pay_bill_third
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        file_id,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="thirdStatus != null">
        third_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="paySubjectId != null">
        pay_subject_id,
      </if>
      <if test="paySubjectType != null">
        pay_subject_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        #{fileId,jdbcType=BIGINT},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="thirdStatus != null">
        #{thirdStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectId != null">
        #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        #{paySubjectType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateBatch" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      update pay_bill_third
      <set>
        <if test="item.fileId != null">
          file_id = #{item.fileId,jdbcType=BIGINT},
        </if>
        <if test="item.tradeType != null">
          trade_type = #{item.tradeType,jdbcType=VARCHAR},
        </if>
        <if test="item.tradeNo != null">
          trade_no = #{item.tradeNo,jdbcType=VARCHAR},
        </if>
        <if test="item.channelTradeNo != null">
          channel_trade_no = #{item.channelTradeNo,jdbcType=VARCHAR},
        </if>
        <if test="item.tradeTime != null">
          trade_time = #{item.tradeTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.tradeAmount != null">
          trade_amount = #{item.tradeAmount,jdbcType=BIGINT},
        </if>
        <if test="item.source != null">
          `source` = #{item.source,jdbcType=TINYINT},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.thirdStatus != null">
          third_status = #{item.thirdStatus,jdbcType=TINYINT},
        </if>
        <if test="item.status != null">
          `status` = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.deleted != null">
          deleted = #{item.deleted,jdbcType=TINYINT},
        </if>
        <if test="item.gmtCreate != null">
          gmt_create = #{gmtCreate,jdbcType=BIGINT},
        </if>
        <if test="item.gmtUpdate != null">
          gmt_update = #{item.gmtUpdate,jdbcType=BIGINT},
        </if>
        <if test="item.channelId != null">
          channel_id = #{item.channelId,jdbcType=BIGINT},
        </if>
        <if test="item.paySubjectId != null">
          pay_subject_id = #{item.paySubjectId,jdbcType=BIGINT},
        </if>
        <if test="item.paySubjectType != null">
          pay_subject_type = #{item.paySubjectType,jdbcType=TINYINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.PayBillThirdDO">
    update pay_bill_third
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=BIGINT},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="thirdStatus != null">
        third_status = #{thirdStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectId != null">
        pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.PayBillThirdDO">
    update pay_bill_third
    set file_id = #{fileId,jdbcType=BIGINT},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      trade_amount = #{tradeAmount,jdbcType=BIGINT},
      `source` = #{source,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      third_status = #{thirdStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      pay_subject_type = #{paySubjectType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getOne" parameterType="so.dian.huashan.collection.mapper.param.PayBillThirdParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pay_bill_third
    <where>
      deleted = 0
      <if test="tradeNo != null">
        and trade_no = #{tradeNo,jdbcType=VARCHAR}
      </if>
      <if test="tradeType != null">
        and trade_type = #{tradeType,jdbcType=VARCHAR}
      </if>
    </where>
    limit 1
  </select>
  <select id="selectListByParam" parameterType="so.dian.huashan.collection.mapper.param.PayBillThirdParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pay_bill_third
    <where>
      deleted = 0
      <if test="tradeNos != null">
        and trade_no in
        <foreach collection="tradeNos" item="tradeNo" open="(" close=")" separator=",">
          #{tradeNo}
        </foreach>
      </if>
      <if test="tradeNo != null">
        and trade_no = #{tradeNo,jdbcType=VARCHAR}
      </if>
      <if test="tradeType != null">
        and trade_type = #{tradeType,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="selectByTradeNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill_third
    where deleted = 0 and trade_type = 'out'
    <if test="channelTradeNos != null">
      and channel_trade_no in
      <foreach collection="channelTradeNos" item="tradeNo" open="(" close=")" separator=",">
        #{tradeNo}
      </foreach>
    </if>
  </select>

  <select id="selectByCheckDateTime" parameterType="map" resultType="long">
    select
    id
    from pay_bill_third
    where deleted = 0 and `status` &amp; 15 in(0,2)
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="startTime != null">
      and gmt_update <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
    </if>
    <if test="endTime != null">
      and gmt_update <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
    </if>
    order by id asc
  </select>

  <update id="batchUpdateStatusByIds" parameterType="list">
    update pay_bill_third set `status` = (`status` &amp;1048560) + #{status,jdbcType=INTEGER},gmt_update = UNIX_TIMESTAMP() * 1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill_third
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>