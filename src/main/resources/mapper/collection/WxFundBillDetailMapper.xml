<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.WxFundBillDetailMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mch_type" jdbcType="TINYINT" property="mchType" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="bill_date" jdbcType="INTEGER" property="billDate" />
    <result column="pay_biz_no" jdbcType="VARCHAR" property="payBizNo" />
    <result column="fund_no" jdbcType="VARCHAR" property="fundNo" />
    <result column="biz_name" jdbcType="VARCHAR" property="bizName" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="income_type" jdbcType="VARCHAR" property="incomeType" />
    <result column="income_amt" jdbcType="BIGINT" property="incomeAmt" />
    <result column="acct_balance" jdbcType="BIGINT" property="acctBalance" />
    <result column="change_initiator" jdbcType="VARCHAR" property="changeInitiator" />
    <result column="biz_voucher_no" jdbcType="VARCHAR" property="bizVoucherNo" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mch_type, mch_id, bill_date, pay_biz_no, fund_no, biz_name, biz_type, income_type, 
    income_amt, acct_balance, change_initiator, biz_voucher_no, remark, deleted, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_fund_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_fund_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO" useGeneratedKeys="true">
    insert into wx_fund_bill_detail (mch_type, mch_id, bill_date, 
      pay_biz_no, fund_no, biz_name, 
      biz_type, income_type, income_amt, 
      acct_balance, change_initiator, biz_voucher_no, 
      remark, deleted, gmt_create, 
      gmt_update)
    values (#{mchType,jdbcType=TINYINT}, #{mchId,jdbcType=VARCHAR}, #{billDate,jdbcType=INTEGER}, 
      #{payBizNo,jdbcType=VARCHAR}, #{fundNo,jdbcType=VARCHAR}, #{bizName,jdbcType=VARCHAR}, 
      #{bizType,jdbcType=VARCHAR}, #{incomeType,jdbcType=VARCHAR}, #{incomeAmt,jdbcType=BIGINT}, 
      #{acctBalance,jdbcType=BIGINT}, #{changeInitiator,jdbcType=VARCHAR}, #{bizVoucherNo,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertBatch"  keyProperty="id" >
    insert into wx_fund_bill_detail (mch_type, mch_id, bill_date,
                                     pay_biz_no, fund_no, biz_name,
                                     biz_type, income_type, income_amt,
                                     acct_balance, change_initiator, biz_voucher_no,
                                     remark, deleted, gmt_create,
                                     gmt_update)
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.mchType,jdbcType=TINYINT}, #{item.mchId,jdbcType=VARCHAR}, #{item.billDate,jdbcType=INTEGER},
            #{item.payBizNo,jdbcType=VARCHAR}, #{item.fundNo,jdbcType=VARCHAR}, #{item.bizName,jdbcType=VARCHAR},
            #{item.bizType,jdbcType=VARCHAR}, #{item.incomeType,jdbcType=VARCHAR}, #{item.incomeAmt,jdbcType=BIGINT},
            #{item.acctBalance,jdbcType=BIGINT}, #{item.changeInitiator,jdbcType=VARCHAR}, #{item.bizVoucherNo,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT},
            #{item.gmtUpdate,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO" useGeneratedKeys="true">
    insert into wx_fund_bill_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mchType != null">
        mch_type,
      </if>
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="payBizNo != null">
        pay_biz_no,
      </if>
      <if test="fundNo != null">
        fund_no,
      </if>
      <if test="bizName != null">
        biz_name,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="incomeType != null">
        income_type,
      </if>
      <if test="incomeAmt != null">
        income_amt,
      </if>
      <if test="acctBalance != null">
        acct_balance,
      </if>
      <if test="changeInitiator != null">
        change_initiator,
      </if>
      <if test="bizVoucherNo != null">
        biz_voucher_no,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mchType != null">
        #{mchType,jdbcType=TINYINT},
      </if>
      <if test="mchId != null">
        #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=INTEGER},
      </if>
      <if test="payBizNo != null">
        #{payBizNo,jdbcType=VARCHAR},
      </if>
      <if test="fundNo != null">
        #{fundNo,jdbcType=VARCHAR},
      </if>
      <if test="bizName != null">
        #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="incomeType != null">
        #{incomeType,jdbcType=VARCHAR},
      </if>
      <if test="incomeAmt != null">
        #{incomeAmt,jdbcType=BIGINT},
      </if>
      <if test="acctBalance != null">
        #{acctBalance,jdbcType=BIGINT},
      </if>
      <if test="changeInitiator != null">
        #{changeInitiator,jdbcType=VARCHAR},
      </if>
      <if test="bizVoucherNo != null">
        #{bizVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO">
    update wx_fund_bill_detail
    <set>
      <if test="mchType != null">
        mch_type = #{mchType,jdbcType=TINYINT},
      </if>
      <if test="mchId != null">
        mch_id = #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=INTEGER},
      </if>
      <if test="payBizNo != null">
        pay_biz_no = #{payBizNo,jdbcType=VARCHAR},
      </if>
      <if test="fundNo != null">
        fund_no = #{fundNo,jdbcType=VARCHAR},
      </if>
      <if test="bizName != null">
        biz_name = #{bizName,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="incomeType != null">
        income_type = #{incomeType,jdbcType=VARCHAR},
      </if>
      <if test="incomeAmt != null">
        income_amt = #{incomeAmt,jdbcType=BIGINT},
      </if>
      <if test="acctBalance != null">
        acct_balance = #{acctBalance,jdbcType=BIGINT},
      </if>
      <if test="changeInitiator != null">
        change_initiator = #{changeInitiator,jdbcType=VARCHAR},
      </if>
      <if test="bizVoucherNo != null">
        biz_voucher_no = #{bizVoucherNo,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO">
    update wx_fund_bill_detail
    set mch_type = #{mchType,jdbcType=TINYINT},
      mch_id = #{mchId,jdbcType=VARCHAR},
      bill_date = #{billDate,jdbcType=INTEGER},
      pay_biz_no = #{payBizNo,jdbcType=VARCHAR},
      fund_no = #{fundNo,jdbcType=VARCHAR},
      biz_name = #{bizName,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      income_type = #{incomeType,jdbcType=VARCHAR},
      income_amt = #{incomeAmt,jdbcType=BIGINT},
      acct_balance = #{acctBalance,jdbcType=BIGINT},
      change_initiator = #{changeInitiator,jdbcType=VARCHAR},
      biz_voucher_no = #{bizVoucherNo,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>