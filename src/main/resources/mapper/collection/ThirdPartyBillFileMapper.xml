<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.ThirdPartyBillFileMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="bill_date" jdbcType="INTEGER" property="billDate" />
    <result column="collection_registry_id" jdbcType="BIGINT" property="collectionRegistryId" />
    <result column="channel_pay_type" jdbcType="VARCHAR" property="channelPayType" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `status`, bill_date, collection_registry_id, channel_pay_type, file_path, deleted, gmt_create,
    gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from third_party_bill_file
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectList" parameterType="java.util.List" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from third_party_bill_file
    where id in
    <foreach collection="list" item="id" open="(" separator="," close=")">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from third_party_bill_file
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO" useGeneratedKeys="true">
    insert into third_party_bill_file (`status`, bill_date, collection_registry_id, channel_pay_type,
      file_path, deleted, gmt_create, 
      gmt_update)
    values (#{status,jdbcType=TINYINT}, #{billDate,jdbcType=INTEGER}, #{collectionRegistryId,jdbcType=BIGINT}, #{channelPayType,jdbcType=VARCHAR},
      #{filePath,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO" useGeneratedKeys="true">
    insert into third_party_bill_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="collectionRegistryId != null">
        collection_registry_id,
      </if>
      <if test="channelPayType != null">
        channel_pay_type,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=INTEGER},
      </if>
      <if test="collectionRegistryId != null">
        #{collectionRegistryId,jdbcType=BIGINT},
      </if>
      <if test="channelPayType != null">
        #{channelPayType,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO">
    update third_party_bill_file
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=INTEGER},
      </if>
      <if test="collectionRegistryId != null">
        collection_registry_id = #{collectionRegistryId,jdbcType=BIGINT},
      </if>
      <if test="channelPayType != null">
        channel_pay_type = #{channelPayType,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO">
    update third_party_bill_file
    set `status` = #{status,jdbcType=TINYINT},
      bill_date = #{billDate,jdbcType=INTEGER},
      collection_registry_id = #{collectionRegistryId,jdbcType=BIGINT},
      channel_pay_type = #{channelPayType,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByExample" parameterType="so.dian.huashan.collection.mapper.example.ThirdPartyBillFileExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from third_party_bill_file
    where deleted = 0
    <if test="channelPayType != null">
      and channel_pay_type = #{channelPayType,jdbcType=VARCHAR}
    </if>
    <if test="billDate != null">
      and bill_date = #{billDate,jdbcType=INTEGER}
    </if>
  </select>
</mapper>