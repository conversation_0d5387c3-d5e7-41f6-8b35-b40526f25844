<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.SettleDetailOriginalMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
    <result column="settle_detail_no" jdbcType="VARCHAR" property="settleDetailNo" />
    <result column="tp_settle_no" jdbcType="VARCHAR" property="tpSettleNo" />
    <result column="tp_source_id" jdbcType="VARCHAR" property="tpSourceId" />
    <result column="tp_receive_id" jdbcType="VARCHAR" property="tpReceiveId" />
    <result column="amount_output" jdbcType="BIGINT" property="amountOutput" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, check_status, settle_detail_no, tp_settle_no, tp_source_id, tp_receive_id, amount_output, 
    deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_detail_original
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from settle_detail_original
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO" useGeneratedKeys="true">
    insert into settle_detail_original (check_status, settle_detail_no, tp_settle_no, 
      tp_source_id, tp_receive_id, amount_output, 
      deleted, gmt_create, gmt_update
      )
    values (#{checkStatus,jdbcType=TINYINT}, #{settleDetailNo,jdbcType=VARCHAR}, #{tpSettleNo,jdbcType=VARCHAR}, 
      #{tpSourceId,jdbcType=VARCHAR}, #{tpReceiveId,jdbcType=VARCHAR}, #{amountOutput,jdbcType=BIGINT}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO" useGeneratedKeys="true">
    insert into settle_detail_original
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="settleDetailNo != null">
        settle_detail_no,
      </if>
      <if test="tpSettleNo != null">
        tp_settle_no,
      </if>
      <if test="tpSourceId != null">
        tp_source_id,
      </if>
      <if test="tpReceiveId != null">
        tp_receive_id,
      </if>
      <if test="amountOutput != null">
        amount_output,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="settleDetailNo != null">
        #{settleDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="tpSettleNo != null">
        #{tpSettleNo,jdbcType=VARCHAR},
      </if>
      <if test="tpSourceId != null">
        #{tpSourceId,jdbcType=VARCHAR},
      </if>
      <if test="tpReceiveId != null">
        #{tpReceiveId,jdbcType=VARCHAR},
      </if>
      <if test="amountOutput != null">
        #{amountOutput,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO">
    update settle_detail_original
    <set>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="settleDetailNo != null">
        settle_detail_no = #{settleDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="tpSettleNo != null">
        tp_settle_no = #{tpSettleNo,jdbcType=VARCHAR},
      </if>
      <if test="tpSourceId != null">
        tp_source_id = #{tpSourceId,jdbcType=VARCHAR},
      </if>
      <if test="tpReceiveId != null">
        tp_receive_id = #{tpReceiveId,jdbcType=VARCHAR},
      </if>
      <if test="amountOutput != null">
        amount_output = #{amountOutput,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO">
    update settle_detail_original
    set check_status = #{checkStatus,jdbcType=TINYINT},
      settle_detail_no = #{settleDetailNo,jdbcType=VARCHAR},
      tp_settle_no = #{tpSettleNo,jdbcType=VARCHAR},
      tp_source_id = #{tpSourceId,jdbcType=VARCHAR},
      tp_receive_id = #{tpReceiveId,jdbcType=VARCHAR},
      amount_output = #{amountOutput,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCheckDateTime" parameterType="map" resultType="long">
    select
    id
    from settle_detail_original
    where deleted = 0 and check_status in(0)
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="startTime != null">
      and gmt_update <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
    </if>
    <if test="endTime != null">
      and gmt_update <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
    </if>
    order by id asc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from settle_detail_original
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
  <update id="batchUpdateStatusByIds" parameterType="list">
    update settle_detail_original set check_status = #{status,jdbcType=INTEGER},gmt_update = UNIX_TIMESTAMP() * 1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectBySettleDetailNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from settle_detail_original
    where deleted = 0
    and settle_detail_no = #{settleDetailNo}
  </select>

  <select id="selectBySettleDetailNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from settle_detail_original
    where deleted = 0
    <if test="settleDetailNos != null">
      and settle_detail_no in
      <foreach collection="settleDetailNos" item="settleDetailNo" open="(" close=")" separator=",">
        #{settleDetailNo}
      </foreach>
    </if>
  </select>


</mapper>