<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.BillSettleOrderOriginalMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="voucher_id" jdbcType="BIGINT" property="voucherId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="pay_amount" jdbcType="BIGINT" property="payAmount" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="loan_shop_id" jdbcType="BIGINT" property="loanShopId" />
    <result column="return_box_no" jdbcType="VARCHAR" property="returnBoxNo" />
    <result column="loan_price_info" jdbcType="VARCHAR" property="loanPriceInfo" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="pay_success_time" jdbcType="TIMESTAMP" property="paySuccessTime" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `status`, voucher_id, order_no, order_status, order_amount, pay_amount, pay_time, loan_shop_id,
    return_box_no, loan_price_info, gmt_create, gmt_update, deleted, version, pay_success_time,biz_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_settle_order_original
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_settle_order_original
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO" useGeneratedKeys="true">
    insert into bill_settle_order_original (`status`, voucher_id, order_no, order_status,
      order_amount, pay_amount, pay_time, 
      loan_shop_id, return_box_no, loan_price_info, 
      gmt_create, gmt_update, deleted, version, pay_success_time,biz_type
      )
    values (#{status,jdbcType=TINYINT}, #{voucherId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER},
      #{orderAmount,jdbcType=BIGINT}, #{payAmount,jdbcType=BIGINT}, #{payTime,jdbcType=TIMESTAMP}, 
      #{loanShopId,jdbcType=BIGINT}, #{returnBoxNo,jdbcType=VARCHAR}, #{loanPriceInfo,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{version,jdbcType=INTEGER},#{payTime,jdbcType=TIMESTAMP}, #{bizType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO" useGeneratedKeys="true">
    insert into bill_settle_order_original
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="loanShopId != null">
        loan_shop_id,
      </if>
      <if test="returnBoxNo != null">
        return_box_no,
      </if>
      <if test="loanPriceInfo != null">
        loan_price_info,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="paySuccessTime != null">
        pay_success_time,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loanShopId != null">
        #{loanShopId,jdbcType=BIGINT},
      </if>
      <if test="returnBoxNo != null">
        #{returnBoxNo,jdbcType=VARCHAR},
      </if>
      <if test="loanPriceInfo != null">
        #{loanPriceInfo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="paySuccessTime != null">
        #{paySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO">
    update bill_settle_order_original
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=BIGINT},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loanShopId != null">
        loan_shop_id = #{loanShopId,jdbcType=BIGINT},
      </if>
      <if test="returnBoxNo != null">
        return_box_no = #{returnBoxNo,jdbcType=VARCHAR},
      </if>
      <if test="loanPriceInfo != null">
        loan_price_info = #{loanPriceInfo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="paySuccessTime != null">
        pay_success_time = #{paySuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO">
    update bill_settle_order_original
    set `status` = #{status,jdbcType=TINYINT},
      voucher_id = #{voucherId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      pay_amount = #{payAmount,jdbcType=BIGINT},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      loan_shop_id = #{loanShopId,jdbcType=BIGINT},
      return_box_no = #{returnBoxNo,jdbcType=VARCHAR},
      loan_price_info = #{loanPriceInfo,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      pay_success_time = #{paySuccessTime,jdbcType=TIMESTAMP},
      biz_type = #{bizType,jdbcType=INTEGER},
      where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectIdByTimeRange" parameterType="so.dian.huashan.collection.mapper.example.TimeRangeExample" resultType="long">
    select id from bill_settle_order_original
    where deleted = 0
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    and status = 0
    and gmt_create <![CDATA[ >= ]]> #{timeRange.start, jdbcType=BIGINT}
    and gmt_create <![CDATA[ <= ]]> #{timeRange.end, jdbcType=BIGINT}
    order by id asc
    limit #{pageSize}
  </select>

  <select id="selectByIds" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_settle_order_original
    where deleted = 0
    and id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="orderOriginalDOS" item="orderOriginalDO" index="index" open="" close="" separator=";">
      UPDATE bill_settle_order_original
      SET
      `status` = #{orderOriginalDO.status,jdbcType=TINYINT},
      version = version + 1,
      gmt_update = #{orderOriginalDO.gmtUpdate,jdbcType=BIGINT}
      WHERE id = #{orderOriginalDO.id}
    </foreach>
  </update>

  <select id="selectMaxIdByGmtCreate" parameterType="long" resultType="long">
    select
    max(id)
    from bill_settle_order_original
    where
      gmt_create <![CDATA[ <= ]]> #{gmtCreate, jdbcType=BIGINT}
      and status in (1,3,4)
  </select>

  <delete id="deleteByGmtCreateAndMaxId" parameterType="map">
    delete from bill_settle_order_original
    where
      id <![CDATA[ <= ]]> #{maxId,jdbcType=BIGINT}
      and gmt_create <![CDATA[ <= ]]> #{gmtCreate, jdbcType=BIGINT}
      and status in (1,3,4) limit #{size,jdbcType=INTEGER}
  </delete>
</mapper>