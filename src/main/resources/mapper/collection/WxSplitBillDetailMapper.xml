<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.WxSplitBillDetailMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
    <result column="settle_detail_no" jdbcType="VARCHAR" property="settleDetailNo" />
    <result column="split_date" jdbcType="TIMESTAMP" property="splitDate" />
    <result column="split_initiator" jdbcType="VARCHAR" property="splitInitiator" />
    <result column="split_source_id" jdbcType="VARCHAR" property="splitSourceId" />
    <result column="split_receive_id" jdbcType="VARCHAR" property="splitReceiveId" />
    <result column="split_order_no" jdbcType="VARCHAR" property="splitOrderNo" />
    <result column="split_detail_no" jdbcType="VARCHAR" property="splitDetailNo" />
    <result column="mch_split_order_no" jdbcType="VARCHAR" property="mchSplitOrderNo" />
    <result column="order_amt" jdbcType="BIGINT" property="orderAmt" />
    <result column="split_amt" jdbcType="BIGINT" property="splitAmt" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, check_status, settle_detail_no, split_date, split_initiator, split_source_id, 
    split_receive_id, split_order_no, split_detail_no, mch_split_order_no, order_amt, 
    split_amt, biz_type, deleted, gmt_create, gmt_update
  </sql>
  <sql id="where">
    <if test="settleDetailNoList !=null and settleDetailNoList.size()>0">
      and settle_detail_no in
      <foreach collection="settleDetailNoList" index="index" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_split_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByParam" parameterType="so.dian.huashan.collection.mapper.param.SplitBillParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_split_bill_detail
    <where>
    <include refid="where"/>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_split_bill_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO" useGeneratedKeys="true">
    insert into wx_split_bill_detail (check_status, settle_detail_no, split_date, 
      split_initiator, split_source_id, split_receive_id, 
      split_order_no, split_detail_no, mch_split_order_no, 
      order_amt, split_amt, biz_type, 
      deleted, gmt_create, gmt_update
      )
    values (#{checkStatus,jdbcType=TINYINT}, #{settleDetailNo,jdbcType=VARCHAR}, #{splitDate,jdbcType=TIMESTAMP}, 
      #{splitInitiator,jdbcType=VARCHAR}, #{splitSourceId,jdbcType=VARCHAR}, #{splitReceiveId,jdbcType=VARCHAR}, 
      #{splitOrderNo,jdbcType=VARCHAR}, #{splitDetailNo,jdbcType=VARCHAR}, #{mchSplitOrderNo,jdbcType=VARCHAR}, 
      #{orderAmt,jdbcType=BIGINT}, #{splitAmt,jdbcType=BIGINT}, #{bizType,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}
      )
  </insert>

  <insert id="insertBatch" keyProperty="id">
    insert into wx_split_bill_detail (check_status, settle_detail_no, split_date,
                                      split_initiator, split_source_id, split_receive_id,
                                      split_order_no, split_detail_no, mch_split_order_no,
                                      order_amt, split_amt, biz_type,
                                      deleted, gmt_create, gmt_update
    )
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.checkStatus,jdbcType=TINYINT}, #{item.settleDetailNo,jdbcType=VARCHAR}, #{item.splitDate,jdbcType=TIMESTAMP},
            #{item.splitInitiator,jdbcType=VARCHAR}, #{item.splitSourceId,jdbcType=VARCHAR}, #{item.splitReceiveId,jdbcType=VARCHAR},
            #{item.splitOrderNo,jdbcType=VARCHAR}, #{item.splitDetailNo,jdbcType=VARCHAR}, #{item.mchSplitOrderNo,jdbcType=VARCHAR},
            #{item.orderAmt,jdbcType=BIGINT}, #{item.splitAmt,jdbcType=BIGINT}, #{item.bizType,jdbcType=VARCHAR},
            #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT}, #{item.gmtUpdate,jdbcType=BIGINT}
           )
    </foreach>
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO" useGeneratedKeys="true">
    insert into wx_split_bill_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="settleDetailNo != null">
        settle_detail_no,
      </if>
      <if test="splitDate != null">
        split_date,
      </if>
      <if test="splitInitiator != null">
        split_initiator,
      </if>
      <if test="splitSourceId != null">
        split_source_id,
      </if>
      <if test="splitReceiveId != null">
        split_receive_id,
      </if>
      <if test="splitOrderNo != null">
        split_order_no,
      </if>
      <if test="splitDetailNo != null">
        split_detail_no,
      </if>
      <if test="mchSplitOrderNo != null">
        mch_split_order_no,
      </if>
      <if test="orderAmt != null">
        order_amt,
      </if>
      <if test="splitAmt != null">
        split_amt,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="settleDetailNo != null">
        #{settleDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="splitDate != null">
        #{splitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="splitInitiator != null">
        #{splitInitiator,jdbcType=VARCHAR},
      </if>
      <if test="splitSourceId != null">
        #{splitSourceId,jdbcType=VARCHAR},
      </if>
      <if test="splitReceiveId != null">
        #{splitReceiveId,jdbcType=VARCHAR},
      </if>
      <if test="splitOrderNo != null">
        #{splitOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="splitDetailNo != null">
        #{splitDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="mchSplitOrderNo != null">
        #{mchSplitOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmt != null">
        #{orderAmt,jdbcType=BIGINT},
      </if>
      <if test="splitAmt != null">
        #{splitAmt,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO">
    update wx_split_bill_detail
    <set>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="settleDetailNo != null">
        settle_detail_no = #{settleDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="splitDate != null">
        split_date = #{splitDate,jdbcType=TIMESTAMP},
      </if>
      <if test="splitInitiator != null">
        split_initiator = #{splitInitiator,jdbcType=VARCHAR},
      </if>
      <if test="splitSourceId != null">
        split_source_id = #{splitSourceId,jdbcType=VARCHAR},
      </if>
      <if test="splitReceiveId != null">
        split_receive_id = #{splitReceiveId,jdbcType=VARCHAR},
      </if>
      <if test="splitOrderNo != null">
        split_order_no = #{splitOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="splitDetailNo != null">
        split_detail_no = #{splitDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="mchSplitOrderNo != null">
        mch_split_order_no = #{mchSplitOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmt != null">
        order_amt = #{orderAmt,jdbcType=BIGINT},
      </if>
      <if test="splitAmt != null">
        split_amt = #{splitAmt,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO">
    update wx_split_bill_detail
    set check_status = #{checkStatus,jdbcType=TINYINT},
      settle_detail_no = #{settleDetailNo,jdbcType=VARCHAR},
      split_date = #{splitDate,jdbcType=TIMESTAMP},
      split_initiator = #{splitInitiator,jdbcType=VARCHAR},
      split_source_id = #{splitSourceId,jdbcType=VARCHAR},
      split_receive_id = #{splitReceiveId,jdbcType=VARCHAR},
      split_order_no = #{splitOrderNo,jdbcType=VARCHAR},
      split_detail_no = #{splitDetailNo,jdbcType=VARCHAR},
      mch_split_order_no = #{mchSplitOrderNo,jdbcType=VARCHAR},
      order_amt = #{orderAmt,jdbcType=BIGINT},
      split_amt = #{splitAmt,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectBySettleDetailNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_split_bill_detail
    where deleted = 0
    <if test="settleDetailNos != null">
      and settle_detail_no in
      <foreach collection="settleDetailNos" item="settleDetailNo" open="(" close=")" separator=",">
        #{settleDetailNo}
      </foreach>
    </if>
  </select>
  <update id="batchUpdateStatusByIds" parameterType="list">
    update wx_split_bill_detail set check_status = #{status,jdbcType=INTEGER},gmt_update = UNIX_TIMESTAMP() * 1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByCheckDateTime" parameterType="map" resultType="long">
    select
    id
    from wx_split_bill_detail
    where deleted = 0 and check_status in(0)
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="startTime != null">
      and gmt_update <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
    </if>
    <if test="endTime != null">
      and gmt_update <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
    </if>
    order by id asc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from wx_split_bill_detail
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>