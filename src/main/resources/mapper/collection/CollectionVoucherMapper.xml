<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.CollectionVoucherMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.CollectionVoucherDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="retry_time" jdbcType="INTEGER" property="retryTime" />
    <result column="voucher" jdbcType="VARCHAR" property="voucher" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, source, `status`, retry_time, voucher, receive_time, remark,
    deleted, gmt_create, gmt_update, version
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from collection_voucher
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from collection_voucher
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.CollectionVoucherDO" useGeneratedKeys="true">
    insert into collection_voucher ( source, `status`, retry_time,
      voucher, receive_time, remark, 
      deleted, gmt_create, gmt_update, version
      )
    values ( #{source,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{retryTime,jdbcType=INTEGER},
      #{voucher,jdbcType=VARCHAR}, #{receiveTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.CollectionVoucherDO" useGeneratedKeys="true">
    insert into collection_voucher
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="source != null">
        `source`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="retryTime != null">
        retry_time,
      </if>
      <if test="voucher != null">
        voucher,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="retryTime != null">
        #{retryTime,jdbcType=INTEGER},
      </if>
      <if test="voucher != null">
        #{voucher,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.CollectionVoucherDO">
    update collection_voucher
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="retryTime != null">
        retry_time = #{retryTime,jdbcType=INTEGER},
      </if>
      <if test="voucher != null">
        voucher = #{voucher,jdbcType=VARCHAR},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByExample" parameterType="so.dian.huashan.collection.mapper.example.CollectionVoucherExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from collection_voucher
    where deleted = 0 and gmt_create <![CDATA[ <= ]]> #{gmtCreate}
    <if test="gtId != null">
      and id <![CDATA[ > ]]> #{gtId,jdbcType=BIGINT}
    </if>
    <if test="statusList != null">
      and `status` in
      <foreach collection="statusList" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
    <if test="leRetryTime != null">
      and retry_time <![CDATA[ <= ]]> #{leRetryTime,jdbcType=INTEGER}
    </if>
    order by id ASC
    <if test="page != null">
      limit #{page.offset},#{page.pageSize}
    </if>
  </select>

  <select id="selectForRetry" parameterType="so.dian.huashan.collection.mapper.example.CollectionVoucherExample" resultMap="BaseResultMap">
    select
    /*+INDEX(collection_voucher status_index) */
    <include refid="Base_Column_List" />
    from collection_voucher
    where deleted = 0 and gmt_create <![CDATA[ <= ]]> #{gmtCreate}
    <if test="gtId != null">
      and id <![CDATA[ > ]]> #{gtId,jdbcType=BIGINT}
    </if>
    <if test="statusList != null">
      and `status` in
      <foreach collection="statusList" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
    <if test="leRetryTime != null">
      and retry_time <![CDATA[ <= ]]> #{leRetryTime,jdbcType=INTEGER}
    </if>
    order by id ASC
    <if test="page != null">
      limit #{page.offset},#{page.pageSize}
    </if>
  </select>

  <update id="retryUpdateByIdList">
    update collection_voucher
    set
    status = 4,
    receive_time = receive_time + 1,
    gmt_update = MID(UNIX_TIMESTAMP(NOW()),1,10)*1000
    where
    id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
    and deleted = 0
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into collection_voucher
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.source,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.retryTime,jdbcType=INTEGER},
        #{item.voucher,jdbcType=VARCHAR},
        #{item.receiveTime,jdbcType=TIMESTAMP},
        #{item.remark,jdbcType=VARCHAR},
        #{item.deleted,jdbcType=TINYINT},
        #{item.gmtCreate,jdbcType=BIGINT},
        #{item.gmtUpdate,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>

  <update id="updateStatus" parameterType="map">
    update collection_voucher
    set
        status = #{status,jdbcType=TINYINT},
        version = version + 1,
        gmt_update = UNIX_TIMESTAMP()*1000
    where id = #{id,jdbcType=BIGINT} and version = #{version,jdbcType=INTEGER}
  </update>
</mapper>