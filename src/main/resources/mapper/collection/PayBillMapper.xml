<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.PayBillMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.PayBillDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="voucher_id" jdbcType="BIGINT" property="voucherId" />
    <result column="pay_subject_id" jdbcType="BIGINT" property="paySubjectId" />
    <result column="pay_subject_type" jdbcType="TINYINT" property="paySubjectType" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo" />
    <result column="trade_amount" jdbcType="BIGINT" property="tradeAmount" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, voucher_id, pay_subject_id, pay_subject_type,trade_type, `source`,
    trade_no, channel_trade_no, trade_amount, trade_time, `status`, deleted, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pay_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pay_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.PayBillDO" useGeneratedKeys="true">
    insert into pay_bill (voucher_id, pay_subject_id, pay_subject_type, 
      pay_subject_name, trade_type, `source`, 
      trade_no, channel_trade_no, trade_amount, 
      trade_time, `status`, deleted, 
      gmt_create, gmt_update)
    values (#{voucherId,jdbcType=BIGINT}, #{paySubjectId,jdbcType=BIGINT}, #{paySubjectType,jdbcType=TINYINT}, 
       #{tradeType,jdbcType=VARCHAR}, #{source,jdbcType=TINYINT},
      #{tradeNo,jdbcType=VARCHAR}, #{channelTradeNo,jdbcType=VARCHAR}, #{tradeAmount,jdbcType=BIGINT}, 
      #{tradeTime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.PayBillDO" useGeneratedKeys="true">
    insert into pay_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="paySubjectId != null">
        pay_subject_id,
      </if>
      <if test="paySubjectType != null">
        pay_subject_type,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="voucherId != null">
        #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectId != null">
        #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        #{paySubjectType,jdbcType=TINYINT},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=BIGINT},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.PayBillDO">
    update pay_bill
    <set>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectId != null">
        pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=TINYINT},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=BIGINT},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.PayBillDO">
    update pay_bill
    set voucher_id = #{voucherId,jdbcType=BIGINT},
      pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=TINYINT},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      trade_amount = #{tradeAmount,jdbcType=BIGINT},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCheckDateTime" parameterType="map" resultType="long">
    select
    id
    from pay_bill
    where deleted = 0 and `status` &amp; 15 in(0,2)
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="startTime != null">
      and gmt_update <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
    </if>
    <if test="endTime != null">
      and gmt_update <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
    </if>
    order by id asc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
  <select id="getOne" parameterType="so.dian.huashan.collection.mapper.param.PayBillParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from pay_bill
    <where>
      deleted = 0
      <if test="tradeNo != null">
        and trade_no = #{tradeNo,jdbcType=VARCHAR}
      </if>
      <if test="tradeType != null">
      and trade_type = #{tradeType,jdbcType=VARCHAR}
      </if>
    </where>
    limit 1
  </select>
  <update id="batchUpdateStatusByIds" parameterType="list">
    update pay_bill set status = (status &amp;1048560) + #{status,jdbcType=INTEGER},gmt_update = UNIX_TIMESTAMP() * 1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByTradeNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill
    where deleted = 0
    <if test="tradeNos != null">
      and channel_trade_no in
      <foreach collection="tradeNos" item="tradeNo" open="(" close=")" separator=",">
        #{tradeNo}
      </foreach>
    </if>
  </select>
</mapper>