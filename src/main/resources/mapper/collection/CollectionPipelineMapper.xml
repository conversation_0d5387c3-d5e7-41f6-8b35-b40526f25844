<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.CollectionPipelineMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.CollectionPipelineDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_domain_id" jdbcType="BIGINT" property="bizDomainId" />
    <result column="strategy_code" jdbcType="VARCHAR" property="strategyCode" />
    <result column="collection_way" jdbcType="TINYINT" property="collectionWay" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_domain_id, strategy_code, collection_way, table_name, gmt_create, gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from collection_pipeline
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from collection_pipeline
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.CollectionPipelineDO" useGeneratedKeys="true">
    insert into collection_pipeline (biz_domain_id, strategy_code, collection_way, table_name,
      gmt_create, gmt_update, deleted
      )
    values (#{bizDomainId,jdbcType=BIGINT}, #{strategyCode,jdbcType=VARCHAR}, #{collectionWay,jdbcType=TINYINT},
            #{tableName,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.CollectionPipelineDO" useGeneratedKeys="true">
    insert into collection_pipeline
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizDomainId != null">
        biz_domain_id,
      </if>
      <if test="strategyCode != null">
        strategy_code,
      </if>
      <if test="collectionWay != null">
        collection_way,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizDomainId != null">
        #{bizDomainId,jdbcType=BIGINT},
      </if>
      <if test="strategyCode != null">
        #{strategyCode,jdbcType=VARCHAR},
      </if>
      <if test="collectionWay != null">
        #{collectionWay,jdbcType=TINYINT},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.CollectionPipelineDO">
    update collection_pipeline
    <set>
      <if test="bizDomainId != null">
        biz_domain_id = #{bizDomainId,jdbcType=BIGINT},
      </if>
      <if test="strategyCode != null">
        strategy_code = #{strategyCode,jdbcType=VARCHAR},
      </if>
      <if test="collectionWay != null">
        collection_way = #{collectionWay,jdbcType=TINYINT},
      </if>
      <if test="tableName != null">
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.CollectionPipelineDO">
    update collection_pipeline
    set biz_domain_id = #{bizDomainId,jdbcType=BIGINT},
      strategy_code = #{strategyCode,jdbcType=VARCHAR},
      collection_way = #{collectionWay,jdbcType=TINYINT},
      table_name = #{tableName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectAllRealTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from collection_pipeline
    where collection_way = 1 and deleted = 0
  </select>

  <select id="selectByTableName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from collection_pipeline
    where collection_way = 1 and deleted = 0 and table_name = #{tableName,jdbcType=VARCHAR}
  </select>

  <select id="selectByDomainId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from collection_pipeline
    where collection_way = 1 and deleted = 0 and biz_domain_id = #{domainId,jdbcType=BIGINT}
  </select>
</mapper>