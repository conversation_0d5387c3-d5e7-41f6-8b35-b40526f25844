<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.WxFundAcctInfoMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pt" jdbcType="INTEGER" property="pt" />
    <result column="mch_type" jdbcType="TINYINT" property="mchType" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="wx_acct_type" jdbcType="VARCHAR" property="wxAcctType" />
    <result column="available_amt" jdbcType="BIGINT" property="availableAmt" />
    <result column="pending_amt" jdbcType="BIGINT" property="pendingAmt" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pt, mch_type, mch_id, wx_acct_type, available_amt, pending_amt, deleted, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from wx_fund_acct_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from wx_fund_acct_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO" useGeneratedKeys="true">
    insert into wx_fund_acct_info (pt, mch_type, mch_id, 
      wx_acct_type, available_amt, pending_amt, 
      deleted, gmt_create, gmt_update
      )
    values (#{pt,jdbcType=INTEGER}, #{mchType,jdbcType=TINYINT}, #{mchId,jdbcType=VARCHAR}, 
      #{wxAcctType,jdbcType=VARCHAR}, #{availableAmt,jdbcType=BIGINT}, #{pendingAmt,jdbcType=BIGINT}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertBatch"  keyProperty="id">
    insert into wx_fund_acct_info (pt, mch_type, mch_id,
                                   wx_acct_type, available_amt, pending_amt,
                                   deleted, gmt_create, gmt_update
    )
    values
    <foreach collection="list" item="item" separator=",">
        (#{item.pt,jdbcType=INTEGER}, #{item.mchType,jdbcType=TINYINT}, #{item.mchId,jdbcType=VARCHAR},
            #{item.wxAcctType,jdbcType=VARCHAR}, #{item.availableAmt,jdbcType=BIGINT}, #{item.pendingAmt,jdbcType=BIGINT},
            #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT}, #{item.gmtUpdate,jdbcType=BIGINT}
           )
    </foreach>
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO" useGeneratedKeys="true">
    insert into wx_fund_acct_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pt != null">
        pt,
      </if>
      <if test="mchType != null">
        mch_type,
      </if>
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="wxAcctType != null">
        wx_acct_type,
      </if>
      <if test="availableAmt != null">
        available_amt,
      </if>
      <if test="pendingAmt != null">
        pending_amt,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pt != null">
        #{pt,jdbcType=INTEGER},
      </if>
      <if test="mchType != null">
        #{mchType,jdbcType=TINYINT},
      </if>
      <if test="mchId != null">
        #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="wxAcctType != null">
        #{wxAcctType,jdbcType=VARCHAR},
      </if>
      <if test="availableAmt != null">
        #{availableAmt,jdbcType=BIGINT},
      </if>
      <if test="pendingAmt != null">
        #{pendingAmt,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateBatch" parameterType="java.util.List">
  <foreach collection="list" item="item" separator=";">
    update wx_fund_acct_info
    <set>
      <if test="item.pt != null">
        pt = #{item.pt,jdbcType=INTEGER},
      </if>
      <if test="item.mchType != null">
        mch_type = #{item.mchType,jdbcType=TINYINT},
      </if>
      <if test="item.mchId != null">
        mch_id = #{item.mchId,jdbcType=VARCHAR},
      </if>
      <if test="item.wxAcctType != null">
        wx_acct_type = #{item.wxAcctType,jdbcType=VARCHAR},
      </if>
      <if test="item.availableAmt != null">
        available_amt = #{item.availableAmt,jdbcType=BIGINT},
      </if>
      <if test="item.pendingAmt != null">
        pending_amt = #{item.pendingAmt,jdbcType=BIGINT},
      </if>
      <if test="item.deleted != null">
        deleted = #{item.deleted,jdbcType=TINYINT},
      </if>
      <if test="item.gmtCreate != null">
        gmt_create = #{item.gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="item.gmtUpdate != null">
        gmt_update = #{item.gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{item.id,jdbcType=BIGINT}
  </foreach>
  </update>
  <select id="selectByParam" parameterType="so.dian.huashan.collection.mapper.param.WxFundAcctInfoParam" resultType="so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO">
    select
    <include refid="Base_Column_List" />
    from wx_fund_acct_info
    <where>
      deleted = 0
      <if test="pt != null">
        and pt = #{pt,jdbcType=INTEGER}
      </if>
      <if test="mchType != null">
        and mch_type = #{mchType,jdbcType=TINYINT}
      </if>
      <if test="mchId != null">
        and mch_id= #{mchId,jdbcType=VARCHAR}
      </if>
      <if test="merchantIdList != null and merchantIdList.size()>0">
        and mch_id in
        <foreach collection="merchantIdList" item="merchantId" open="(" close=")" separator=",">
          #{merchantId}
        </foreach>
      </if>

    </where>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO">
    update wx_fund_acct_info
    <set>
      <if test="pt != null">
        pt = #{pt,jdbcType=INTEGER},
      </if>
      <if test="mchType != null">
        mch_type = #{mchType,jdbcType=TINYINT},
      </if>
      <if test="mchId != null">
        mch_id = #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="wxAcctType != null">
        wx_acct_type = #{wxAcctType,jdbcType=VARCHAR},
      </if>
      <if test="availableAmt != null">
        available_amt = #{availableAmt,jdbcType=BIGINT},
      </if>
      <if test="pendingAmt != null">
        pending_amt = #{pendingAmt,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO">
    update wx_fund_acct_info
    set pt = #{pt,jdbcType=INTEGER},
      mch_type = #{mchType,jdbcType=TINYINT},
      mch_id = #{mchId,jdbcType=VARCHAR},
      wx_acct_type = #{wxAcctType,jdbcType=VARCHAR},
      available_amt = #{availableAmt,jdbcType=BIGINT},
      pending_amt = #{pendingAmt,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>