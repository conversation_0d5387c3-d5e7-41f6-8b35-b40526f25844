<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.ThirdPartyBillMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_id" jdbcType="BIGINT" property="fileId" />
    <result column="check_date" jdbcType="INTEGER" property="checkDate" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="bill_time" jdbcType="TIMESTAMP" property="billTime" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="trade_amount" jdbcType="INTEGER" property="tradeAmount" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="bill_source" jdbcType="TINYINT" property="billSource" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="biz_no_1" jdbcType="VARCHAR" property="bizNo1" />
    <result column="biz_no_2" jdbcType="VARCHAR" property="bizNo2" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, file_id, check_date, trade_type, bill_time, pay_no, trade_amount, biz_type,
    bill_source, remark, `status`, biz_no_1, biz_no_2, mch_id, deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from third_party_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from third_party_bill
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
  <select id="selectByCheckDate" resultType="java.lang.Long">
    select
    id
    from third_party_bill
    where check_date = #{checkDate,jdbcType=INTEGER} and deleted = 0 and
          CASE bill_source
    WHEN 1 THEN
    biz_type not in(3,4,8)
    ELSE
    biz_type != 3
    end
    <if test="startDateTime != null">
      and gmt_update >= #{startDateTime}
    </if>
    <if test="endDateTime != null">
      and gmt_update <![CDATA[ <= ]]> #{endDateTime}
    </if>
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
    order by id desc
  </select>

  <select id="selectIdForShard" parameterType="map" resultType="long">
    select
      id
    from third_party_bill
    where check_date = #{checkDate,jdbcType=INTEGER}
      <if test="maxId != null">
        and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
      </if>
      and deleted = 0 and
      CASE bill_source
        WHEN 1 THEN
          biz_type not in(3,4,8)
        ELSE
          biz_type != 3
        end
    order by id asc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from third_party_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO" useGeneratedKeys="true">
    insert into third_party_bill (file_id, check_date, trade_type, 
      bill_time, pay_no, trade_amount,
      biz_type, bill_source, remark, 
      `status`, biz_no_1, biz_no_2, mch_id, deleted, gmt_create,
      gmt_update)
    values (#{fileId,jdbcType=BIGINT}, #{checkDate,jdbcType=INTEGER}, #{tradeType,jdbcType=VARCHAR}, 
      #{billTime,jdbcType=TIMESTAMP}, #{payNo,jdbcType=VARCHAR}, #{tradeAmount,jdbcType=INTEGER},
      #{bizType,jdbcType=TINYINT}, #{billSource,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{bizNo1,jdbcType=VARCHAR}, #{bizNo2,jdbcType=VARCHAR},
      #{mchId,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT},
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO" useGeneratedKeys="true">
    insert into third_party_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        file_id,
      </if>
      <if test="checkDate != null">
        check_date,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="billTime != null">
        bill_time,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="billSource != null">
        bill_source,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="bizNo1 != null">
        biz_no_1,
      </if>
      <if test="biz_no_2 != null">
        biz_no_2,
      </if>
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileId != null">
        #{fileId,jdbcType=BIGINT},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=INTEGER},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="billTime != null">
        #{billTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="billSource != null">
        #{billSource,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="bizNo1 != null">
        #{bizNo1,jdbcType=VARCHAR},
      </if>
      <if test="bizNo2 != null">
        #{bizNo2,jdbcType=VARCHAR},
      </if>
      <if test="mchId != null">
        #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" keyProperty="id">
    insert into third_party_bill (file_id, check_date, trade_type,
    bill_time, pay_no, trade_amount,
    biz_type, bill_source, remark,
    `status`, biz_no_1, biz_no_2, mch_id, deleted, gmt_create,
    gmt_update
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.fileId,jdbcType=BIGINT}, #{item.checkDate,jdbcType=INTEGER}, #{item.tradeType,jdbcType=VARCHAR},
      #{item.billTime,jdbcType=TIMESTAMP}, #{item.payNo,jdbcType=VARCHAR}, #{item.tradeAmount,jdbcType=INTEGER},
      #{item.bizType,jdbcType=TINYINT}, #{item.billSource,jdbcType=TINYINT}, #{item.remark,jdbcType=VARCHAR},
      #{item.status,jdbcType=TINYINT}, #{item.bizNo1,jdbcType=VARCHAR}, #{item.bizNo2,jdbcType=VARCHAR},
      #{item.mchId,jdbcType=VARCHAR}, #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT},
      #{item.gmtUpdate,jdbcType=BIGINT})
    </foreach>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO">
    update third_party_bill
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=BIGINT},
      </if>
      <if test="checkDate != null">
        check_date = #{checkDate,jdbcType=INTEGER},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="billTime != null">
        bill_time = #{billTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="billSource != null">
        bill_source = #{billSource,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="bizNo1 != null">
        biz_no_1 = #{bizNo1,jdbcType=VARCHAR},
      </if>
      <if test="bizNo2 != null">
        biz_no_2 = #{bizNo2,jdbcType=VARCHAR},
      </if>
      <if test="mchId != null">
        mch_id = #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO">
    update third_party_bill
    set file_id = #{fileId,jdbcType=BIGINT},
      check_date = #{checkDate,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=VARCHAR},
      bill_time = #{billTime,jdbcType=TIMESTAMP},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      trade_amount = #{tradeAmount,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=TINYINT},
      bill_source = #{billSource,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      biz_no_1 = #{bizNo1,jdbcType=VARCHAR},
      biz_no_2 = #{bizNo2,jdbcType=VARCHAR},
      mch_id = #{mchId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>