<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="idempotent_no" jdbcType="VARCHAR" property="idempotentNo" />
    <result column="voucher_id" jdbcType="BIGINT" property="voucherId" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="data_source" jdbcType="TINYINT" property="dataSource" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="pay_way" jdbcType="TINYINT" property="payWay" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="trade_amount" jdbcType="INTEGER" property="tradeAmount" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="happen_date" jdbcType="INTEGER" property="happenDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="dian_pay_no" jdbcType="VARCHAR" property="dianPayNo" />
    <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, idempotent_no, voucher_id, trade_type,data_source, biz_type, pay_way, pay_no, trade_amount,
    trade_time, happen_date, `status`, deleted, gmt_create, gmt_update, trade_no, dian_pay_no, channel_trade_no, pay_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from income_original_info
    where id = #{id,jdbcType=BIGINT} and deleted = 0
  </select>
  <select id="selectByIdempotentNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from income_original_info
    where idempotent_no = #{idempotentNo} and deleted = 0
  </select>
  <select id="selectBatchByPayNos" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from income_original_info
    where deleted = 0
    <if test="payNos != null">
      and pay_no in
      <foreach collection="payNos" item="payNo" open="(" close=")" separator=",">
        #{payNo}
      </foreach>
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from income_original_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO" useGeneratedKeys="true">
    insert into income_original_info (idempotent_no, voucher_id, trade_type,data_source,
      biz_type, pay_way, pay_no, 
      trade_amount, trade_time, happen_date, 
      `status`, deleted, gmt_create, 
      gmt_update,trade_no,dian_pay_no,channel_trade_no, pay_type)
    values (#{idempotentNo,jdbcType=VARCHAR}, #{voucherId,jdbcType=BIGINT}, #{tradeType,jdbcType=VARCHAR}, #{dataSource,jdbcType=TINYINT},
      #{bizType,jdbcType=TINYINT}, #{payWay,jdbcType=TINYINT}, #{payNo,jdbcType=VARCHAR}, 
      #{tradeAmount,jdbcType=INTEGER}, #{tradeTime,jdbcType=TIMESTAMP}, #{happenDate,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT},#{tradeNo,jdbcType=VARCHAR},#{dianPayNo,jdbcType=VARCHAR},#{channelTradeNo,jdbcType=VARCHAR},#{payType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO" useGeneratedKeys="true">
    insert into income_original_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="idempotentNo != null">
        idempotent_no,
      </if>
      <if test="voucherId != null">
        voucher_id,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="dataSource != null">
        data_source,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="payWay != null">
        pay_way,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="happenDate != null">
        happen_date,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="dianPayNo != null">
        dian_pay_no,
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="idempotentNo != null">
        #{idempotentNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        #{dataSource,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=TINYINT},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=INTEGER},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="happenDate != null">
        #{happenDate,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="dianPayNo != null">
        #{dianPayNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO">
    update income_original_info
    <set>
      <if test="idempotentNo != null">
        idempotent_no = #{idempotentNo,jdbcType=VARCHAR},
      </if>
      <if test="voucherId != null">
        voucher_id = #{voucherId,jdbcType=BIGINT},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="dataSource != null">
        data_source = #{dataSource,jdbcType=TINYINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="payWay != null">
        pay_way = #{payWay,jdbcType=TINYINT},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=INTEGER},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="happenDate != null">
        happen_date = #{happenDate,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="dianPayNo != null">
        dian_pay_no = #{dianPayNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO">
    update income_original_info
    set idempotent_no = #{idempotentNo,jdbcType=VARCHAR},
      voucher_id = #{voucherId,jdbcType=BIGINT},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      data_source = #{dataSource,jdbcType=TINYINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      pay_way = #{payWay,jdbcType=TINYINT},
      pay_no = #{payNo,jdbcType=VARCHAR},
      trade_amount = #{tradeAmount,jdbcType=INTEGER},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      happen_date = #{happenDate,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      dian_pay_no = #{dianPayNo,jdbcType=VARCHAR},
      channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>