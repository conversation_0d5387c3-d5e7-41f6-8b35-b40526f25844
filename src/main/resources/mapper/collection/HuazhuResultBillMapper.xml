<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.HuazhuResultBillMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="income_original_id" jdbcType="BIGINT" property="incomeOriginalId" />
    <result column="bill_date" jdbcType="INTEGER" property="billDate" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="dian_trade_no" jdbcType="VARCHAR" property="dianTradeNo" />
    <result column="biz_order_no" jdbcType="VARCHAR" property="bizOrderNo" />
    <result column="pay_no" jdbcType="VARCHAR" property="payNo" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="spu_name" jdbcType="VARCHAR" property="spuName" />
    <result column="product_category" jdbcType="INTEGER" property="productCategory" />
    <result column="product_category_desc" jdbcType="VARCHAR" property="productCategoryDesc" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="order_stop_charg_time" jdbcType="TIMESTAMP" property="orderStopChargTime" />
    <result column="loan_shop_company" jdbcType="VARCHAR" property="loanShopCompany" />
    <result column="loan_shop_channel" jdbcType="VARCHAR" property="loanShopChannel" />
    <result column="is_capped_price" jdbcType="TINYINT" property="isCappedPrice" />
    <result column="capped_price" jdbcType="VARCHAR" property="cappedPrice" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, income_original_id, bill_date, trade_no, dian_trade_no, biz_order_no, pay_no, 
    pay_type, amount, currency, trade_time, spu_code, spu_name, product_category, product_category_desc, 
    order_amount, device_no, trade_type, order_stop_charg_time, loan_shop_company, loan_shop_channel, 
    is_capped_price, capped_price, gmt_create, gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from huazhu_result_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from huazhu_result_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO" useGeneratedKeys="true">
    insert into huazhu_result_bill (income_original_id, bill_date, trade_no, 
      dian_trade_no, biz_order_no, pay_no, 
      pay_type, amount, currency, 
      trade_time, spu_code, spu_name, 
      product_category, product_category_desc, order_amount, 
      device_no, trade_type, order_stop_charg_time, 
      loan_shop_company, loan_shop_channel, is_capped_price, 
      capped_price, gmt_create, gmt_update, 
      deleted)
    values (#{incomeOriginalId,jdbcType=BIGINT}, #{billDate,jdbcType=INTEGER}, #{tradeNo,jdbcType=VARCHAR}, 
      #{dianTradeNo,jdbcType=VARCHAR}, #{bizOrderNo,jdbcType=VARCHAR}, #{payNo,jdbcType=VARCHAR}, 
      #{payType,jdbcType=INTEGER}, #{amount,jdbcType=BIGINT}, #{currency,jdbcType=VARCHAR}, 
      #{tradeTime,jdbcType=TIMESTAMP}, #{spuCode,jdbcType=VARCHAR}, #{spuName,jdbcType=VARCHAR}, 
      #{productCategory,jdbcType=INTEGER}, #{productCategoryDesc,jdbcType=VARCHAR}, #{orderAmount,jdbcType=BIGINT}, 
      #{deviceNo,jdbcType=VARCHAR}, #{tradeType,jdbcType=VARCHAR}, #{orderStopChargTime,jdbcType=TIMESTAMP}, 
      #{loanShopCompany,jdbcType=VARCHAR}, #{loanShopChannel,jdbcType=VARCHAR}, #{isCappedPrice,jdbcType=TINYINT}, 
      #{cappedPrice,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, 
      #{deleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO" useGeneratedKeys="true">
    insert into huazhu_result_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="incomeOriginalId != null">
        income_original_id,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="dianTradeNo != null">
        dian_trade_no,
      </if>
      <if test="bizOrderNo != null">
        biz_order_no,
      </if>
      <if test="payNo != null">
        pay_no,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="spuName != null">
        spu_name,
      </if>
      <if test="productCategory != null">
        product_category,
      </if>
      <if test="productCategoryDesc != null">
        product_category_desc,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="deviceNo != null">
        device_no,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="orderStopChargTime != null">
        order_stop_charg_time,
      </if>
      <if test="loanShopCompany != null">
        loan_shop_company,
      </if>
      <if test="loanShopChannel != null">
        loan_shop_channel,
      </if>
      <if test="isCappedPrice != null">
        is_capped_price,
      </if>
      <if test="cappedPrice != null">
        capped_price,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="incomeOriginalId != null">
        #{incomeOriginalId,jdbcType=BIGINT},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=INTEGER},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="dianTradeNo != null">
        #{dianTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="bizOrderNo != null">
        #{bizOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="payNo != null">
        #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="productCategory != null">
        #{productCategory,jdbcType=INTEGER},
      </if>
      <if test="productCategoryDesc != null">
        #{productCategoryDesc,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="deviceNo != null">
        #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="orderStopChargTime != null">
        #{orderStopChargTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loanShopCompany != null">
        #{loanShopCompany,jdbcType=VARCHAR},
      </if>
      <if test="loanShopChannel != null">
        #{loanShopChannel,jdbcType=VARCHAR},
      </if>
      <if test="isCappedPrice != null">
        #{isCappedPrice,jdbcType=TINYINT},
      </if>
      <if test="cappedPrice != null">
        #{cappedPrice,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO">
    update huazhu_result_bill
    <set>
      <if test="incomeOriginalId != null">
        income_original_id = #{incomeOriginalId,jdbcType=BIGINT},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=INTEGER},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="dianTradeNo != null">
        dian_trade_no = #{dianTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="bizOrderNo != null">
        biz_order_no = #{bizOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="payNo != null">
        pay_no = #{payNo,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        spu_name = #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="productCategory != null">
        product_category = #{productCategory,jdbcType=INTEGER},
      </if>
      <if test="productCategoryDesc != null">
        product_category_desc = #{productCategoryDesc,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="deviceNo != null">
        device_no = #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="orderStopChargTime != null">
        order_stop_charg_time = #{orderStopChargTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loanShopCompany != null">
        loan_shop_company = #{loanShopCompany,jdbcType=VARCHAR},
      </if>
      <if test="loanShopChannel != null">
        loan_shop_channel = #{loanShopChannel,jdbcType=VARCHAR},
      </if>
      <if test="isCappedPrice != null">
        is_capped_price = #{isCappedPrice,jdbcType=TINYINT},
      </if>
      <if test="cappedPrice != null">
        capped_price = #{cappedPrice,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO">
    update huazhu_result_bill
    set income_original_id = #{incomeOriginalId,jdbcType=BIGINT},
      bill_date = #{billDate,jdbcType=INTEGER},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      dian_trade_no = #{dianTradeNo,jdbcType=VARCHAR},
      biz_order_no = #{bizOrderNo,jdbcType=VARCHAR},
      pay_no = #{payNo,jdbcType=VARCHAR},
      pay_type = #{payType,jdbcType=INTEGER},
      amount = #{amount,jdbcType=BIGINT},
      currency = #{currency,jdbcType=VARCHAR},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      spu_name = #{spuName,jdbcType=VARCHAR},
      product_category = #{productCategory,jdbcType=INTEGER},
      product_category_desc = #{productCategoryDesc,jdbcType=VARCHAR},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      device_no = #{deviceNo,jdbcType=VARCHAR},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      order_stop_charg_time = #{orderStopChargTime,jdbcType=TIMESTAMP},
      loan_shop_company = #{loanShopCompany,jdbcType=VARCHAR},
      loan_shop_channel = #{loanShopChannel,jdbcType=VARCHAR},
      is_capped_price = #{isCappedPrice,jdbcType=TINYINT},
      capped_price = #{cappedPrice,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into huazhu_result_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.incomeOriginalId,jdbcType=BIGINT},
        #{item.billDate,jdbcType=INTEGER},
        #{item.tradeNo,jdbcType=VARCHAR},
        #{item.dianTradeNo,jdbcType=VARCHAR},
        #{item.bizOrderNo,jdbcType=VARCHAR},
        #{item.payNo,jdbcType=VARCHAR},
        #{item.payType,jdbcType=INTEGER},
        #{item.amount,jdbcType=BIGINT},
        #{item.currency,jdbcType=VARCHAR},
        #{item.tradeTime,jdbcType=TIMESTAMP},
        #{item.spuCode,jdbcType=VARCHAR},
        #{item.spuName,jdbcType=VARCHAR},
        #{item.productCategory,jdbcType=INTEGER},
        #{item.productCategoryDesc,jdbcType=VARCHAR},
        #{item.orderAmount,jdbcType=BIGINT},
        #{item.deviceNo,jdbcType=VARCHAR},
        #{item.tradeType,jdbcType=VARCHAR},
        #{item.orderStopChargTime,jdbcType=TIMESTAMP},
        #{item.loanShopCompany,jdbcType=VARCHAR},
        #{item.loanShopChannel,jdbcType=VARCHAR},
        #{item.isCappedPrice,jdbcType=TINYINT},
        #{item.cappedPrice,jdbcType=VARCHAR},
        #{item.gmtCreate,jdbcType=BIGINT},
        #{item.gmtUpdate,jdbcType=BIGINT},
        #{item.deleted,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>
</mapper>