<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.HuazhuIncomeOriginalInfoMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="income_original_id" jdbcType="BIGINT" property="incomeOriginalId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, income_original_id, order_no, `status`, gmt_create, gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from huazhu_income_original_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from huazhu_income_original_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO" useGeneratedKeys="true">
    insert into huazhu_income_original_info (income_original_id, order_no, `status`, 
      gmt_create, gmt_update, deleted
      )
    values (#{incomeOriginalId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO" useGeneratedKeys="true">
    insert into huazhu_income_original_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="incomeOriginalId != null">
        income_original_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="incomeOriginalId != null">
        #{incomeOriginalId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO">
    update huazhu_income_original_info
    <set>
      <if test="incomeOriginalId != null">
        income_original_id = #{incomeOriginalId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO">
    update huazhu_income_original_info
    set income_original_id = #{incomeOriginalId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByIncomeOriginalId" parameterType="long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from huazhu_income_original_info
    where income_original_id = #{incomeOriginalId,jdbcType=BIGINT} and deleted = 0
  </select>

  <select id="selectForBurstJob" parameterType="date" resultType="long">
    select hz.id from
        huazhu_income_original_info hz
    LEFT JOIN
        income_original_info ioi
    on hz.income_original_id = ioi.id
    where trade_time <![CDATA[ < ]]> #{tradeTime,jdbcType=TIMESTAMP}
      and hz.status = 0 and hz.deleted = 0 and ioi.deleted = 0
    order by hz.id
  </select>

  <select id="selectIdForShard" parameterType="map" resultType="long">
    select hz.id from
      huazhu_income_original_info hz
        LEFT JOIN
      income_original_info ioi
      on hz.income_original_id = ioi.id
    where trade_time <![CDATA[ < ]]> #{tradeTime,jdbcType=TIMESTAMP}
      and trade_time <![CDATA[ >= ]]> '2024-12-31 00:00:00'
      and hz.status = 0 and hz.deleted = 0 and ioi.deleted = 0
      <if test="maxId != null">
        and hz.id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
      </if>
    order by hz.id asc
  </select>
  
  <select id="selectOriginalByIds" parameterType="list" resultType="so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoExtDO">
    select
        hz.id,
        hz.income_original_id,
        hz.order_no,
        hz.status,
        ioi.trade_type,
        ioi.pay_type,
        ioi.pay_no,
        ioi.trade_amount,
        ioi.trade_time,
        ioi.dian_pay_no
    from huazhu_income_original_info hz
        LEFT JOIN
      income_original_info ioi
      on hz.income_original_id = ioi.id
    where hz.id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

  <update id="batchUpdateStatus" parameterType="list">
    update huazhu_income_original_info set status = #{status,jdbcType=TINYINT}, gmt_update = UNIX_TIMESTAMP()*1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>