<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.BillSettleControlOrderMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="main_biz_type" jdbcType="INTEGER" property="mainBizType" />
    <result column="main_biz_id" jdbcType="BIGINT" property="mainBizId" />
    <result column="settle_subject_type" jdbcType="INTEGER" property="settleSubjectType" />
    <result column="settle_subject_id" jdbcType="BIGINT" property="settleSubjectId" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="control_status" jdbcType="TINYINT" property="controlStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, main_biz_type, main_biz_id, settle_subject_type, settle_subject_id, 
    gmt_create, gmt_update, deleted, control_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_settle_control_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_settle_control_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO" useGeneratedKeys="true">
    insert into bill_settle_control_order (order_no, main_biz_type, main_biz_id, 
      settle_subject_type, settle_subject_id, gmt_create, 
      gmt_update, deleted, control_status)
    values (#{orderNo,jdbcType=VARCHAR}, #{mainBizType,jdbcType=INTEGER}, #{mainBizId,jdbcType=BIGINT}, 
      #{settleSubjectType,jdbcType=INTEGER}, #{settleSubjectId,jdbcType=BIGINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{controlStatus,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO" useGeneratedKeys="true">
    insert into bill_settle_control_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="mainBizType != null">
        main_biz_type,
      </if>
      <if test="mainBizId != null">
        main_biz_id,
      </if>
      <if test="settleSubjectType != null">
        settle_subject_type,
      </if>
      <if test="settleSubjectId != null">
        settle_subject_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="controlStatus != null">
        control_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="mainBizType != null">
        #{mainBizType,jdbcType=INTEGER},
      </if>
      <if test="mainBizId != null">
        #{mainBizId,jdbcType=BIGINT},
      </if>
      <if test="settleSubjectType != null">
        #{settleSubjectType,jdbcType=INTEGER},
      </if>
      <if test="settleSubjectId != null">
        #{settleSubjectId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="controlStatus != null">
        #{controlStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO">
    update bill_settle_control_order
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="mainBizType != null">
        main_biz_type = #{mainBizType,jdbcType=INTEGER},
      </if>
      <if test="mainBizId != null">
        main_biz_id = #{mainBizId,jdbcType=BIGINT},
      </if>
      <if test="settleSubjectType != null">
        settle_subject_type = #{settleSubjectType,jdbcType=INTEGER},
      </if>
      <if test="settleSubjectId != null">
        settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="controlStatus != null">
        control_status = #{controlStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO">
    update bill_settle_control_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
      main_biz_type = #{mainBizType,jdbcType=INTEGER},
      main_biz_id = #{mainBizId,jdbcType=BIGINT},
      settle_subject_type = #{settleSubjectType,jdbcType=INTEGER},
      settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      control_status = #{controlStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByOrderNos" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_settle_control_order
    where deleted = 0
    and order_no in
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="selectIdsForShard" parameterType="map" resultType="long">
    select id from bill_settle_control_order
    where deleted = 0
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    and control_status is not null
    and gmt_update <![CDATA[ >= ]]> #{timeRange.start, jdbcType=BIGINT}
    and gmt_update <![CDATA[ <= ]]> #{timeRange.end, jdbcType=BIGINT}
    order by id asc
    limit #{pageSize}
  </select>

  <select id="selectByIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_settle_control_order
    where deleted = 0
    and id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

</mapper>