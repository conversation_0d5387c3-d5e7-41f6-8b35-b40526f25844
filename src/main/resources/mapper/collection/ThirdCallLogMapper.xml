<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.collection.mapper.ThirdCallLogMapper">
    <resultMap id="BaseResultMap" type="so.dian.huashan.collection.mapper.dos.ThirdCallLogDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="call_date" jdbcType="INTEGER" property="callDate"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="request_json" jdbcType="VARCHAR" property="requestJson"/>
        <result column="http_status_code" jdbcType="INTEGER" property="httpStatusCode"/>
        <result column="third_error_code" jdbcType="VARCHAR" property="thirdErrorCode"/>
        <result column="fail_reason" jdbcType="TINYINT" property="failReason"/>
        <result column="third_call_time" jdbcType="TINYINT" property="thirdCallTime"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="INTEGER" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , call_date, biz_type, request_json, http_status_code, third_error_code, fail_reason,third_call_time,
    gmt_create, gmt_update, deleted
    </sql>
    <sql id="where">
        <if test="callDate !=null">
            and call_date = #{callDate}
        </if>
        <if test="bizType !=null">
            and biz_type = #{bizType}
        </if>
        <if test="httpStatusCode !=null">
            and http_status_code = #{httpStatusCode}
        </if>
        <if test="thirdErrorCode !=null">
            and third_error_code = #{thirdErrorCode}
        </if>
        and deleted = 0;
    </sql>
    <sql id="table_name">
        third_call_log
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.collection.mapper.dos.ThirdCallLogDO" useGeneratedKeys="true">
        insert into
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="callDate != null">
                call_date,
            </if>
            <if test="bizType != null">
                biz_type,
            </if>
            <if test="requestJson != null">
                request_json,
            </if>
            <if test="httpStatusCode != null">
                http_status_code,
            </if>
            <if test="thirdErrorCode != null">
                `third_error_code`,
            </if>
            <if test="failReason != null">
                fail_reason,
            </if>
            <if test="thirdCallTime != null">
                third_call_time,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="callDate != null">
                #{callDate},
            </if>
            <if test="bizType != null">
                #{bizType},
            </if>
            <if test="requestJson != null">
                #{requestJson},
            </if>
            <if test="httpStatusCode != null">
                #{httpStatusCode},
            </if>
            <if test="thirdErrorCode != null">
                #{thirdErrorCode},
            </if>
            <if test="failReason != null">
                #{failReason},
            </if>
            <if test="thirdCallTime != null">
                #{thirdCallTime},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=BIGINT},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update
        <include refid="table_name"/>
        <set>
            <if test="callDate != null">
                call_date = #{callDate},
            </if>
            <if test="bizType != null">
                biz_type = #{bizType},
            </if>
            <if test="requestJson != null">
                request_json = #{requestJson},
            </if>
            <if test="httpStatusCode != null">
                http_status_code = #{httpStatusCode},
            </if>
            <if test="thirdErrorCode != null">
                third_error_code = #{thirdErrorCode},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason},
            </if>
            <if test="thirdCallTime != null">
                third_call_time = #{thirdCallTime},
            </if>
        </set>
        where id = #{id}
    </update>
    <select id="selectByParam" parameterType="so.dian.huashan.collection.mapper.param.ThirdCallLogParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="table_name"/>
        <where>
            <include refid="where"/>
        </where>
    </select>
    <select id="selectByIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="table_name"/>
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>