<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.ControlOrderCheckConsistencyMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="control_order_id" jdbcType="BIGINT" property="controlOrderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="control_status" jdbcType="TINYINT" property="controlStatus" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `status`, control_order_id, order_no, control_status, gmt_create, gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from control_order_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from control_order_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO" useGeneratedKeys="true">
    insert into control_order_check_consistency (`status`, control_order_id, order_no, control_status,
      gmt_create, gmt_update, deleted
      )
    values (#{status,jdbcType=TINYINT}, #{controlOrderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{controlStatus,jdbcType=TINYINT}
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO" useGeneratedKeys="true">
    insert into control_order_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="controlOrderId != null">
        control_order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="controlStatus != null">
        control_status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="controlOrderId != null">
        #{controlOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="controlStatus != null">
        #{controlStatus,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO">
    update control_order_check_consistency
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="controlOrderId != null">
        control_order_id = #{controlOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="controlStatus != null">
        control_status = #{controlStatus,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO">
    update control_order_check_consistency
    set `status` = #{status,jdbcType=TINYINT},
      control_order_id = #{controlOrderId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      control_status = #{controlStatus,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into control_order_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="consistencyDOS" item="consistencyDO" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{consistencyDO.id,jdbcType=BIGINT},
        #{consistencyDO.status,jdbcType=TINYINT},
        #{consistencyDO.controlOrderId,jdbcType=BIGINT},
        #{consistencyDO.orderNo,jdbcType=VARCHAR},
        #{consistencyDO.controlStatus,jdbcType=TINYINT},
        #{consistencyDO.gmtCreate,jdbcType=BIGINT},
        #{consistencyDO.gmtUpdate,jdbcType=BIGINT},
        #{consistencyDO.deleted,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>

  <select id="selectByControlOrderIds" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from control_order_check_consistency
    where deleted = 0
    and control_order_id in
    <foreach collection="controlOrderIds" item="controlOrderId" open="(" close=")" separator=",">
      #{controlOrderId,jdbcType=BIGINT}
    </foreach>
  </select>

  <update id="batchLogicDelete" parameterType="java.lang.Long">
    update control_order_check_consistency
    set deleted = 1, gmt_update = UNIX_TIMESTAMP()*1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>