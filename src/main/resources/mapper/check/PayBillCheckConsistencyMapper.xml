<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.PayBillCheckConsistencyMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pay_bill_id" jdbcType="BIGINT" property="payBillId" />
    <result column="pay_bill_third_id" jdbcType="BIGINT" property="payBillThirdId" />
    <result column="pay_subject_id" jdbcType="BIGINT" property="paySubjectId" />
    <result column="pay_subject_type" jdbcType="TINYINT" property="paySubjectType" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo" />
    <result column="trade_amount" jdbcType="BIGINT" property="tradeAmount" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="check_type" jdbcType="TINYINT" property="checkType" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, pay_bill_id, pay_bill_third_id, pay_subject_id, pay_subject_type,
    trade_type, `source`, trade_no, channel_trade_no, trade_amount, trade_time, check_type, 
    deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pay_bill_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pay_bill_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO" useGeneratedKeys="true">
    insert into pay_bill_check_consistency (pay_bill_id, pay_bill_third_id, pay_subject_id, 
      pay_subject_type,  trade_type,
      `source`, trade_no, channel_trade_no, 
      trade_amount, trade_time, check_type, 
      deleted, gmt_create, gmt_update
      )
    values (#{payBillId,jdbcType=BIGINT}, #{payBillThirdId,jdbcType=BIGINT}, #{paySubjectId,jdbcType=BIGINT}, 
      #{paySubjectType,jdbcType=TINYINT},  #{tradeType,jdbcType=VARCHAR},
      #{source,jdbcType=TINYINT}, #{tradeNo,jdbcType=VARCHAR}, #{channelTradeNo,jdbcType=VARCHAR}, 
      #{tradeAmount,jdbcType=BIGINT}, #{tradeTime,jdbcType=TIMESTAMP}, #{checkType,jdbcType=TINYINT}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO" useGeneratedKeys="true">
    insert into pay_bill_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="payBillId != null">
        pay_bill_id,
      </if>
      <if test="payBillThirdId != null">
        pay_bill_third_id,
      </if>
      <if test="paySubjectId != null">
        pay_subject_id,
      </if>
      <if test="paySubjectType != null">
        pay_subject_type,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="tradeTime != null">
        trade_time,
      </if>
      <if test="checkType != null">
        check_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="payBillId != null">
        #{payBillId,jdbcType=BIGINT},
      </if>
      <if test="payBillThirdId != null">
        #{payBillThirdId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectId != null">
        #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        #{paySubjectType,jdbcType=TINYINT},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=BIGINT},
      </if>
      <if test="tradeTime != null">
        #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO">
    update pay_bill_check_consistency
    <set>
      <if test="payBillId != null">
        pay_bill_id = #{payBillId,jdbcType=BIGINT},
      </if>
      <if test="payBillThirdId != null">
        pay_bill_third_id = #{payBillThirdId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectId != null">
        pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=TINYINT},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=BIGINT},
      </if>
      <if test="tradeTime != null">
        trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkType != null">
        check_type = #{checkType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO">
    update pay_bill_check_consistency
    set pay_bill_id = #{payBillId,jdbcType=BIGINT},
      pay_bill_third_id = #{payBillThirdId,jdbcType=BIGINT},
      pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=TINYINT},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
      trade_amount = #{tradeAmount,jdbcType=BIGINT},
      trade_time = #{tradeTime,jdbcType=TIMESTAMP},
      check_type = #{checkType,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into pay_bill_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="checkDiffs" item="checkDiff" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{checkDiff.id,jdbcType=BIGINT},
        #{checkDiff.payBillId,jdbcType=BIGINT},
        #{checkDiff.payBillThirdId,jdbcType=BIGINT},
        #{checkDiff.paySubjectId,jdbcType=BIGINT},
        #{checkDiff.paySubjectType,jdbcType=TINYINT},
        #{checkDiff.tradeType,jdbcType=VARCHAR},
        #{checkDiff.source,jdbcType=TINYINT},
        #{checkDiff.tradeNo,jdbcType=VARCHAR},
        #{checkDiff.channelTradeNo,jdbcType=VARCHAR},
        #{checkDiff.tradeAmount,jdbcType=BIGINT},
        #{checkDiff.tradeTime,jdbcType=TIMESTAMP},
        #{checkDiff.checkType,jdbcType=TINYINT},
        #{checkDiff.deleted,jdbcType=TINYINT},
        #{checkDiff.gmtCreate,jdbcType=BIGINT},
        #{checkDiff.gmtUpdate,jdbcType=BIGINT}
      </trim>
    </foreach>
  </insert>

  <update id="batchDeleteByPayBillIds" parameterType="list">
    update pay_bill_check_consistency set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where pay_bill_id in
    <foreach collection="payBillIds" item="payBillId" open="(" close=")" separator=",">
      #{payBillId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="batchDeleteByPayBillThirdIds" parameterType="list">
    update pay_bill_check_consistency set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where pay_bill_third_id in
    <foreach collection="payBillThirdIds" item="payBillThirdId" open="(" close=")" separator=",">
      #{payBillThirdId,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>