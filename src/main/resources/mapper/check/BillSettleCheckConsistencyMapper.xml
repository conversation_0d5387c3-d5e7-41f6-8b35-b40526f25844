<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.BillSettleCheckConsistencyMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="original_id" jdbcType="BIGINT" property="originalId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="check_type" jdbcType="TINYINT" property="checkType" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, original_id, order_no, order_status, check_type, gmt_create, gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_settle_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_settle_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO" useGeneratedKeys="true">
    insert into bill_settle_check_consistency (original_id, order_no, order_status, 
      check_type, gmt_create, gmt_update, 
      deleted)
    values (#{originalId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, 
      #{checkType,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, 
      #{deleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO" useGeneratedKeys="true">
    insert into bill_settle_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="originalId != null">
        original_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="checkType != null">
        check_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="originalId != null">
        #{originalId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO">
    update bill_settle_check_consistency
    <set>
      <if test="originalId != null">
        original_id = #{originalId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="checkType != null">
        check_type = #{checkType,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO">
    update bill_settle_check_consistency
    set original_id = #{originalId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      check_type = #{checkType,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into bill_settle_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="checkConsistencys" item="checkConsistency" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{checkConsistency.id,jdbcType=BIGINT},
        #{checkConsistency.originalId,jdbcType=BIGINT},
        #{checkConsistency.orderNo,jdbcType=VARCHAR},
        #{checkConsistency.orderStatus,jdbcType=INTEGER},
        #{checkConsistency.checkType,jdbcType=TINYINT},
        #{checkConsistency.gmtCreate,jdbcType=BIGINT},
        #{checkConsistency.gmtUpdate,jdbcType=BIGINT},
        #{checkConsistency.deleted,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>

  <select id="selectMaxIdByGmtCreate" parameterType="long" resultType="long">
    select max(id) from bill_settle_check_consistency where gmt_create <![CDATA[<=]]> #{gmtCreate,jdbcType=BIGINT}
  </select>
  <delete id="deleteByGmtCreateAndMaxId" parameterType="map">
    delete from bill_settle_check_consistency
    where
      id <![CDATA[<=]]> #{maxId,jdbcType=BIGINT}
      and gmt_create <![CDATA[<=]]> #{gmtCreate,jdbcType=BIGINT}
      limit #{size,jdbcType=INTEGER}
  </delete>
</mapper>