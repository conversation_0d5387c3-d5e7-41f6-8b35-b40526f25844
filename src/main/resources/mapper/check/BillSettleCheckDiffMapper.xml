<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.BillSettleCheckDiffMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="original_id" jdbcType="BIGINT" property="originalId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Base_Column_List">
    id, original_id, order_no, order_status, gmt_create, gmt_update, deleted, version
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_settle_check_diff
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_settle_check_diff
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <delete id="batchDeleteByIds" parameterType="list">
    delete from bill_settle_check_diff
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>

  <update id="batchAddVersion" parameterType="list">
    update bill_settle_check_diff set version = version + 1, gmt_update = UNIX_TIMESTAMP()*1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO" useGeneratedKeys="true">
    insert into bill_settle_check_diff (original_id, order_no, order_status, 
      gmt_create, gmt_update, deleted, version
      )
    values (#{originalId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO" useGeneratedKeys="true">
    insert into bill_settle_check_diff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="originalId != null">
        original_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="originalId != null">
        #{originalId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO">
    update bill_settle_check_diff
    <set>
      <if test="originalId != null">
        original_id = #{originalId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO">
    update bill_settle_check_diff
    set original_id = #{originalId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into bill_settle_check_diff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="checkDiffs" item="checkDiff" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{checkDiff.id,jdbcType=BIGINT},
        #{checkDiff.originalId,jdbcType=BIGINT},
        #{checkDiff.orderNo,jdbcType=VARCHAR},
        #{checkDiff.orderStatus,jdbcType=INTEGER},
        #{checkDiff.gmtCreate,jdbcType=BIGINT},
        #{checkDiff.gmtUpdate,jdbcType=BIGINT},
        #{checkDiff.deleted,jdbcType=INTEGER},
        #{checkDiff.version,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>

  <select id="selectReCheck" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_settle_check_diff
    where deleted = 0 order by gmt_create asc, id asc limit #{page.offset},#{page.pageSize}
  </select>

  <select id="selectRecently" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_settle_check_diff
    where deleted = 0
    and gmt_create <![CDATA[>=]]> #{startGmtCreate,jdbcType=BIGINT}
    and gmt_create <![CDATA[<=]]> #{endGmtCreate,jdbcType=BIGINT}
    order by gmt_create desc, id asc limit #{page.offset},#{page.pageSize}
  </select>

  <select id="countDiff" resultType="int">
    select
    count(1)
    from bill_settle_check_diff
    where deleted = 0
    and gmt_create <![CDATA[>=]]> #{startGmtCreate,jdbcType=BIGINT}
    and gmt_create <![CDATA[<=]]> #{endGmtCreate,jdbcType=BIGINT}
  </select>

  <select id="selectByIds" parameterType="list" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_settle_check_diff
    where deleted = 0
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id,jdbcType=BIGINT}
      </foreach>
  </select>
</mapper>