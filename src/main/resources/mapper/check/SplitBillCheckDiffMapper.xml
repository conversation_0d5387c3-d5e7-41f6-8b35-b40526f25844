<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.SplitBillCheckDiffMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="settle_detail_no" jdbcType="VARCHAR" property="settleDetailNo" />
    <result column="wx_detail_id" jdbcType="BIGINT" property="wxDetailId" />
    <result column="biz_original_id" jdbcType="BIGINT" property="bizOriginalId" />
    <result column="diff_scene" jdbcType="TINYINT" property="diffScene" />
    <result column="diff_reason" jdbcType="VARCHAR" property="diffReason" />
    <result column="check_count" jdbcType="INTEGER" property="checkCount" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, `status`, settle_detail_no, wx_detail_id, biz_original_id, diff_scene, diff_reason, 
    check_count, deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from split_bill_check_diff
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from split_bill_check_diff
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO" useGeneratedKeys="true">
    insert into split_bill_check_diff (`status`, settle_detail_no, wx_detail_id, 
      biz_original_id, diff_scene, diff_reason, 
      check_count, deleted, gmt_create, 
      gmt_update)
    values (#{status,jdbcType=TINYINT}, #{settleDetailNo,jdbcType=VARCHAR}, #{wxDetailId,jdbcType=BIGINT}, 
      #{bizOriginalId,jdbcType=BIGINT}, #{diffScene,jdbcType=TINYINT}, #{diffReason,jdbcType=VARCHAR}, 
      #{checkCount,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO" useGeneratedKeys="true">
    insert into split_bill_check_diff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="status != null">
        `status`,
      </if>
      <if test="settleDetailNo != null">
        settle_detail_no,
      </if>
      <if test="wxDetailId != null">
        wx_detail_id,
      </if>
      <if test="bizOriginalId != null">
        biz_original_id,
      </if>
      <if test="diffScene != null">
        diff_scene,
      </if>
      <if test="diffReason != null">
        diff_reason,
      </if>
      <if test="checkCount != null">
        check_count,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="settleDetailNo != null">
        #{settleDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="wxDetailId != null">
        #{wxDetailId,jdbcType=BIGINT},
      </if>
      <if test="bizOriginalId != null">
        #{bizOriginalId,jdbcType=BIGINT},
      </if>
      <if test="diffScene != null">
        #{diffScene,jdbcType=TINYINT},
      </if>
      <if test="diffReason != null">
        #{diffReason,jdbcType=VARCHAR},
      </if>
      <if test="checkCount != null">
        #{checkCount,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO">
    update split_bill_check_diff
    <set>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="settleDetailNo != null">
        settle_detail_no = #{settleDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="wxDetailId != null">
        wx_detail_id = #{wxDetailId,jdbcType=BIGINT},
      </if>
      <if test="bizOriginalId != null">
        biz_original_id = #{bizOriginalId,jdbcType=BIGINT},
      </if>
      <if test="diffScene != null">
        diff_scene = #{diffScene,jdbcType=TINYINT},
      </if>
      <if test="diffReason != null">
        diff_reason = #{diffReason,jdbcType=VARCHAR},
      </if>
      <if test="checkCount != null">
        check_count = #{checkCount,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO">
    update split_bill_check_diff
    set `status` = #{status,jdbcType=TINYINT},
      settle_detail_no = #{settleDetailNo,jdbcType=VARCHAR},
      wx_detail_id = #{wxDetailId,jdbcType=BIGINT},
      biz_original_id = #{bizOriginalId,jdbcType=BIGINT},
      diff_scene = #{diffScene,jdbcType=TINYINT},
      diff_reason = #{diffReason,jdbcType=VARCHAR},
      check_count = #{checkCount,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into split_bill_check_diff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="checkDiffs" item="checkDiff" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{checkDiff.id,jdbcType=BIGINT},
        #{checkDiff.status,jdbcType=TINYINT},
        #{checkDiff.settleDetailNo,jdbcType=VARCHAR},
        #{checkDiff.wxDetailId,jdbcType=BIGINT},
        #{checkDiff.bizOriginalId,jdbcType=BIGINT},
        #{checkDiff.diffScene,jdbcType=TINYINT},
        #{checkDiff.diffReason,jdbcType=VARCHAR},
        #{checkDiff.checkCount,jdbcType=INTEGER},
        #{checkDiff.deleted,jdbcType=TINYINT},
        #{checkDiff.gmtCreate,jdbcType=BIGINT},
        #{checkDiff.gmtUpdate,jdbcType=BIGINT}
      </trim>
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="checkDiffs" item="item" index="index" open="" close="" separator=";">
      UPDATE split_bill_check_diff
      SET
      `status` = #{item.status,jdbcType=TINYINT},
      settle_detail_no = #{item.settleDetailNo,jdbcType=VARCHAR},
      wx_detail_id = #{item.wxDetailId,jdbcType=BIGINT},
      biz_original_id = #{item.bizOriginalId,jdbcType=BIGINT},
      diff_scene = #{item.diffScene,jdbcType=TINYINT},
      diff_reason = #{item.diffReason,jdbcType=VARCHAR},
      check_count = #{item.checkCount,jdbcType=INTEGER},
      gmt_update = UNIX_TIMESTAMP()*1000
      WHERE id = #{item.id}
      AND deleted = 0
    </foreach>
  </update>

  <update id="batchDeleteBySettleDetailIds" parameterType="list">
    update split_bill_check_diff set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where biz_original_id in
    <foreach collection="settleDetailIds" item="settleDetailId" open="(" close=")" separator=",">
      #{settleDetailId,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchDeleteByIds" parameterType="list">
    update split_bill_check_diff set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchDeleteByWxSplitIds" parameterType="list">
    update split_bill_check_diff set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where wx_detail_id in
    <foreach collection="wxSplitIds" item="wxSplitId" open="(" close=")" separator=",">
      #{wxSplitId,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByCheckDateTime" parameterType="map" resultType="long">
    select
    id
    from split_bill_check_diff
    where deleted = 0 and `status` = 0
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    <if test="startTime != null">
      and gmt_update <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
    </if>
    <if test="endTime != null">
      and gmt_update <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
    </if>
    order by id asc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from split_bill_check_diff
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="totalDiffStatistics" parameterType="map" resultType="java.lang.Long">
    select count(1) from split_bill_check_diff
    where deleted = 0 and `status` = 0
      and gmt_update <![CDATA[ >= ]]> #{startBillDate}
      and gmt_update <![CDATA[ < ]]> #{endBillDate}
  </select>
</mapper>