<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.ResultBillMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.ResultBillDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bill_id" jdbcType="BIGINT" property="billId" />
    <result column="idempotent_no" jdbcType="VARCHAR" property="idempotentNo" />
    <result column="check_date" jdbcType="INTEGER" property="checkDate" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="bill_date" jdbcType="TIMESTAMP" property="billDate" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="pay_way" jdbcType="TINYINT" property="payWay" />
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" />
    <result column="mch_order_no" jdbcType="VARCHAR" property="mchOrderNo" />
    <result column="mch_id" jdbcType="VARCHAR" property="mchId" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="subject_code" jdbcType="VARCHAR" property="subjectCode" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="subject_type" jdbcType="TINYINT" property="subjectType" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="dian_pay_no" jdbcType="VARCHAR" property="dianPayNo" />
    <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bill_id, idempotent_no, check_date, trade_type, bill_date, trade_no,
    amount, biz_type, pay_way, check_result, mch_order_no, mch_id,
      currency, subject_code, subject_name, subject_type, deleted, gmt_create, gmt_update, dian_pay_no, channel_trade_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from result_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from result_bill
    where deleted = 0
    <if test="tradeType != null">
      and trade_type = #{tradeType}
    </if>
    <if test="tradeNo != null">
      and trade_no = #{tradeNo}
    </if>
    <if test="amount != null">
      and amount = #{amount}
    </if>
    <if test="payWay != null">
      and pay_way = #{payWay}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from result_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.ResultBillDO" useGeneratedKeys="true">
    insert into result_bill (bill_id,idempotent_no,
      check_date, trade_type, bill_date,
      trade_no, amount, biz_type, 
      pay_way, check_result,
     mch_order_no,
     mch_id,
     currency,
     subject_code,
     subject_name,
     subject_type,
     deleted,
      gmt_create, gmt_update, dian_pay_no, channel_trade_no)
    values (#{billId,jdbcType=BIGINT},#{idempotentNo,jdbcType=VARCHAR},
      #{checkDate,jdbcType=INTEGER}, #{tradeType,jdbcType=VARCHAR}, #{billDate,jdbcType=TIMESTAMP},
      #{tradeNo,jdbcType=VARCHAR}, #{amount,jdbcType=INTEGER}, #{bizType,jdbcType=TINYINT}, 
      #{payWay,jdbcType=TINYINT}, #{checkResult,jdbcType=VARCHAR}, #{mchOrderNo,jdbcType=VARCHAR},
            #{mchId,jdbcType=VARCHAR},
            #{currency,jdbcType=VARCHAR},
            #{subjectCode,jdbcType=VARCHAR},
            #{subjectName,jdbcType=VARCHAR},
            #{subjectType,jdbcType=TINYINT},
            #{deleted,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{dianPayNo,jdbcType=VARCHAR}, #{channelTradeNo,jdbcType=VARCHAR})
  </insert>

  <insert id="insertOrUpdateBatch" parameterType="java.util.List">
    insert into result_bill (
                       bill_id,
                       idempotent_no,
                       check_date,
                       trade_type,
                       bill_date,
                       trade_no,
                       amount,
                       biz_type,
                       pay_way,
                       check_result,
                       mch_order_no,
                       mch_id,
                       currency,
                       subject_code,
                       subject_name,
                       subject_type,
                       deleted,
                       gmt_create,
                       gmt_update,
                       dian_pay_no,
                       channel_trade_no)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.billId,jdbcType=BIGINT},
      #{item.idempotentNo,jdbcType=VARCHAR},
       #{item.checkDate,jdbcType=INTEGER},
       #{item.tradeType,jdbcType=VARCHAR},
       #{item.billDate,jdbcType=TIMESTAMP},
       #{item.tradeNo,jdbcType=VARCHAR},
       #{item.amount,jdbcType=INTEGER},
       #{item.bizType,jdbcType=TINYINT},
       #{item.payWay,jdbcType=TINYINT},
       #{item.checkResult,jdbcType=VARCHAR},

      #{item.mchOrderNo,jdbcType=VARCHAR},
      #{item.mchId,jdbcType=VARCHAR},
      #{item.currency,jdbcType=VARCHAR},
      #{item.subjectCode,jdbcType=VARCHAR},
      #{item.subjectName,jdbcType=VARCHAR},
      #{item.subjectType,jdbcType=TINYINT},

       #{item.deleted,jdbcType=TINYINT},
       #{item.gmtCreate,jdbcType=BIGINT},
       #{item.gmtUpdate,jdbcType=BIGINT},#{item.dianPayNo,jdbcType=VARCHAR},#{item.channelTradeNo,jdbcType=VARCHAR})
    </foreach>
      ON DUPLICATE KEY UPDATE bill_id=values(bill_id),
    idempotent_no=values(idempotent_no),
      check_date=values(check_date),
      trade_type=values(trade_type),
      bill_date=values(bill_date),
      trade_no=values(trade_no),
      amount=values(amount),
      biz_type=values(biz_type),
      pay_way=values(pay_way),
      check_result=values(check_result),
    mch_order_no=values(mch_order_no),
    mch_id=values(mch_id),
    currency=values(currency),
    subject_code=values(subject_code),
    subject_name=values(subject_name),
    subject_type=values(subject_type),
      deleted=values(deleted),
      gmt_update=values(gmt_update),
      dian_pay_no=values(dian_pay_no),
     channel_trade_no=values(channel_trade_no)
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.ResultBillDO" useGeneratedKeys="true">
    insert into result_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billId != null">
        bill_id,
      </if>
      <if test="idempotentNo != null">
        idempotent_no,
      </if>
      <if test="checkDate != null">
        check_date,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="payWay != null">
        pay_way,
      </if>
      <if test="checkResult != null">
        check_result,
      </if>
      <if test="mchOrderNo != null">
        mch_order_no,
      </if>
      <if test="mchId != null">
        mch_id,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="subjectCode != null">
        subject_code,
      </if>
      <if test="subjectName != null">
        subject_name,
      </if>
      <if test="subjectType != null">
        subject_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="dianPayNo != null">
        dian_pay_no,
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billId != null">
        #{billId,jdbcType=BIGINT},
      </if>
      <if test="idempotentNo != null">
        #{idempotentNo,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null">
        #{checkDate,jdbcType=INTEGER},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=TINYINT},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="mchOrderNo != null">
        #{mchOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="mchId != null">
        #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="subjectCode != null">
        #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectName != null">
        #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="subjectType != null">
        #{subjectType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="dianPayNo != null">
        #{dian_pay_no,jdbcType=VARCHAR},
      </if>
      <if test="channelTradeNo != null">
        #{channel_trade_no,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.ResultBillDO">
    update result_bill
    <set>
      <if test="billId != null">
        bill_id = #{billId,jdbcType=BIGINT},
      </if>
      <if test="idempotentNo != null">
        idempotent_no = #{idempotent_no,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null">
        check_date = #{checkDate,jdbcType=INTEGER},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="payWay != null">
        pay_way = #{payWay,jdbcType=TINYINT},
      </if>
      <if test="checkResult != null">
        check_result = #{checkResult,jdbcType=VARCHAR},
      </if>
      <if test="mchOrderNo != null">
        mch_order_no = #{mchOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="mchId != null">
        mch_id = #{mchId,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="subjectCode != null">
        subject_code = #{subjectCode,jdbcType=VARCHAR},
      </if>
      <if test="subjectName != null">
        subject_name = #{subjectName,jdbcType=VARCHAR},
      </if>
      <if test="subjectType != null">
        subject_type = #{subjectType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="dianPayNo != null">
        dian_pay_no = #{dianPayNo,jdbcType=BIGINT},
      </if>
      <if test="channelTradeNo != null">
        channel_trade_no = #{channelTradeNo,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.ResultBillDO">
    update result_bill
    set bill_id = #{billId,jdbcType=BIGINT},
        idempotent_no = #{idempotent_no,jdbcType=VARCHAR},
      check_date = #{checkDate,jdbcType=INTEGER},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      bill_date = #{billDate,jdbcType=TIMESTAMP},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=INTEGER},
      biz_type = #{bizType,jdbcType=TINYINT},
      pay_way = #{payWay,jdbcType=TINYINT},
      check_result = #{checkResult,jdbcType=VARCHAR},
      mch_order_no = #{mchOrderNo,jdbcType=VARCHAR},
      mch_id = #{mchId,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      subject_code = #{subjectCode,jdbcType=VARCHAR},
      subject_name = #{subjectName,jdbcType=VARCHAR},
      subject_type = #{subjectType,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      dian_pay_no = #{dianPayNo,jdbcType=BIGINT},
      channel_trade_no = #{channelTradeNo,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>