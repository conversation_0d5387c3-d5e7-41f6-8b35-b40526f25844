<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.SplitBillCheckConsistencyMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="wx_split_bill_detail_id" jdbcType="BIGINT" property="wxSplitBillDetailId" />
    <result column="settle_detail_original_id" jdbcType="BIGINT" property="settleDetailOriginalId" />
    <result column="split_detail_no" jdbcType="VARCHAR" property="splitDetailNo" />
    <result column="split_source_id" jdbcType="VARCHAR" property="splitSourceId" />
    <result column="split_receive_id" jdbcType="VARCHAR" property="splitReceiveId" />
    <result column="split_amt" jdbcType="BIGINT" property="splitAmt" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, wx_split_bill_detail_id, settle_detail_original_id, split_detail_no, split_source_id, 
    split_receive_id, split_amt, deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from split_bill_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from split_bill_check_consistency
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO" useGeneratedKeys="true">
    insert into split_bill_check_consistency (wx_split_bill_detail_id, settle_detail_original_id, 
      split_detail_no, split_source_id, split_receive_id, 
      split_amt, deleted, gmt_create, 
      gmt_update)
    values (#{wxSplitBillDetailId,jdbcType=BIGINT}, #{settleDetailOriginalId,jdbcType=BIGINT}, 
      #{splitDetailNo,jdbcType=VARCHAR}, #{splitSourceId,jdbcType=VARCHAR}, #{splitReceiveId,jdbcType=VARCHAR}, 
      #{splitAmt,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO" useGeneratedKeys="true">
    insert into split_bill_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wxSplitBillDetailId != null">
        wx_split_bill_detail_id,
      </if>
      <if test="settleDetailOriginalId != null">
        settle_detail_original_id,
      </if>
      <if test="splitDetailNo != null">
        split_detail_no,
      </if>
      <if test="splitSourceId != null">
        split_source_id,
      </if>
      <if test="splitReceiveId != null">
        split_receive_id,
      </if>
      <if test="splitAmt != null">
        split_amt,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wxSplitBillDetailId != null">
        #{wxSplitBillDetailId,jdbcType=BIGINT},
      </if>
      <if test="settleDetailOriginalId != null">
        #{settleDetailOriginalId,jdbcType=BIGINT},
      </if>
      <if test="splitDetailNo != null">
        #{splitDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="splitSourceId != null">
        #{splitSourceId,jdbcType=VARCHAR},
      </if>
      <if test="splitReceiveId != null">
        #{splitReceiveId,jdbcType=VARCHAR},
      </if>
      <if test="splitAmt != null">
        #{splitAmt,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO">
    update split_bill_check_consistency
    <set>
      <if test="wxSplitBillDetailId != null">
        wx_split_bill_detail_id = #{wxSplitBillDetailId,jdbcType=BIGINT},
      </if>
      <if test="settleDetailOriginalId != null">
        settle_detail_original_id = #{settleDetailOriginalId,jdbcType=BIGINT},
      </if>
      <if test="splitDetailNo != null">
        split_detail_no = #{splitDetailNo,jdbcType=VARCHAR},
      </if>
      <if test="splitSourceId != null">
        split_source_id = #{splitSourceId,jdbcType=VARCHAR},
      </if>
      <if test="splitReceiveId != null">
        split_receive_id = #{splitReceiveId,jdbcType=VARCHAR},
      </if>
      <if test="splitAmt != null">
        split_amt = #{splitAmt,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO">
    update split_bill_check_consistency
    set wx_split_bill_detail_id = #{wxSplitBillDetailId,jdbcType=BIGINT},
      settle_detail_original_id = #{settleDetailOriginalId,jdbcType=BIGINT},
      split_detail_no = #{splitDetailNo,jdbcType=VARCHAR},
      split_source_id = #{splitSourceId,jdbcType=VARCHAR},
      split_receive_id = #{splitReceiveId,jdbcType=VARCHAR},
      split_amt = #{splitAmt,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into split_bill_check_consistency
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="checkDiffs" item="checkDiff" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{checkDiff.id,jdbcType=BIGINT},
        #{checkDiff.wxSplitBillDetailId,jdbcType=BIGINT},
        #{checkDiff.settleDetailOriginalId,jdbcType=BIGINT},
        #{checkDiff.splitDetailNo,jdbcType=VARCHAR},
        #{checkDiff.splitSourceId,jdbcType=VARCHAR},
        #{checkDiff.splitReceiveId,jdbcType=VARCHAR},
        #{checkDiff.splitAmt,jdbcType=BIGINT},
        #{checkDiff.deleted,jdbcType=TINYINT},
        #{checkDiff.gmtCreate,jdbcType=BIGINT},
        #{checkDiff.gmtUpdate,jdbcType=BIGINT}
      </trim>
    </foreach>
  </insert>
  <update id="batchDeleteBySplitBillIds" parameterType="list">
    update split_bill_check_consistency set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where settle_detail_original_id in
    <foreach collection="splitBillIds" item="splitBillId" open="(" close=")" separator=",">
      #{splitBillId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="batchDeleteBySplitBillThirdIds" parameterType="list">
    update split_bill_check_consistency set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
    where wx_split_bill_detail_id in
    <foreach collection="splitBillThirdIds" item="splitBillThirdId" open="(" close=")" separator=",">
      #{splitBillThirdId,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>