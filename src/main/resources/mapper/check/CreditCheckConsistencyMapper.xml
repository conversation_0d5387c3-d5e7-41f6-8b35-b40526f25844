<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.CreditCheckConsistencyMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.CreditCheckConsistencyDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="check_type" jdbcType="TINYINT" property="checkType" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id,  batch_no ,biz_id, biz_type, check_type, gmt_create, gmt_update, deleted
  </sql>
  <insert id="insertBatch" keyProperty="id">
    insert into credit_check_consistency (batch_no,biz_id, biz_type, check_type, deleted,gmt_create, gmt_update)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.batchNo},#{item.bizId}, #{item.bizType},
      #{item.checkType}, #{item.deleted}, #{item.gmtCreate},
      #{item.gmtUpdate})
    </foreach>
  </insert>
</mapper>