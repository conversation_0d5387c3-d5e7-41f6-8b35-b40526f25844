<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.PayBillCheckDiffMapper">
    <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="pay_bill_id" jdbcType="BIGINT" property="payBillId"/>
        <result column="pay_bill_third_id" jdbcType="BIGINT" property="payBillThirdId"/>
        <result column="pay_subject_id" jdbcType="BIGINT" property="paySubjectId"/>
        <result column="pay_subject_type" jdbcType="TINYINT" property="paySubjectType"/>
        <result column="trade_type" jdbcType="VARCHAR" property="tradeType"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
        <result column="channel_trade_no" jdbcType="VARCHAR" property="channelTradeNo"/>
        <result column="trade_amount" jdbcType="BIGINT" property="tradeAmount"/>
        <result column="third_trade_amount" jdbcType="BIGINT" property="thirdTradeAmount"/>
        <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime"/>
        <result column="reason" jdbcType="TINYINT" property="reason"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , pay_bill_id, pay_bill_third_id, pay_subject_id, pay_subject_type,
    trade_type, `source`, trade_no, channel_trade_no, trade_amount, third_trade_amount, 
    trade_time, reason, `status`, remark, deleted, gmt_create, gmt_update
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pay_bill_check_diff
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from pay_bill_check_diff
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO" useGeneratedKeys="true">
        insert into pay_bill_check_diff (pay_bill_id, pay_bill_third_id, pay_subject_id,
                                         pay_subject_type, pay_subject_name, trade_type,
                                         `source`, trade_no, channel_trade_no,
                                         trade_amount, third_trade_amount, trade_time,
                                         reason, `status`, remark,
                                         deleted, gmt_create, gmt_update)
        values (#{payBillId,jdbcType=BIGINT}, #{payBillThirdId,jdbcType=BIGINT}, #{paySubjectId,jdbcType=BIGINT},
                #{paySubjectType,jdbcType=TINYINT}, #{tradeType,jdbcType=VARCHAR},
                #{source,jdbcType=TINYINT}, #{tradeNo,jdbcType=VARCHAR}, #{channelTradeNo,jdbcType=VARCHAR},
                #{tradeAmount,jdbcType=BIGINT}, #{thirdTradeAmount,jdbcType=BIGINT}, #{tradeTime,jdbcType=TIMESTAMP},
                #{reason,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR},
                #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO" useGeneratedKeys="true">
        insert into pay_bill_check_diff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="payBillId != null">
                pay_bill_id,
            </if>
            <if test="payBillThirdId != null">
                pay_bill_third_id,
            </if>
            <if test="paySubjectId != null">
                pay_subject_id,
            </if>
            <if test="paySubjectType != null">
                pay_subject_type,
            </if>
            <if test="tradeType != null">
                trade_type,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="tradeNo != null">
                trade_no,
            </if>
            <if test="channelTradeNo != null">
                channel_trade_no,
            </if>
            <if test="tradeAmount != null">
                trade_amount,
            </if>
            <if test="thirdTradeAmount != null">
                third_trade_amount,
            </if>
            <if test="tradeTime != null">
                trade_time,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="payBillId != null">
                #{payBillId,jdbcType=BIGINT},
            </if>
            <if test="payBillThirdId != null">
                #{payBillThirdId,jdbcType=BIGINT},
            </if>
            <if test="paySubjectId != null">
                #{paySubjectId,jdbcType=BIGINT},
            </if>
            <if test="paySubjectType != null">
                #{paySubjectType,jdbcType=TINYINT},
            </if>
            <if test="tradeType != null">
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                #{source,jdbcType=TINYINT},
            </if>
            <if test="tradeNo != null">
                #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="channelTradeNo != null">
                #{channelTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeAmount != null">
                #{tradeAmount,jdbcType=BIGINT},
            </if>
            <if test="thirdTradeAmount != null">
                #{thirdTradeAmount,jdbcType=BIGINT},
            </if>
            <if test="tradeTime != null">
                #{tradeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=BIGINT},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO">
        update pay_bill_check_diff
        <set>
            <if test="payBillId != null">
                pay_bill_id = #{payBillId,jdbcType=BIGINT},
            </if>
            <if test="payBillThirdId != null">
                pay_bill_third_id = #{payBillThirdId,jdbcType=BIGINT},
            </if>
            <if test="paySubjectId != null">
                pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
            </if>
            <if test="paySubjectType != null">
                pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
            </if>
            <if test="tradeType != null">
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="source != null">
                `source` = #{source,jdbcType=TINYINT},
            </if>
            <if test="tradeNo != null">
                trade_no = #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="channelTradeNo != null">
                channel_trade_no = #{channelTradeNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeAmount != null">
                trade_amount = #{tradeAmount,jdbcType=BIGINT},
            </if>
            <if test="thirdTradeAmount != null">
                third_trade_amount = #{thirdTradeAmount,jdbcType=BIGINT},
            </if>
            <if test="tradeTime != null">
                trade_time = #{tradeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=TINYINT},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=BIGINT},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO">
        update pay_bill_check_diff
        set pay_bill_id        = #{payBillId,jdbcType=BIGINT},
            pay_bill_third_id  = #{payBillThirdId,jdbcType=BIGINT},
            pay_subject_id     = #{paySubjectId,jdbcType=BIGINT},
            pay_subject_type   = #{paySubjectType,jdbcType=TINYINT},
            trade_type         = #{tradeType,jdbcType=VARCHAR},
            `source`           = #{source,jdbcType=TINYINT},
            trade_no           = #{tradeNo,jdbcType=VARCHAR},
            channel_trade_no   = #{channelTradeNo,jdbcType=VARCHAR},
            trade_amount       = #{tradeAmount,jdbcType=BIGINT},
            third_trade_amount = #{thirdTradeAmount,jdbcType=BIGINT},
            trade_time         = #{tradeTime,jdbcType=TIMESTAMP},
            reason             = #{reason,jdbcType=TINYINT},
            `status`           = #{status,jdbcType=TINYINT},
            remark             = #{remark,jdbcType=VARCHAR},
            deleted            = #{deleted,jdbcType=TINYINT},
            gmt_create         = #{gmtCreate,jdbcType=BIGINT},
            gmt_update         = #{gmtUpdate,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        insert into pay_bill_check_diff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <include refid="Base_Column_List"/>
        </trim>
        VALUES
        <foreach collection="checkDiffs" item="checkDiff" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{checkDiff.id,jdbcType=BIGINT},
                #{checkDiff.payBillId,jdbcType=BIGINT},
                #{checkDiff.payBillThirdId,jdbcType=BIGINT},
                #{checkDiff.paySubjectId,jdbcType=BIGINT},
                #{checkDiff.paySubjectType,jdbcType=TINYINT},
                #{checkDiff.tradeType,jdbcType=VARCHAR},
                #{checkDiff.source,jdbcType=TINYINT},
                #{checkDiff.tradeNo,jdbcType=VARCHAR},
                #{checkDiff.channelTradeNo,jdbcType=VARCHAR},
                #{checkDiff.tradeAmount,jdbcType=BIGINT},
                #{checkDiff.thirdTradeAmount,jdbcType=BIGINT},
                #{checkDiff.tradeTime,jdbcType=TIMESTAMP},
                #{checkDiff.reason,jdbcType=TINYINT},
                #{checkDiff.status,jdbcType=TINYINT},
                #{checkDiff.remark,jdbcType=VARCHAR},
                #{checkDiff.deleted,jdbcType=TINYINT},
                #{checkDiff.gmtCreate,jdbcType=BIGINT},
                #{checkDiff.gmtUpdate,jdbcType=BIGINT}
            </trim>
        </foreach>
    </insert>

    <update id="batchDeleteByPayBillIds" parameterType="list">
        update pay_bill_check_diff set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
        where pay_bill_id in
        <foreach collection="payBillIds" item="payBillId" open="(" close=")" separator=",">
            #{payBillId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchDeleteByPayBillThirdIds" parameterType="list">
        update pay_bill_check_diff set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000
        where pay_bill_third_id in
        <foreach collection="payBillThirdIds" item="payBillThirdId" open="(" close=")" separator=",">
            #{payBillThirdId,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByCheckDateTime" parameterType="map" resultType="long">
        select
        id
        from pay_bill_check_diff
        where deleted = 0 and `status` = 0 and reason = 1
        <if test="maxId != null">
            and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
        </if>
        <if test="startTime != null">
            and gmt_update <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            and gmt_update <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
        </if>
        order by id asc
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pay_bill_check_diff
        where deleted = 0
        <if test="ids != null">
            and id in
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selectCheckDiffGroupByParam" parameterType="so.dian.huashan.check.job.param.PayBillCheckDiffGroupParam"
            resultType="so.dian.huashan.check.job.dto.PayBillCheckDiffGroupDTO">
            select pay_subject_id,`source`,trade_time from pay_bill_check_diff
        <where>
            <if test="updateStartTime != null">
                and gmt_update <![CDATA[ >= ]]> #{updateStartTime,jdbcType=BIGINT}
            </if>
            <if test="updateEndTime != null">
                and gmt_update <![CDATA[ <= ]]> #{updateEndTime,jdbcType=BIGINT}
            </if>
            <if test="status != null">
                and `status` = #{status,jdbcType=TINYINT}
            </if>
        </where>
        group by pay_subject_id,`source`,trade_time
    </select>
    <select id="checkOffDiffAmountAndCount" parameterType="so.dian.huashan.model.param.CheckOffSumAndCountParam" resultType="so.dian.huashan.model.dto.CheckOffSumAndCountDTO">
        select (sum(abs(trade_amount-third_trade_amount))) as diff_amount,count(1) as `count` from pay_bill_check_diff
         <where>
             deleted = 0 and status = 0
             <if test="paySubjectId !=null">
                 and `pay_subject_id` = #{paySubjectId}
             </if>
             <if test="source!=null">
                 and `source` = #{source,jdbcType=TINYINT}
             </if>
             <if test="tradeStartTime != null">
                 and trade_time <![CDATA[ >= ]]> #{tradeStartTime,jdbcType=TIMESTAMP}
             </if>
             <if test="tradeEndTime != null">
                 and trade_time <![CDATA[ <= ]]> #{tradeEndTime,jdbcType=TIMESTAMP}
             </if>
         </where>
    </select>
  <select id="selectByCheckDiffParam" parameterType="so.dian.huashan.model.param.CheckDiffParam" resultType="so.dian.huashan.check.job.dto.PayBillCheckDiffDTO">
    select  id
      , pay_bill_id, pay_bill_third_id, pay_subject_id, pay_subject_type,
      trade_type, `source`, trade_no, channel_trade_no, trade_amount, third_trade_amount,
      trade_time, reason, `status`, remark, deleted, gmt_create, gmt_update,sum(trade_amount-third_trade_amount)  as diff_amount from pay_bill_check_diff
    <where>
        deleted = 0
        <if test="status !=null">
            and `status` = #{status,jdbcType=TINYINT}
        </if>
        <if test="source!=null">
            and `source` = #{source,jdbcType=TINYINT}
        </if>
        <if test="tradeStartTime != null">
            and trade_time >= #{tradeStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="tradeEndTime != null">
            and trade_time <![CDATA[ <= ]]> #{tradeEndTime,jdbcType=TIMESTAMP}
        </if>
    </where>

  </select>
  <select id="countDiff" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill_check_diff
    where deleted = 0 and `status` = 0
    order by gmt_update desc
  </select>
    <select id="getOne" parameterType="so.dian.huashan.model.param.CheckDiffParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pay_bill_check_diff
        <where>
            <if test="paySubjectId != null">
                and pay_subject_id = #{paySubjectId}
            </if>
            and deleted = 0
        </where>
        limit 1
    </select>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="checkDiffs" item="item" index="index" open="" close="" separator=";">
      UPDATE pay_bill_check_diff
      SET
      reason = #{item.reason},
      gmt_update=UNIX_TIMESTAMP()*1000
      WHERE id = #{item.id}
      AND deleted = 0
    </foreach>
  </update>
  <select id="selectByQuery" parameterType="so.dian.huashan.model.query.CheckDiffQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
        from pay_bill_check_diff
    <where>
        deleted = 0
        <if test="status !=null">
            and `status` = #{status,jdbcType=TINYINT}
        </if>
        <if test="source!=null">
            and `source` = #{source,jdbcType=TINYINT}
        </if>
        <if test="tradeStartTime != null">
            and trade_time >= #{tradeStartTime}
        </if>
        <if test="tradeEndTime != null">
           and trade_time <![CDATA[ <= ]]> #{tradeEndTime}
        </if>
        <if test="tradeNo != null">
            and trade_no = #{tradeNo}
        </if>
        <if test="channelTradeNo != null">
            and channel_trade_no = #{channelTradeNo}
        </if>
        <if test="paySubjectId != null">
        and pay_subject_id = #{paySubjectId}
        </if>
        <if test="paySubjectType != null">
        and pay_subject_type = #{paySubjectType}
        </if>
    </where>
  </select>
</mapper>