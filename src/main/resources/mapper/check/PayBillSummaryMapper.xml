<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.PayBillSummaryMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.PayBillSummaryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="natural_date" jdbcType="INTEGER" property="naturalDate" />
    <result column="pay_subject_id" jdbcType="BIGINT" property="paySubjectId" />
    <result column="pay_subject_type" jdbcType="TINYINT" property="paySubjectType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="pay_diff_amount" jdbcType="BIGINT" property="payDiffAmount" />
    <result column="pay_diff_count" jdbcType="INTEGER" property="payDiffCount" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, natural_date, pay_subject_id, pay_subject_type, `source`, pay_diff_amount, pay_diff_count, 
    deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from pay_bill_summary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from pay_bill_summary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.PayBillSummaryDO" useGeneratedKeys="true">
    insert into pay_bill_summary (natural_date, pay_subject_id, pay_subject_type, 
      `source`, pay_diff_amount, pay_diff_count, 
      deleted, gmt_create, gmt_update
      )
    values (#{naturalDate,jdbcType=INTEGER}, #{paySubjectId,jdbcType=BIGINT}, #{paySubjectType,jdbcType=TINYINT}, 
      #{source,jdbcType=TINYINT}, #{payDiffAmount,jdbcType=BIGINT}, #{payDiffCount,jdbcType=INTEGER}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.PayBillSummaryDO" useGeneratedKeys="true">
    insert into pay_bill_summary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="naturalDate != null">
        natural_date,
      </if>
      <if test="paySubjectId != null">
        pay_subject_id,
      </if>
      <if test="paySubjectType != null">
        pay_subject_type,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="payDiffAmount != null">
        pay_diff_amount,
      </if>
      <if test="payDiffCount != null">
        pay_diff_count,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="naturalDate != null">
        #{naturalDate,jdbcType=INTEGER},
      </if>
      <if test="paySubjectId != null">
        #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        #{paySubjectType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="payDiffAmount != null">
        #{payDiffAmount,jdbcType=BIGINT},
      </if>
      <if test="payDiffCount != null">
        #{payDiffCount,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.PayBillSummaryDO">
    update pay_bill_summary
    <set>
      <if test="naturalDate != null">
        natural_date = #{naturalDate,jdbcType=INTEGER},
      </if>
      <if test="paySubjectId != null">
        pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      </if>
      <if test="paySubjectType != null">
        pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=TINYINT},
      </if>
      <if test="payDiffAmount != null">
        pay_diff_amount = #{payDiffAmount,jdbcType=BIGINT},
      </if>
      <if test="payDiffCount != null">
        pay_diff_count = #{payDiffCount,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.PayBillSummaryDO">
    update pay_bill_summary
    set natural_date = #{naturalDate,jdbcType=INTEGER},
      pay_subject_id = #{paySubjectId,jdbcType=BIGINT},
      pay_subject_type = #{paySubjectType,jdbcType=TINYINT},
      `source` = #{source,jdbcType=TINYINT},
      pay_diff_amount = #{payDiffAmount,jdbcType=BIGINT},
      pay_diff_count = #{payDiffCount,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByParam" parameterType="so.dian.huashan.model.param.SummaryParam" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill_summary
    <where>
      deleted = 0
      <if test="paySubjectId != null">
        and pay_subject_id = #{paySubjectId,jdbcType=BIGINT}
      </if>
      <if test="source !=null">
        and `source` = #{source,jdbcType=TINYINT}
      </if>
      <if test="checkStartDate != null">
        and natural_date &gt;= #{checkStartDate,jdbcType=INTEGER}
      </if>
       <if test="checkEndDate != null">
        and natural_date &lt;= #{checkEndDate,jdbcType=INTEGER}
      </if>
      <if test="naturalDate != null">
        and natural_date = #{naturalDate,jdbcType=INTEGER}
      </if>

    </where>
  </select>

  <select id="selectPageByQuery" parameterType="so.dian.huashan.model.query.GetSummaryPageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from pay_bill_summary
    <where>
      deleted = 0
      <if test="paySubjectId != null">
        and pay_subject_id = #{paySubjectId,jdbcType=BIGINT}
      </if>
      <if test="paySubjectType != null">
        and pay_subject_type = #{paySubjectType,jdbcType=TINYINT}
      </if>
      <if test="source !=null">
        and `source` = #{source,jdbcType=TINYINT}
      </if>
      <if test="checkStartDate != null">
        and natural_date &gt;= #{checkStartDate,jdbcType=INTEGER}
      </if>
      <if test="checkEndDate != null">
        and natural_date &lt;= #{checkEndDate,jdbcType=INTEGER}
      </if>
    </where>
  </select>
</mapper>