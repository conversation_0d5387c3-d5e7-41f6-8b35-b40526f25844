<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.CreditCheckDiffMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.CreditCheckDiffDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="reason" jdbcType="TINYINT" property="reason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, batch_no,biz_id, biz_type, reason,status,remark, deleted, gmt_create, gmt_update
  </sql>
  <sql id="table">
    credit_check_diff
  </sql>
  <update id="deleteByBizIdAndBizType">
    update
    <include refid="table"/>
    set deleted = 1,gmt_update = UNIX_TIMESTAMP() * 1000 where biz_id in
    <foreach
            collection="bizIds" index="bizId" open="(" close=")" item="bizId" separator=",">
      #{bizId}
    </foreach>
    and biz_type = #{bizType}
  </update>
  <insert id="insertBatch" keyProperty="id">
    insert into <include refid="table"/> (batch_no,biz_id, biz_type, reason,status,remark, deleted, gmt_create, gmt_update)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.batchNo},#{item.bizId}, #{item.bizType},#{item.reason},
      #{item.status}, #{item.remark},#{item.deleted}, #{item.gmtCreate},
      #{item.gmtUpdate})
    </foreach>
  </insert>

  <select id="totalDiffStatistics" parameterType="map" resultType="java.lang.Long">
    select count(distinct (biz_id)) from <include refid="table"/>
    where deleted = 0 and `status` = 0
      and gmt_update <![CDATA[ >= ]]> #{startBillDate}
      and gmt_update <![CDATA[ < ]]> #{endBillDate}
      and biz_type = #{bizType}
  </select>
  <update id="updateStatusByIds">
    update <include refid="table"/>
    set `status` = 3,gmt_update = UNIX_TIMESTAMP() * 1000
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </update>
  <select id="selectMaxBatchNo" resultType="string">
    select batch_no from credit_check_diff where deleted = 0 and `status` = 0 order by SUBSTRING_INDEX(SUBSTRING_INDEX(batch_no, '_', 2), '_', -1) desc,SUBSTRING_INDEX(batch_no, '_', -1)  desc limit 1
  </select>
  <select id="selectByCheckBatchNo" parameterType="map" resultType="long">
    select
    id
    from <include refid="table"/>
    where deleted = 0 and `status` = 0
    <if test="batchNo != null">
      and batch_no = #{batchNo}
    </if>
    <if test="maxId != null">
      and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
    </if>
    order by id asc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from <include refid="table"/>
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>
</mapper>