<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.check.mapper.ControlOrderCheckDiffMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="control_order_id" jdbcType="BIGINT" property="controlOrderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="diff_code" jdbcType="INTEGER" property="diffCode" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    id, control_order_id, order_no, diff_code, retry_count, gmt_create,
    gmt_update, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from control_order_check_diff
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from control_order_check_diff
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO" useGeneratedKeys="true">
    insert into control_order_check_diff (control_order_id, order_no,
      diff_code, retry_count, gmt_create, 
      gmt_update, deleted)
    values (#{controlOrderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR},
      #{diffCode,jdbcType=INTEGER}, #{retryCount,jdbcType=INTEGER}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO" useGeneratedKeys="true">
    insert into control_order_check_diff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="controlOrderId != null">
        control_order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="diffCode != null">
        diff_code,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="controlOrderId != null">
        #{controlOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="diffCode != null">
        #{diffCode,jdbcType=INTEGER},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO">
    update control_order_check_diff
    <set>
      <if test="controlOrderId != null">
        control_order_id = #{controlOrderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="diffCode != null">
        diff_code = #{diffCode,jdbcType=INTEGER},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO">
    update control_order_check_diff
    set control_order_id = #{controlOrderId,jdbcType=BIGINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      diff_code = #{diffCode,jdbcType=INTEGER},
      retry_count = #{retryCount,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" keyColumn="id" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
    insert into control_order_check_diff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <include refid="Base_Column_List"/>
    </trim>
    VALUES
    <foreach collection="checkDiffs" item="checkDiff" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{checkDiff.id,jdbcType=BIGINT},
        #{checkDiff.controlOrderId,jdbcType=BIGINT},
        #{checkDiff.orderNo,jdbcType=VARCHAR},
        #{checkDiff.diffCode,jdbcType=INTEGER},
        #{checkDiff.retryCount,jdbcType=INTEGER},
        #{checkDiff.gmtCreate,jdbcType=BIGINT},
        #{checkDiff.gmtUpdate,jdbcType=BIGINT},
        #{checkDiff.deleted,jdbcType=INTEGER}
      </trim>
    </foreach>
  </insert>

  <select id="selectReCheck" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from control_order_check_diff
    where deleted = 0 and retry_count <![CDATA[<=]]> 10
    order by gmt_create asc, id asc limit #{page.offset},#{page.pageSize}
  </select>

  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="checkDiffDOS" item="checkDiffDO" index="index" open="" close="" separator=";">
      UPDATE control_order_check_diff
      SET
      retry_count = #{checkDiffDO.retryCount,jdbcType=INTEGER},
      diff_code = #{checkDiffDO.diffCode,jdbcType=INTEGER},
      gmt_update = #{checkDiffDO.gmtUpdate,jdbcType=BIGINT}
      WHERE id = #{checkDiffDO.id}
    </foreach>
  </update>

  <update id="batchLogicDeleteByIds" parameterType="map">
    UPDATE control_order_check_diff
    SET deleted = 1,
        gmt_update = UNIX_TIMESTAMP()*1000
        <if test="count != null">
          ,retry_count = retry_count + #{count,jdbcType=INTEGER}
        </if>
    where id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </update>

  <delete id="batchDeleteByIds" parameterType="java.util.List">
    delete from control_order_check_diff
    where id in
           <foreach collection="ids" item="id" open="(" close=")" separator=",">
             #{id,jdbcType=BIGINT}
           </foreach>
  </delete>

  <select id="selectIncrementDiff" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from control_order_check_diff
    where deleted = 0
    and gmt_create <![CDATA[>=]]> #{startTime,jdbcType=BIGINT}
    and gmt_create <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
    order by gmt_create desc, id asc
  </select>

  <select id="selectByControlOrderIds" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from control_order_check_diff
    where deleted = 0
    and control_order_id in
    <foreach collection="controlOrderIds" item="controlOrderId" open="(" close=")" separator=",">
      #{controlOrderId,jdbcType=BIGINT}
    </foreach>
  </select>
</mapper>