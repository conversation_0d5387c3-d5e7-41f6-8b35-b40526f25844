<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.PaymentMapper">

  <sql id="Base_Column_List">
    id, order_id, order_no, trade_no, pay_no, device_no, user_id, biz_type, pay_type,pay_type_route,
    `status`, pay_amount, refund_amount, pay_time, refund_time, note, create_time, update_time,
    biz_id
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="so.dian.huashan.common.mapper.lhc.entity.PaymentDO">
    select
    <include refid="Base_Column_List" />
    from payment
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectAlipayByPayNo" resultType="so.dian.huashan.common.mapper.lhc.entity.PaymentDO">
    select
    <include refid="Base_Column_List" />
    from payment
    where pay_no = #{payNo}
  </select>

  <select id="selectByOrderNo" resultType="so.dian.huashan.common.mapper.lhc.entity.PaymentDO">
    select
    <include refid="Base_Column_List" />
    from payment
    where order_no = #{orderNo}
  </select>

  <select id="selectByOrderNoAndPayType" resultType="so.dian.huashan.common.mapper.lhc.entity.PaymentDO">
    select
    <include refid="Base_Column_List" />
    from payment
    where order_no = #{orderNo} and pay_type = #{payType} and status = 3
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.lhc.entity.PaymentDO" useGeneratedKeys="true">
    insert into payment (order_id, order_no, trade_no,
                         pay_no, device_no, user_id,
                         biz_type, pay_type, `status`,
                         pay_amount, refund_amount, pay_time,
                         refund_time, note, create_time,
                         update_time,
                         biz_id)
    values (#{orderId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR},
            #{payNo,jdbcType=VARCHAR}, #{deviceNo,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT},
            #{bizType,jdbcType=TINYINT}, #{payType,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
            #{payAmount,jdbcType=INTEGER}, #{refundAmount,jdbcType=INTEGER}, #{payTime,jdbcType=TIMESTAMP},
            #{refundTime,jdbcType=TIMESTAMP}, #{note,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP},
            #{bizId,jdbcType=BIGINT})
  </insert>


</mapper>