<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.RefundOrderMapper">

  <sql id="Base_Column_List">
    id, refund_order_no, biz_refund_no, biz_order_no, pay_order_no, refund_amount
  </sql>

  <select id="selectByPayOrderNo" parameterType="map" resultType="so.dian.huashan.common.mapper.lhc.entity.RefundOrderDO">
    select
    <include refid="Base_Column_List" />
    from pay_core_refund_order
    where pay_order_no = #{payOrderNo,jdbcType=VARCHAR}
  </select>

</mapper>