<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.OfflinePayMapper">

  <sql id="Base_Column_List">
    id, order_no, pay_no, pay_amount, pay_time, pay_pic, pay_type, cs_id, gmt_create,
    `gmt_update`
  </sql>

  <select id="selectById" parameterType="java.lang.Long" resultType="so.dian.huashan.common.mapper.lhc.entity.OfflinePayDO">
    select 
    <include refid="Base_Column_List" />
    from offline_pay
    where id = #{id,jdbcType=BIGINT}
  </select>
</mapper>