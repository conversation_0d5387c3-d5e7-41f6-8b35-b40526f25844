<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.RefundOrderPaymentChannelMapRelationMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sharding_key" jdbcType="VARCHAR" property="shardingKey" />
    <result column="biz_order_no" jdbcType="VARCHAR" property="bizOrderNo" />
    <result column="trade_order_no" jdbcType="VARCHAR" property="tradeOrderNo" />
    <result column="refund_order_no" jdbcType="VARCHAR" property="refundOrderNo" />
    <result column="refund_type" jdbcType="VARCHAR" property="refundType" />
    <result column="payment_no" jdbcType="VARCHAR" property="paymentNo" />
    <result column="refund_channel" jdbcType="VARCHAR" property="refundChannel" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="refund_amount" jdbcType="BIGINT" property="refundAmount" />
    <result column="fail_msg" jdbcType="VARCHAR" property="failMsg" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sharding_key, biz_order_no, trade_order_no, refund_order_no, refund_type, payment_no, 
    refund_channel, order_amount, refund_status, refund_amount, fail_msg, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from refund_order_payment_channel_map_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from refund_order_payment_channel_map_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO" useGeneratedKeys="true">
    insert into refund_order_payment_channel_map_relation (sharding_key, biz_order_no, trade_order_no, 
      refund_order_no, refund_type, payment_no, 
      refund_channel, order_amount, refund_status, 
      refund_amount, fail_msg, gmt_create, 
      gmt_update)
    values (#{shardingKey,jdbcType=VARCHAR}, #{bizOrderNo,jdbcType=VARCHAR}, #{tradeOrderNo,jdbcType=VARCHAR}, 
      #{refundOrderNo,jdbcType=VARCHAR}, #{refundType,jdbcType=VARCHAR}, #{paymentNo,jdbcType=VARCHAR}, 
      #{refundChannel,jdbcType=VARCHAR}, #{orderAmount,jdbcType=BIGINT}, #{refundStatus,jdbcType=INTEGER}, 
      #{refundAmount,jdbcType=BIGINT}, #{failMsg,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO" useGeneratedKeys="true">
    insert into refund_order_payment_channel_map_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shardingKey != null">
        sharding_key,
      </if>
      <if test="bizOrderNo != null">
        biz_order_no,
      </if>
      <if test="tradeOrderNo != null">
        trade_order_no,
      </if>
      <if test="refundOrderNo != null">
        refund_order_no,
      </if>
      <if test="refundType != null">
        refund_type,
      </if>
      <if test="paymentNo != null">
        payment_no,
      </if>
      <if test="refundChannel != null">
        refund_channel,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="failMsg != null">
        fail_msg,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shardingKey != null">
        #{shardingKey,jdbcType=VARCHAR},
      </if>
      <if test="bizOrderNo != null">
        #{bizOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeOrderNo != null">
        #{tradeOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderNo != null">
        #{refundOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundType != null">
        #{refundType,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="refundChannel != null">
        #{refundChannel,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="failMsg != null">
        #{failMsg,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO">
    update refund_order_payment_channel_map_relation
    <set>
      <if test="shardingKey != null">
        sharding_key = #{shardingKey,jdbcType=VARCHAR},
      </if>
      <if test="bizOrderNo != null">
        biz_order_no = #{bizOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeOrderNo != null">
        trade_order_no = #{tradeOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderNo != null">
        refund_order_no = #{refundOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="refundType != null">
        refund_type = #{refundType,jdbcType=VARCHAR},
      </if>
      <if test="paymentNo != null">
        payment_no = #{paymentNo,jdbcType=VARCHAR},
      </if>
      <if test="refundChannel != null">
        refund_channel = #{refundChannel,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=BIGINT},
      </if>
      <if test="failMsg != null">
        fail_msg = #{failMsg,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO">
    update refund_order_payment_channel_map_relation
    set sharding_key = #{shardingKey,jdbcType=VARCHAR},
      biz_order_no = #{bizOrderNo,jdbcType=VARCHAR},
      trade_order_no = #{tradeOrderNo,jdbcType=VARCHAR},
      refund_order_no = #{refundOrderNo,jdbcType=VARCHAR},
      refund_type = #{refundType,jdbcType=VARCHAR},
      payment_no = #{paymentNo,jdbcType=VARCHAR},
      refund_channel = #{refundChannel,jdbcType=VARCHAR},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      refund_amount = #{refundAmount,jdbcType=BIGINT},
      fail_msg = #{failMsg,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>