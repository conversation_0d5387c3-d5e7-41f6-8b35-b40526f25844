<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.OrderPartnerMappingMapper">

  <select id="selectByOrderNos" parameterType="list" resultType="so.dian.huashan.common.mapper.lhc.entity.OrderPartnerMappingDO">
      select order_no,partner_order from order_partner_mapping  where order_no in
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
  </select>


</mapper>