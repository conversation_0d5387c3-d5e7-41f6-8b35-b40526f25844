<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.HeraTransOrderMapper">
    <sql id="Base_Column_List">
        id,
        <include refid="Column_List"/>
    </sql>

    <sql id="Column_List">
      trade_no, out_biz_no,
      out_pay_no, biz_type, pay_amount,
      pay_channel, pay_type, trade_status,
      pay_time, pay_acct_type, pay_acct_name,
      pay_bank_name, pay_acct_no, rec_acct_type,
      rec_bank_name, rec_acct_name, rec_acct_no,
      rec_acct_mark, pay_ope_user, fail_reason,
      remark, create_time, update_time,
      deleted, token, pay_account_subject
  </sql>

  <select id="getById" resultType="so.dian.huashan.common.mapper.lhc.entity.HeraTransOrderBO">
    select
      <include refid="Base_Column_List" />
    from hera_trans_order
    where id = #{id}
  </select>
</mapper>