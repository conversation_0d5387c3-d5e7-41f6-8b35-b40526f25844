<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.emei.ProductDeviceInfoMapper">

    <select id="selectByDeviceNos" parameterType="list" resultType="so.dian.huashan.common.mapper.emei.dto.ProductDeviceInfoDO">
        select device_no,spu_code,product_category from product_device_info where device_no in
        <foreach collection="deviceNos" item="deviceNo" open="(" close=")" separator=",">
            #{deviceNo,jdbcType=VARCHAR}
        </foreach>
        and deleted = 0
    </select>
</mapper>