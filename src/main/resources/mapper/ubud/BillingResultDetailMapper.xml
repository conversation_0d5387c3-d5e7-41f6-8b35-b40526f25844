<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.ubud.BillingResultDetailMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_no" jdbcType="VARCHAR" property="tenantNo" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="biz_code" jdbcType="VARCHAR" property="bizCode" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="biz_no" jdbcType="VARCHAR" property="bizNo" />
    <result column="billing_target_id" jdbcType="VARCHAR" property="billingTargetId" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="settle_target_id" jdbcType="VARCHAR" property="settleTargetId" />
    <result column="settle_target_type" jdbcType="VARCHAR" property="settleTargetType" />
    <result column="settle_source_type" jdbcType="VARCHAR" property="settleSourceType" />
    <result column="settle_source_id" jdbcType="VARCHAR" property="settleSourceId" />
    <result column="amount_input" jdbcType="BIGINT" property="amountInput" />
    <result column="amount_output" jdbcType="BIGINT" property="amountOutput" />
    <result column="biz_date" jdbcType="TIMESTAMP" property="bizDate" />
    <result column="fund_item_id" jdbcType="BIGINT" property="fundItemId" />
    <result column="fund_item_code" jdbcType="VARCHAR" property="fundItemCode" />
    <result column="billing_rule_id" jdbcType="BIGINT" property="billingRuleId" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_no, app_id, biz_code, serial_no, biz_no, billing_target_id, trade_type, 
    settle_target_id, settle_target_type, settle_source_type, settle_source_id, amount_input, 
    amount_output, biz_date, fund_item_id, fund_item_code, billing_rule_id, deleted, 
    create_time, update_time, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from billing_result_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from billing_result_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO" useGeneratedKeys="true">
    insert into billing_result_detail (tenant_no, app_id, biz_code, 
      serial_no, biz_no, billing_target_id, 
      trade_type, settle_target_id, settle_target_type, 
      settle_source_type, settle_source_id, amount_input, 
      amount_output, biz_date, fund_item_id, 
      fund_item_code, billing_rule_id, deleted, 
      create_time, update_time, gmt_create, 
      gmt_update)
    values (#{tenantNo,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, #{bizCode,jdbcType=VARCHAR}, 
      #{serialNo,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR}, #{billingTargetId,jdbcType=VARCHAR}, 
      #{tradeType,jdbcType=INTEGER}, #{settleTargetId,jdbcType=VARCHAR}, #{settleTargetType,jdbcType=VARCHAR}, 
      #{settleSourceType,jdbcType=VARCHAR}, #{settleSourceId,jdbcType=VARCHAR}, #{amountInput,jdbcType=BIGINT}, 
      #{amountOutput,jdbcType=BIGINT}, #{bizDate,jdbcType=TIMESTAMP}, #{fundItemId,jdbcType=BIGINT}, 
      #{fundItemCode,jdbcType=VARCHAR}, #{billingRuleId,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO" useGeneratedKeys="true">
    insert into billing_result_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantNo != null">
        tenant_no,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="bizCode != null">
        biz_code,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="bizNo != null">
        biz_no,
      </if>
      <if test="billingTargetId != null">
        billing_target_id,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="settleTargetId != null">
        settle_target_id,
      </if>
      <if test="settleTargetType != null">
        settle_target_type,
      </if>
      <if test="settleSourceType != null">
        settle_source_type,
      </if>
      <if test="settleSourceId != null">
        settle_source_id,
      </if>
      <if test="amountInput != null">
        amount_input,
      </if>
      <if test="amountOutput != null">
        amount_output,
      </if>
      <if test="bizDate != null">
        biz_date,
      </if>
      <if test="fundItemId != null">
        fund_item_id,
      </if>
      <if test="fundItemCode != null">
        fund_item_code,
      </if>
      <if test="billingRuleId != null">
        billing_rule_id,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantNo != null">
        #{tenantNo,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="billingTargetId != null">
        #{billingTargetId,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=INTEGER},
      </if>
      <if test="settleTargetId != null">
        #{settleTargetId,jdbcType=VARCHAR},
      </if>
      <if test="settleTargetType != null">
        #{settleTargetType,jdbcType=VARCHAR},
      </if>
      <if test="settleSourceType != null">
        #{settleSourceType,jdbcType=VARCHAR},
      </if>
      <if test="settleSourceId != null">
        #{settleSourceId,jdbcType=VARCHAR},
      </if>
      <if test="amountInput != null">
        #{amountInput,jdbcType=BIGINT},
      </if>
      <if test="amountOutput != null">
        #{amountOutput,jdbcType=BIGINT},
      </if>
      <if test="bizDate != null">
        #{bizDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fundItemId != null">
        #{fundItemId,jdbcType=BIGINT},
      </if>
      <if test="fundItemCode != null">
        #{fundItemCode,jdbcType=VARCHAR},
      </if>
      <if test="billingRuleId != null">
        #{billingRuleId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO">
    update billing_result_detail
    <set>
      <if test="tenantNo != null">
        tenant_no = #{tenantNo,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="bizCode != null">
        biz_code = #{bizCode,jdbcType=VARCHAR},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null">
        biz_no = #{bizNo,jdbcType=VARCHAR},
      </if>
      <if test="billingTargetId != null">
        billing_target_id = #{billingTargetId,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=INTEGER},
      </if>
      <if test="settleTargetId != null">
        settle_target_id = #{settleTargetId,jdbcType=VARCHAR},
      </if>
      <if test="settleTargetType != null">
        settle_target_type = #{settleTargetType,jdbcType=VARCHAR},
      </if>
      <if test="settleSourceType != null">
        settle_source_type = #{settleSourceType,jdbcType=VARCHAR},
      </if>
      <if test="settleSourceId != null">
        settle_source_id = #{settleSourceId,jdbcType=VARCHAR},
      </if>
      <if test="amountInput != null">
        amount_input = #{amountInput,jdbcType=BIGINT},
      </if>
      <if test="amountOutput != null">
        amount_output = #{amountOutput,jdbcType=BIGINT},
      </if>
      <if test="bizDate != null">
        biz_date = #{bizDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fundItemId != null">
        fund_item_id = #{fundItemId,jdbcType=BIGINT},
      </if>
      <if test="fundItemCode != null">
        fund_item_code = #{fundItemCode,jdbcType=VARCHAR},
      </if>
      <if test="billingRuleId != null">
        billing_rule_id = #{billingRuleId,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO">
    update billing_result_detail
    set tenant_no = #{tenantNo,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      biz_code = #{bizCode,jdbcType=VARCHAR},
      serial_no = #{serialNo,jdbcType=VARCHAR},
      biz_no = #{bizNo,jdbcType=VARCHAR},
      billing_target_id = #{billingTargetId,jdbcType=VARCHAR},
      trade_type = #{tradeType,jdbcType=INTEGER},
      settle_target_id = #{settleTargetId,jdbcType=VARCHAR},
      settle_target_type = #{settleTargetType,jdbcType=VARCHAR},
      settle_source_type = #{settleSourceType,jdbcType=VARCHAR},
      settle_source_id = #{settleSourceId,jdbcType=VARCHAR},
      amount_input = #{amountInput,jdbcType=BIGINT},
      amount_output = #{amountOutput,jdbcType=BIGINT},
      biz_date = #{bizDate,jdbcType=TIMESTAMP},
      fund_item_id = #{fundItemId,jdbcType=BIGINT},
      fund_item_code = #{fundItemCode,jdbcType=VARCHAR},
      billing_rule_id = #{billingRuleId,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByExample" parameterType="so.dian.huashan.common.mapper.ubud.example.BillingResultDetailExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from billing_result_detail
    where deleted = 0
    and biz_no = #{bizNo,jdbcType=VARCHAR}
    and settle_target_id = #{settleTargetId,jdbcType=VARCHAR}
    and settle_target_type = #{settleTargetType,jdbcType=VARCHAR}
    and trade_type = #{tradeType,jdbcType=INTEGER}
  </select>

  <select id="selectByBizNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from billing_result_detail
    where deleted = 0
    and biz_no = #{bizNo,jdbcType=VARCHAR}
  </select>
</mapper>