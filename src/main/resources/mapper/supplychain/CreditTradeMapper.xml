<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.CreditTradeMapper">
    <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.supplychain.dos.CreditTradeDO">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="credit_card_id" jdbcType="BIGINT" property="creditCardId"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId"/>
        <result column="credit_type" jdbcType="TINYINT" property="creditType"/>
        <result column="trade_amount" jdbcType="BIGINT" property="tradeAmount"/>
        <result column="extra_json" jdbcType="VARCHAR" property="extraJson"/>
        <result column="first_shipment_time" jdbcType="TIMESTAMP" property="firstShipmentTime"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="trade_status" jdbcType="TINYINT" property="tradeStatus"/>
        <result column="confirm_status" jdbcType="TINYINT" property="confirmStatus"/>
        <result column="scm_trade_no" jdbcType="VARCHAR" property="scmTradeNo"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="biz_no" jdbcType="VARCHAR" property="bizNo"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
        -->
        id, credit_card_id,contract_id, version, out_order_id, credit_type, trade_amount, extra_json,
        first_shipment_time, gmt_create, gmt_update, deleted, trade_status,confirm_status, scm_trade_no,biz_type,biz_no
    </sql>
    <select id="selectByBizNosAndBizType" resultMap="BaseResultMap">
            select <include refid="Base_Column_List"/> from credit_platform.credit_trade where biz_type = #{bizType} and biz_no in
        <foreach collection="bizNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
</mapper>