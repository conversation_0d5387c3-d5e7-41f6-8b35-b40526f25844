<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.ShipmentSubOrderMapper">
    <sql id="Base_Column_List">
        id,shipment_sub_order_no,shipment_order_no,express_id,logistics_no,content_group_stat,has_item,creator_id,remark,create_time,update_time,deleted
    </sql>
    <select id="selectByShipmentOrderNos" parameterType="java.util.List" resultType="so.dian.huashan.common.mapper.supplychain.dos.ShipmentSubOrderDO">
        select <include refid="Base_Column_List"/> from jiuhua.shipment_sub_order where shipment_order_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultType="so.dian.huashan.common.mapper.supplychain.dos.ShipmentSubOrderDO">
        select <include refid="Base_Column_List"/> from jiuhua.shipment_sub_order where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
</mapper>