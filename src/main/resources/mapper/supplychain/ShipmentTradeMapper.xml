<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.ShipmentTradeMapper">
    <select id="selectByCheckDateTime" parameterType="map" resultType="long">
       select id from taishan.trade_order where pay_status = 2 and pay_channel in (49,50,1002049,1002050,1004049,1004050,1048049,1048050) and deleted = 0 and gmt_update <![CDATA[ >= ]]>#{startTime} and gmt_update <![CDATA[ <= ]]> #{endTime}
        <if test="maxId != null">
            and id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
        </if>
       order by id asc
    </select>
    <sql id="Base_Column_List">
       id, order_no, biz_type,order_source,order_page,status,sub_status,tag,create_time,finish_time,total_amount,discount_amount,settle_amount,logistics_fee,real_pay_amount,pay_time,pay_mode,pay_channel,pay_status,pay_order_no,paid_amount,
         buyer_id,buyer_name,buyer_type,buyer_city,seller_id,seller_name,seller_type,creator_id,creator,modifier,gmt_create,gmt_update,deleted,buyer_diamond_level,approval_process_id,approval_finish_time
    </sql>
    <select id="selectByIds" parameterType="java.util.List" resultType="so.dian.huashan.common.mapper.supplychain.dos.ShipmentTradeDO">
        select <include refid="Base_Column_List"/> from taishan.trade_order where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
</mapper>