<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.TradeOrderProxySaleInfoMapper">

    <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.supplychain.dos.TradeOrderProxySaleInfoDO">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderNo" column="order_no" jdbcType="BIGINT"/>
            <result property="payMode" column="pay_mode" jdbcType="VARCHAR"/>
            <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
            <result property="alreadyPayAmount" column="already_pay_amount" jdbcType="BIGINT"/>
            <result property="waitPayAmount" column="wait_pay_amount" jdbcType="BIGINT"/>
            <result property="waitPayAmount" column="wait_pay_amount" jdbcType="BIGINT"/>
            <result property="creditInfo" column="credit_info" jdbcType="VARCHAR"/>
            <result property="stageCount" column="stage_count" jdbcType="INTEGER"/>
            <result property="payPic" column="pay_pic" jdbcType="VARCHAR"/>
            <result property="payType" column="pay_type" jdbcType="INTEGER"/>
            <result property="commissionWay" column="commission_way" jdbcType="INTEGER"/>
            <result property="contractInfo" column="contract_info" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="BIGINT"/>
            <result property="gmtUpdate" column="gmt_update" jdbcType="BIGINT"/>
            <result property="deleted" column="deleted" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_no,pay_mode,
        pay_time,already_pay_amount,wait_pay_amount,
        stage_count,pay_pic,pay_type,credit_info,
        commission_way,contract_info,remark,
        gmt_create,gmt_update,deleted
    </sql>

    <select id="selectByTradeNo" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from taishan.trade_order_proxy_sale_info
        where  order_no = #{orderNo} and deleted = 0
    </select>
</mapper>
