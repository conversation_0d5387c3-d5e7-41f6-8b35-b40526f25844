<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.ShipmentOrderMapper">
    <select id="selectByCheckDateTime" parameterType="map" resultType="long">
        select js.id
        from jiuhua.shipment_order js
        left join tianmu.shipment_notice_order so on so.notice_no = js.shipment_notice_order_no
        left join  oss.agent_purchase_order ao on  ao.purchase_order_no = substring_index(so.biz_no,"-",1)
        left join taishan.trade_order tr on tr.order_no = ao.source_order_no
        where js.status in (2,3,4) and so.biz_type in (6,61) and ao.source_type = 60 and tr.pay_status = 2 and tr.pay_channel in (49,50,1002049,1002050,1004049,1004050,1048049,1048050)
        and js.deleted =0 and so.deleted = 0 and tr.deleted = 0
        and js.update_time <![CDATA[ >= ]]>#{startTime} and js.update_time <![CDATA[ <= ]]>#{endTime}
        <if test="maxId != null">
            and js.id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
        </if>
        order by js.id asc
    </select>
    <sql id="Base_Column_List">
        id,shipment_order_no,shipment_notice_order_no,scm_biz_type,status,expected_content_group_stat,actual_content_group_stat,is_rejects,warehouse_id,transit_id,acceptor_type,acceptor_id,creator_id,close_operator_id,shipment_time,
        remark,version,order_dimension,wave_no,is_normal,create_time,update_time,deleted
    </sql>
    <select id="selectByIds" parameterType="java.util.List" resultType="so.dian.huashan.common.mapper.supplychain.dos.ShipmentOrderDO">
        select <include refid="Base_Column_List"/> from jiuhua.shipment_order where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
    <select id="selectByShipmentOrderNos" parameterType="java.util.List" resultType="so.dian.huashan.common.mapper.supplychain.dos.ShipmentOrderDO">
        select <include refid="Base_Column_List"/> from jiuhua.shipment_order where shipment_order_no in
        <foreach collection="shipmentOrderNos" item="shipmentOrderNo" open="(" separator="," close=")">
            #{shipmentOrderNo}
        </foreach>
        and deleted = 0
    </select>
</mapper>