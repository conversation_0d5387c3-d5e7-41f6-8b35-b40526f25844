<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.TransferOrderMapper">
    <select id="selectByCheckDateTime" parameterType="map" resultType="long">
        select tr.id from jinyun.channel_transfer_order tr
        left join jinyun.channel_transfer_sale_info si on si.order_no = tr.order_no
        left join tianmu.shipment_notice_order nr on nr.biz_no = tr.order_no
        left join jiuhua.shipment_order so on so.shipment_notice_order_no = nr.notice_no
        left join jiuhua.receipt_order ro on so.shipment_order_no = ro.relate_order_no
        where tr.status = 4 and tr.order_mark = 1 and si.pay_type =1 and nr.biz_type = 22  and ro.scm_biz_type =22 and so.status = 3 and ro.status = 3
        and si.deleted = 0 and tr.deleted = 0 and nr.deleted = 0 and so.deleted = 0 and ro.deleted = 0
        and ro.update_time <![CDATA[ >= ]]>#{startTime} and ro.update_time <![CDATA[ <= ]]>#{endTime}
        <if test="maxId != null">
            and si.id <![CDATA[ > ]]> #{maxId,jdbcType=BIGINT}
        </if>
        order by tr.id asc
    </select>
    <sql id="Base_Column_List">
    id,order_no,`type`,status,out_agent_id,in_agent_id,out_position_id,in_position_id,receive_name,receive_phone,receive_address,
    process_instance_id,auditor,relate_order_no,remark,creator,updator,create_time,update_time,gmt_create,gmt_update,delivery_time,receive_time,receive_remark,process_version,deleted,order_mark
    </sql>
    <select id="selectByIds" parameterType="java.util.List" resultType="so.dian.huashan.common.mapper.supplychain.dos.TransferOrderDO">
        select <include refid="Base_Column_List"/> from jinyun.channel_transfer_order where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
</mapper>