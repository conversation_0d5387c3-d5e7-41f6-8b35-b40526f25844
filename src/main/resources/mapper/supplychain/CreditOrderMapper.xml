<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.supplychain.CreditOrderMapper">
    <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.supplychain.dos.CreditOrderDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="credit_card_id" jdbcType="BIGINT" property="creditCardId"/>
        <result column="out_order_id" jdbcType="VARCHAR" property="outOrderId"/>
        <result column="out_order_type" jdbcType="TINYINT" property="outOrderType"/>
        <result column="order_amount" jdbcType="BIGINT" property="orderAmount"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="extra_json" jdbcType="VARCHAR" property="extraJson"/>
        <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="order_status" jdbcType="TINYINT" property="orderStatus"/>
        <result column="biz_order_no" jdbcType="VARCHAR" property="bizOrderNo"/>
        <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo"/>
        <result column="shipment_sub_order_no" jdbcType="VARCHAR" property="shipmentSubOrderNo"/>
        <result column="credit_trade_id" jdbcType="BIGINT" property="creditTradeId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,credit_card_id,version, out_order_id, out_order_type, order_amount, status,extra_json, gmt_create,
    gmt_update, deleted,order_status,biz_order_no,purchase_order_no,shipment_sub_order_no,credit_trade_id
    </sql>
    <select id="selectByShipmentSubOrderNos" parameterType="java.util.List" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from credit_platform.credit_order where shipment_sub_order_no in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted = 0
    </select>
</mapper>