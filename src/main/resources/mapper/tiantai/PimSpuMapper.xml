<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.tiantai.PimSpuMapper">

  <select id="selectAll" resultType="so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO">
    select spu_code,spu_name from pim_spu where deleted = 0
  </select>

  <select id="selectByCodes" parameterType="string" resultType="so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO">
    select spu_code,spu_name from pim_spu where deleted = 0 and spu_code in
    <foreach collection="spuCodes" item="spuCode" open="(" close=")" separator=",">
      #{spuCode,jdbcType=VARCHAR}
    </foreach>
  </select>
</mapper>