<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.tiantai.PimCspuMapper">

  <select id="selectAll" resultType="so.dian.huashan.common.mapper.tiantai.dos.PimCspuDO">
    select spu_code,cspu_code,cspu_name,product_category,cloud_model from pim_cspu where `status` = 0 and deleted = 0
  </select>
</mapper>