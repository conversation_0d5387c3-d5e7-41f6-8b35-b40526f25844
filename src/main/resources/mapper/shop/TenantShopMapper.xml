<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.shop.TenantShopMapper">

  <select id="selectByShopIds" parameterType="list" resultType="so.dian.huashan.common.mapper.shop.dos.TenantShopDO">
    select shop_id,tenant_id from tenant_shop where shop_id in
    <foreach collection="shopIds" item="shopId" open="(" close=")" separator=",">
      #{shopId,jdbcType=BIGINT}
    </foreach>
     and relation_type = 4 and valid = 0;
  </select>
</mapper>