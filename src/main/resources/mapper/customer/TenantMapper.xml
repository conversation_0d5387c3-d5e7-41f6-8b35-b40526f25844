<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.customer.TenantMapper">

    <select id="selectByIds" parameterType="list" resultType="so.dian.huashan.common.mapper.customer.dos.TenantDO">
        select id, agent_id from tenant where id in
         <foreach collection="ids" item="id" open="(" close=")" separator=",">
             #{id,jdbcType=BIGINT}
         </foreach>
    </select>
</mapper>