<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.oss.AgentMapper">
    <select id="selectByIds" parameterType="list" resultType="so.dian.huashan.common.mapper.oss.dos.AgentDO">
        select id,name,type from agent where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>