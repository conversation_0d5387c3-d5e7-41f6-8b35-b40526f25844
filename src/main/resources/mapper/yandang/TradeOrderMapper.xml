<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.yandang.TradeOrderMapper">
    <sql id="Base_Column_List">
        id
        , out_trade_no, trade_no, out_pay_no, biz_type, pay_amount, service_amount, pay_channel, pay_sub_channel,
      status, next_query_time, pay_start_time,pay_finish_time, pay_acc_type, pay_channel_id, pay_acc_no,
      pay_acc_name, pay_remark, rev_acc_type, rev_card_type, rev_acc_no, rev_acc_name,
      rev_card_id, rev_mobile, rev_bank_name, rev_bank_province, rev_bank_city, rev_bank_branch,
      rev_bank_no, fail_reason, remark, creator_id, creator_name, gmt_create, gmt_update, deleted
    </sql>
    <select id="findByTradeNo" resultType="so.dian.huashan.common.mapper.yandang.dos.TransOrderDO">
        select
        <include refid="Base_Column_List"/>
        from yandang.trans_order
        where trade_no = #{tradeNo}
        and deleted = 0
        limit 1
    </select>
</mapper>
