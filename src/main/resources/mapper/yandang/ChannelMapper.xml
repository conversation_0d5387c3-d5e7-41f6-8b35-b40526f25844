<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.yandang.ChannelMapper">
    <sql id="Base_Column_List">
        id, channel_name, pay_channel,pay_sub_channel, account_no, ext_data, settle_subject_id,settle_subject_type, remark,lock_version,biz_type, gmt_create,
    gmt_update, deleted
    </sql>
    <select id="selectByExample" parameterType="so.dian.huashan.common.mapper.yandang.example.ChannelExample" resultType="so.dian.huashan.common.mapper.yandang.dos.ChannelDO">
        select <include refid="Base_Column_List"/> from yandang.channel
        <where>
            deleted = 0
            <if test="payChannel !=null">
                and pay_channel = #{payChannel}
            </if>
        </where>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultType="so.dian.huashan.common.mapper.yandang.dos.ChannelDO">
        select
        <include refid="Base_Column_List" />
        from yandang.channel
        where id = #{id,jdbcType=BIGINT}
    </select>
</mapper>
