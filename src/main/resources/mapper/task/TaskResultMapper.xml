<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.task.mapper.TaskResultMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.task.mapper.entity.TaskResultDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_voucher_id" jdbcType="BIGINT" property="taskVoucherId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="result_notice" jdbcType="VARCHAR" property="resultNotice" />
    <result column="notice_time" jdbcType="VARCHAR" property="noticeTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_voucher_id, `type`, result_notice, notice_time, deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskResultDO" useGeneratedKeys="true">
    insert into task_result (task_voucher_id, `type`, result_notice, 
      notice_time, deleted, gmt_create, 
      gmt_update)
    values (#{taskVoucherId,jdbcType=BIGINT}, #{type,jdbcType=TINYINT}, #{resultNotice,jdbcType=VARCHAR}, 
      #{noticeTime,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, 
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskResultDO" useGeneratedKeys="true">
    insert into task_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskVoucherId != null">
        task_voucher_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="resultNotice != null">
        result_notice,
      </if>
      <if test="noticeTime != null">
        notice_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskVoucherId != null">
        #{taskVoucherId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="resultNotice != null">
        #{resultNotice,jdbcType=VARCHAR},
      </if>
      <if test="noticeTime != null">
        #{noticeTime,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.task.mapper.entity.TaskResultDO">
    update task_result
    <set>
      <if test="taskVoucherId != null">
        task_voucher_id = #{taskVoucherId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="resultNotice != null">
        result_notice = #{resultNotice,jdbcType=VARCHAR},
      </if>
      <if test="noticeTime != null">
        notice_time = #{noticeTime,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.task.mapper.entity.TaskResultDO">
    update task_result
    set task_voucher_id = #{taskVoucherId,jdbcType=BIGINT},
      `type` = #{type,jdbcType=TINYINT},
      result_notice = #{resultNotice,jdbcType=VARCHAR},
      notice_time = #{noticeTime,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>