<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.task.mapper.TaskRegistryMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.task.mapper.entity.TaskRegistryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="task_actuator" jdbcType="VARCHAR" property="taskActuator" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="open_status" jdbcType="TINYINT" property="openStatus" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="biz_domain_id" jdbcType="BIGINT" property="bizDomainId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_type, task_name, task_actuator, open_status,extend, deleted, gmt_create, gmt_update, biz_domain_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_registry
    where id = #{id,jdbcType=BIGINT} and deleted = 0
  </select>
  <select id="selectByName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_registry
    where task_name = #{taskName,jdbcType=VARCHAR} and deleted = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_registry
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskRegistryDO" useGeneratedKeys="true">
    insert into task_registry (task_type, task_name, task_actuator, 
      open_status,extend, deleted, gmt_create,
      gmt_update, bizDomainId)
    values (#{taskType,jdbcType=TINYINT}, #{taskName,jdbcType=VARCHAR}, #{taskActuator,jdbcType=VARCHAR}, 
      #{openStatus,jdbcType=TINYINT},#{extend,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT},
      #{gmtUpdate,jdbcType=BIGINT}, #{bizDomainId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskRegistryDO" useGeneratedKeys="true">
    insert into task_registry
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskName != null">
        task_name,
      </if>
      <if test="taskActuator != null">
        task_actuator,
      </if>
      <if test="openStatus != null">
        open_status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="bizDomainId != null">
        biz_domain_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskActuator != null">
        #{taskActuator,jdbcType=VARCHAR},
      </if>
      <if test="openStatus != null">
        #{openStatus,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="bizDomainId != null">
        #{bizDomainId, jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.task.mapper.entity.TaskRegistryDO">
    update task_registry
    <set>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="taskActuator != null">
        task_actuator = #{taskActuator,jdbcType=VARCHAR},
      </if>
      <if test="openStatus != null">
        open_status = #{openStatus,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
      <if test="bizDomainId != null">
        biz_domain_id = #{bizDomainId, jdbcTypr=BIGINT}
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.task.mapper.entity.TaskRegistryDO">
    update task_registry
    set task_type = #{taskType,jdbcType=TINYINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      task_actuator = #{taskActuator,jdbcType=VARCHAR},
      open_status = #{openStatus,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      biz_domain_id = #{bizDomainId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>