<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.task.mapper.TaskMasterMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.task.mapper.entity.TaskMasterDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_registry_id" jdbcType="BIGINT" property="taskRegistryId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="invoke_date" jdbcType="INTEGER" property="invokeDate" />
    <result column="invoke_type" jdbcType="TINYINT" property="invokeType" />
    <result column="model" jdbcType="TINYINT" property="model" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="step" jdbcType="INTEGER" property="step" />
    <result column="all_times" jdbcType="BIGINT" property="allTimes" />
    <result column="success_times" jdbcType="BIGINT" property="successTimes" />
    <result column="fail_times" jdbcType="BIGINT" property="failTimes" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_registry_id,batch_no, invoke_date, invoke_type,model, `status`, start_time, finish_time,
    step, deleted,all_times,success_times,fail_times, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_master
    where id = #{id,jdbcType=BIGINT} and deleted = 0
  </select>

  <select id="selectByPrimaryKeys" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_master
    where deleted = 0
      and id in
    <foreach collection="ids" item="id" open="(" close=")" separator=",">
      #{id,jdbcType=BIGINT}
    </foreach>
  </select>

  <select id="selectByBatchNo"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_master
    where batch_no = #{batchNo,jdbcType=VARCHAR} and deleted = 0
  </select>

  <select id="selectByExample"  resultMap="BaseResultMap" parameterType="so.dian.huashan.task.mapper.example.TaskMasterExample">
    select
    <include refid="Base_Column_List" />
    from task_master
    where deleted = 0
    <if test="taskRegistryId != null">
      and task_registry_id = #{taskRegistryId,jdbcType=BIGINT}
    </if>
    <if test="statuses != null">
      and `status` in
        <foreach collection="statuses" item="status" open="(" close=")" separator=",">
          #{status,jdbcType=TINYINT}
        </foreach>
    </if>
    order by gmt_create desc
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_master
    where deleted = 0 and invoke_type = #{invokeType,jdbcType=INTEGER}
    <if test="list != null">
      and id in
      <foreach collection="list" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="selectByInvokeTypeInvokeDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_master
    where invoke_date = #{invokeDate,jdbcType=INTEGER} and invoke_type = #{invokeType,jdbcType=INTEGER} and deleted = 0
  </select>
  <select id="selectByTaskRegistryIdInvokeDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_master
    where invoke_date = #{invokeDate,jdbcType=INTEGER} and task_registry_id = #{taskRegistryId,jdbcType=BIGINT} and deleted = 0 and status = 4
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_master
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="deleteById" parameterType="java.lang.Long">
    update task_master set deleted = 1,gmt_update = UNIX_TIMESTAMP()*1000
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskMasterDO" useGeneratedKeys="true">
    insert into task_master (task_registry_id,batch_no, invoke_date, invoke_type,model,
      `status`, start_time, finish_time, 
      step, deleted,all_times,success_times,fail_times, gmt_create,
      gmt_update)
    values (#{taskRegistryId,jdbcType=BIGINT},#{batchNo,jdbcType=VARCHAR}, #{invokeDate,jdbcType=INTEGER}, #{invokeType,jdbcType=TINYINT},  #{model,jdbcType=TINYINT},
      #{status,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP}, #{finishTime,jdbcType=TIMESTAMP}, 
      #{step,jdbcType=INTEGER}, #{deleted,jdbcType=TINYINT},#{allTimes,jdbcType=BIGINT},#{successTimes,jdbcType=BIGINT},#{failTimes,jdbcType=BIGINT}, #{gmtCreate,jdbcType=BIGINT},
      #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskMasterDO" useGeneratedKeys="true">
    insert into task_master
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskRegistryId != null">
        task_registry_id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="invokeDate != null">
        invoke_date,
      </if>
      <if test="invokeType != null">
        invoke_type,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="step != null">
        step,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="allTimes != null">
        all_times,
      </if>
      <if test="successTimes != null">
        success_times,
      </if>
      <if test="failTimes != null">
        fail_times,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskRegistryId != null">
        #{taskRegistryId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="invokeDate != null">
        #{invokeDate,jdbcType=INTEGER},
      </if>
      <if test="invokeType != null">
        #{invokeType,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        #{model,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="step != null">
        #{step,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="allTimes != null">
        #{allTimes,jdbcType=BIGINT},
      </if>
      <if test="successTimes != null">
        #{successTimes,jdbcType=BIGINT},
      </if>
      <if test="failTimes != null">
        #{failTimes,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.task.mapper.entity.TaskMasterDO">
    update task_master
    <set>
      <if test="taskRegistryId != null">
        task_registry_id = #{taskRegistryId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="invokeDate != null">
        invoke_date = #{invokeDate,jdbcType=INTEGER},
      </if>
      <if test="invokeType != null">
        invoke_type = #{invokeType,jdbcType=TINYINT},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="step != null">
        step = #{step,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="allTimes != null">
        all_times = #{allTimes,jdbcType=BIGINT},
      </if>
      <if test="successTimes != null">
        success_times = #{successTimes,jdbcType=BIGINT},
      </if>
      <if test="failTimes != null">
        fail_times = #{failTimes,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByIdCAS">
    update task_master
    set success_times = success_times +  #{successTimes,jdbcType=BIGINT},fail_times = fail_times + #{failTimes,jdbcType=BIGINT},gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT} and success_times = #{oldSuccessTimes,jdbcType=BIGINT} and fail_times = #{oldFailTimes,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.task.mapper.entity.TaskMasterDO">
    update task_master
    set task_registry_id = #{taskRegistryId,jdbcType=BIGINT},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      invoke_date = #{invokeDate,jdbcType=INTEGER},
      invoke_type = #{invokeType,jdbcType=TINYINT},
      model = #{model,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      finish_time = #{finishTime,jdbcType=TIMESTAMP},
      step = #{step,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=TINYINT},
      all_times = #{allTimes,jdbcType=BIGINT},
      success_times = #{successTimes,jdbcType=BIGINT},
      fail_times = #{failTimes,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="batchUpdateStatus" parameterType="java.util.List">
    <foreach collection="taskMasters" item="taskMaster" index="index" open="" close="" separator=";">
      UPDATE task_master
      SET
      `status` = #{taskMaster.status,jdbcType=TINYINT},
      gmt_update = #{taskMaster.gmtUpdate,jdbcType=BIGINT}
      WHERE id = #{taskMaster.id}
    </foreach>
  </update>
</mapper>