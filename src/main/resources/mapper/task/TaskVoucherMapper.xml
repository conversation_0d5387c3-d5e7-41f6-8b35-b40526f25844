<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.task.mapper.TaskVoucherMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.task.mapper.entity.TaskVoucherDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_registry_id" jdbcType="BIGINT" property="taskRegistryId" />
    <result column="task_master_id" jdbcType="BIGINT" property="taskMasterId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="voucher" jdbcType="VARCHAR" property="voucher" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="invoke_time" jdbcType="TIMESTAMP" property="invokeTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="invoke_result" jdbcType="VARCHAR" property="invokeResult" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_registry_id, task_master_id, `name`, voucher, `status`, invoke_time, finish_time, 
    invoke_result, deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_voucher
    where id = #{id,jdbcType=BIGINT} and deleted = 0
  </select>
  <select id="selectByTaskMasterId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_voucher
    where task_master_id = #{taskMasterId,jdbcType=BIGINT} and deleted = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_voucher
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskVoucherDO" useGeneratedKeys="true">
    insert into task_voucher (task_registry_id, task_master_id, `name`, 
      voucher, `status`, invoke_time, 
      finish_time, invoke_result, deleted, 
      gmt_create, gmt_update)
    values (#{taskRegistryId,jdbcType=BIGINT}, #{taskMasterId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{voucher,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{invokeTime,jdbcType=TIMESTAMP}, 
      #{finishTime,jdbcType=TIMESTAMP}, #{invokeResult,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskVoucherDO" useGeneratedKeys="true">
    insert into task_voucher
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskRegistryId != null">
        task_registry_id,
      </if>
      <if test="taskMasterId != null">
        task_master_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="voucher != null">
        voucher,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="invokeTime != null">
        invoke_time,
      </if>
      <if test="finishTime != null">
        finish_time,
      </if>
      <if test="invokeResult != null">
        invoke_result,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskRegistryId != null">
        #{taskRegistryId,jdbcType=BIGINT},
      </if>
      <if test="taskMasterId != null">
        #{taskMasterId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="voucher != null">
        #{voucher,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="invokeTime != null">
        #{invokeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null">
        #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invokeResult != null">
        #{invokeResult,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.task.mapper.entity.TaskVoucherDO">
    update task_voucher
    <set>
      <if test="taskRegistryId != null">
        task_registry_id = #{taskRegistryId,jdbcType=BIGINT},
      </if>
      <if test="taskMasterId != null">
        task_master_id = #{taskMasterId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="voucher != null">
        voucher = #{voucher,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="invokeTime != null">
        invoke_time = #{invokeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null">
        finish_time = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="invokeResult != null">
        invoke_result = #{invokeResult,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.task.mapper.entity.TaskVoucherDO">
    update task_voucher
    set task_registry_id = #{taskRegistryId,jdbcType=BIGINT},
      task_master_id = #{taskMasterId,jdbcType=BIGINT},
      `name` = #{name,jdbcType=VARCHAR},
      voucher = #{voucher,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      invoke_time = #{invokeTime,jdbcType=TIMESTAMP},
      finish_time = #{finishTime,jdbcType=TIMESTAMP},
      invoke_result = #{invokeResult,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>