<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.task.mapper.TaskSubMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.task.mapper.entity.TaskSubDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_master_id" jdbcType="BIGINT" property="taskMasterId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="voucher" jdbcType="VARCHAR" property="voucher" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_master_id, `status`, voucher, deleted, gmt_create, gmt_update
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from task_sub
    where id = #{id,jdbcType=BIGINT} and deleted = 0
  </select>

  <select id="selectByExample" parameterType="so.dian.huashan.task.mapper.example.TaskSubExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_sub
    where deleted = 0
    <if test="ids != null">
      and id in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id,jdbcType=BIGINT}
      </foreach>
    </if>
    <if test="idHash != null">
      and id <![CDATA[%]]> #{idHash.factor,jdbcType=INTEGER} = #{idHash.index,jdbcType=INTEGER}
    </if>
    <if test="taskMasterId != null">
      and task_master_id = #{taskMasterId,jdbcType=BIGINT}
    </if>
    <if test="statuses != null">
      and status in
      <foreach collection="statuses" item="status" open="(" close=")" separator=",">
        #{status,jdbcType=TINYINT}
      </foreach>
    </if>
    order by id asc
    <if test="page != null">
      limit #{page.offset}, #{page.pageSize}
    </if>
  </select>

  <select id="selectByFailStatus"  resultType="java.lang.Long">
    select
      distinct task_master_id
    from task_sub
    where status in(0,1,3) and deleted = 0 and gmt_update <![CDATA[ <= ]]> #{gmtUpdate}
  </select>

  <select id="selectByTaskMasterIdStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_sub
    where task_master_id = #{taskMasterId,jdbcType=BIGINT}
    and id<![CDATA[%]]> #{total,jdbcType=INTEGER} = #{index,jdbcType=INTEGER}
    and deleted = 0
    <if test="statusList != null">
      and status in
      <foreach collection="statusList" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
    order by id desc
  </select>

  <select id="selectByTaskMasterIdStatusList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from task_sub
    where task_master_id = #{taskMasterId,jdbcType=BIGINT}
    and deleted = 0
    <if test="statusList != null">
      and status in
      <foreach collection="statusList" item="status" open="(" close=")" separator=",">
        #{status}
      </foreach>
    </if>
    order by id desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from task_sub
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskSubDO" useGeneratedKeys="true">
    insert into task_sub (task_master_id, `status`, voucher, 
      deleted, gmt_create, gmt_update
      )
    values (#{taskMasterId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{voucher,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="so.dian.huashan.task.mapper.entity.TaskSubDO" useGeneratedKeys="true">
    insert into task_sub
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskMasterId != null">
        task_master_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="voucher != null">
        voucher,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskMasterId != null">
        #{taskMasterId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        #{voucher,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <insert id="insertBatch" keyProperty="id">
    insert into task_sub (task_master_id, `status`, voucher,
    deleted, gmt_create, gmt_update
    )
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.taskMasterId,jdbcType=BIGINT}, #{item.status,jdbcType=TINYINT}, #{item.voucher,jdbcType=VARCHAR},
      #{item.deleted,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=BIGINT}, #{item.gmtUpdate,jdbcType=BIGINT})
    </foreach>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="so.dian.huashan.task.mapper.entity.TaskSubDO">
    update task_sub
    <set>
      <if test="taskMasterId != null">
        task_master_id = #{taskMasterId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="voucher != null">
        voucher = #{voucher,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=BIGINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.task.mapper.entity.TaskSubDO">
    update task_sub
    set task_master_id = #{taskMasterId,jdbcType=BIGINT},
      `status` = #{status,jdbcType=TINYINT},
      voucher = #{voucher,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="statistics" resultType="so.dian.huashan.task.job.dto.TaskSubDTO">
    select count(1) as count, status from task_sub
    where deleted = 0 and task_master_id = #{taskMasterId,jdbcType=BIGINT}
    group by status
  </select>

  <select id="statisticsByMasterIds" parameterType="long" resultType="so.dian.huashan.task.job.dto.TaskSubDTO">
    select count(1) as count, status, task_master_id from task_sub
    where deleted = 0
    and task_master_id in
    <foreach collection="taskMasterIds" item="taskMasterId" open="(" close=")" separator=",">
      #{taskMasterId,jdbcType=BIGINT}
    </foreach>
    group by task_master_id, status
  </select>

  <update id="updateStatusByPrimaryKey" parameterType="map">
    update task_sub set `status` = #{expectStatus,jdbcType=INTEGER}, gmt_update=UNIX_TIMESTAMP()*1000
    where deleted = 0 and id = #{id,jdbcType=BIGINT} and `status` = #{oldStatus,jdbcType=INTEGER}
  </update>

  <select id="selectMasterIdBySubIds"  resultType="long">
    select
      distinct task_master_id
    from task_sub
    where deleted = 0 and id in
    <foreach collection="subIds" item="subId" open="(" close=")" separator=",">
      #{subId,jdbcType=BIGINT}
    </foreach>
  </select>
</mapper>