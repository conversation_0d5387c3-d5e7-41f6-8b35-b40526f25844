spring:
  application:
    name: huashan
  datasource:
    multiple:
      supplychain:
        url: ****************************************************************************************************************************
        username: huashan
        password: A4gALMCnGYHnjdu
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 30
        min-idle: 10
      huashan:
        primary: true
        url: *********************************************************************************************************************************
        username: huashan
        password: A4gALMCnGYHnjdu
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 30
        min-idle: 10
      newyork-sharding:
        url: ***********************************************************************************************************************************
        username: newyork_merge
        password: 2cueaQ1MGojvCA2U
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      newyork-tidb:
        url: **********************************************************************************************************
        username: newyork
        password: KNd9JAssJidAMgiaY
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      customer:
        url: *******************************************************************************************************
        username: customer
        password: zbDimJwgYU5vn9Rj
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      oss:
        url: **********************************************************************************************************************
        username: oss
        password: GklVwZXOHk8psVE3
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      shop:
        url: *********************************************************************************************************
        username: shopmerge_reader
        password: q7GzorR6EWSRQkCa
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      tiantai:
        url: ******************************************************************************************************
        username: tiantai
        password: 8VKjezSAYd6VtLoI
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 2
        max-active: 10
        min-idle: 2
      yandang:
        url: *************************************************************************************************************************
        driver-class-name: com.mysql.jdbc.Driver
        username: yandang
        password: ZbIgmh7OZcDr8JcH
        initial-size: 10
        max-active: 20
        min-idle: 10
      emei:
        url: ***************************************************************************************************
        username: emei
        password: xR6PxSXPm46p9aMG
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      lhc:
        url: ******************************************************************************************************
        username: hera
        password: YqUsvtKJV2MuffaS
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      contract:
        url: **************************************************************************************************************
        username: contract_divide
        password: QnW3FB48s6KgxF9
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      ubud:
        url: ***************************************************************************************************
        username: ubud
        password: MQGTJfKahVsJm9sh
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
      polar:
        url: *******************************************************************************************************************************************************************************************
        username: huashan
        password: gjZjqT0gh9Ec0OE0y
        driver-class-name: com.mysql.jdbc.Driver
        initial-size: 10
        max-active: 20
        min-idle: 10
  kafka:
    # 以逗号分隔的地址列表，用于建立与Kafka集群的初始连接(kafka 默认的端口号为9092)
    income:
      bootstrap-servers: kafka-dev-hb2.dian.so:9092
      consumer:
        group-id: g-huashan-kafka-local
        topic: M2K-LHC
    settle:
      bootstrap-servers: kafka-dev-hb2.dian.so:9092
      consumer:
        group-id: g-huashan-settle-local
        topic: M2K-LHC
    pay:
      bootstrap-servers: kafka-dev-hb2.dian.so:9092
      consumer:
        group-id: g-huashan-pay-local
        topic: m2k-yandang
    iot:
      bootstrap-servers: kafka-dev-hb2.dian.so:9092
      consumer:
        group-id: g-huashan-iot-local
        topic: m2k-iot
    control:
      bootstrap-servers: kafka.dian-stable.com:9092
      consumer:
        group-id: settle_huashan_stable_local
        topic: shop_order_control_result_stable
    listener:
      missing-topics-fatal: false
      ack-mode: manual
      type: batch
      concurrency: 5
      enable: true
    biz:
      listener:
        enable: true
    admin:
      client-id: huashan
  batch:
    job:
      enabled: false # 这个配置开启的话，在应用启动时会自动执行所有的job，参数是空的；所以线上可以关闭

  elasticsearch:
    uris: http://elasticsearch762.dian-dev.com:9200

rocketmq:
  name-server: mq101.dian-stable.com:9876;mq102.dian-stable.com:9876
  producer:
    group: p-huashan
  consumer:
    group: c-huashan_${system.env}
  shard-task:
    topic: shard_task_complate_${system.env}
    tag: shard_task_complate_${system.env}
  listener-brake:
    group: c_kafka_listener_brake_${system.env}
    topic: huashan
    tag: kafka_listener_brake_${system.env}
eureka:
  client:
    service-url:
      defaultZone: https://eureka.dian-stable.com/eureka

system:
  env: local

huashan:
  thirdparty:
    bill:
      file: /Users/<USER>/Desktop/test/bill/account
      delete-zip: true
  xiyouke:
    channel:
      domain: https://api-dev.xiyk.cn
    notify:
      domain: https://z-alter-941161377219743744.seven.dian-dev.com/huashan
  biz:
    es:
      control:
        index: wide_table_orders
    collection-registry-id: 1
    settle:
      dingding-code: 579a6b2793c54ced9d404a905ea1c64b
      max-loop: ********
      deleteSize: 2
      control-dingding-code: ttt
    dingding:
      code: 50ed9040425c4eceb37d33f0e6ed2219
    not-findRefund-biz-types:
      - 32
      - 89
      - 39
    unipay-biz-types:
      - 100

redisson:
  address: r-2zehnriold4wekq31z.redis.rds.aliyuncs.com:6379
  password: Kongge789
  database: 23

dian:
  xdjob:
    enabled: true
    accessToken:
    admin-addresses: http://xdjob.dian-stable.com/xxl-job-admin
    executor-address:
    executor-ip:
    executor-port: 9999
    log-path: ${user.home}/logs/xxl-job/jobhandler
    log-retention-days: 30
    appname: huashan
otel:
  exporter:
    otlp:
      protocol: grpc
      endpoint: http://otel-preview-for-prod-collector.skywalking.svc.cluster.local:4317
  propagators:
    - tracecontext
  resource:
    attributes:
      deployment.environment: local
      service:
        name: huashan
        namespace: caifa
  metrics:
    exporter: none
  logs:
    exporter: none

pay:
  bill:
    domain: https://w-alter-941161377219743744.six.dian-dev.com

huangshan:
  datasource:
    url: ***********************************************************************************************************************************
    username: huangshan
    password: IfxCWaeVojS1EhZQy
    driver-class-name: com.mysql.jdbc.Driver
    initial-size: 5
    max-active: 10
    min-idle: 5
  rocketmq:
    fail-skip: true
    nameServer: mq101.dian-stable.com:9876;mq102.dian-stable.com:9876
    topic: ${spring.application.name}_${system.env}
  redis:
    url: r-2zehnriold4wekq31z.redis.rds.aliyuncs.com:6379
    password: Kongge789
    database: 22
iot:
  xiaodian:
    mch:
      id: 1664865568