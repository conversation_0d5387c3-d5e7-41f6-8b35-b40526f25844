package so.dian.huashan.framework.scan;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import so.dian.himalaya.common.enums.DeletedEnum;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * @author: miaoshuai
 * @create: 2023/12/19 17:19
 * @description: 运维字段设值
 */
@Slf4j
@Intercepts({@Signature(
        type = Executor.class,
        method = "update",
        args = {MappedStatement.class, Object.class}
)})
public class MybatisOperationSetInterceptor implements Interceptor {

    private final static String DELETED_NAME = "deleted";
    private final static String GMT_CREATE_NAME = "gmtCreate";
    private final static String GMT_UPDATE_NAME = "gmtUpdate";

    private static final String GENERIC_NAME_PREFIX = "param";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement)invocation.getArgs()[0];
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        Object parameter = invocation.getArgs()[1];

        if (SqlCommandType.DELETE.equals(sqlCommandType))
            return invocation.proceed();

        if (parameter instanceof Map) {
            Map<String, Object> parameterMap = (Map<String, Object>) parameter;
            parameterMap.forEach((key, value) -> {
                if (!key.startsWith(GENERIC_NAME_PREFIX) && value instanceof List) {
                    List<?> params = (List<?>) value;
                    for (Object param : params) {
                        populateFieldIfNecessary(param, sqlCommandType);
                    }
                }
            });
        }else {
            populateFieldIfNecessary(parameter, sqlCommandType);
        }
        return invocation.proceed();
    }

    private void populateFieldIfNecessary(Object param, SqlCommandType sqlCommandType) {
        if (SqlCommandType.INSERT.equals(sqlCommandType)) {
            setValueIfNecessary(param, DELETED_NAME, DeletedEnum.NOT_DELETED.getCode(), Boolean.FALSE);
            setValueIfNecessary(param, GMT_CREATE_NAME, System.currentTimeMillis(), Boolean.FALSE);
            setValueIfNecessary(param, GMT_UPDATE_NAME, System.currentTimeMillis(), Boolean.FALSE);
        }else if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
            setValueIfNecessary(param, GMT_UPDATE_NAME, System.currentTimeMillis(), Boolean.TRUE);
        }
    }

    private void setValueIfNecessary(Object param, String fieldName, Object value, boolean forceUpdate) {
        try {
            PropertyDescriptor propertyDescriptor = BeanUtil.getPropertyDescriptor(param.getClass(), fieldName);
            if (Objects.isNull(propertyDescriptor))
                return;

            if (!forceUpdate) {
                Method getter = propertyDescriptor.getReadMethod();
                Object oldValue = getter.invoke(param);
                if (Objects.nonNull(oldValue))
                    return;
            }

            Method setter = propertyDescriptor.getWriteMethod();
            setter.invoke(param, value);
        }catch (Exception e) {
            log.debug("设置运维字段值时出现异常", e);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
