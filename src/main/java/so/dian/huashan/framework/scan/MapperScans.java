package so.dian.huashan.framework.scan;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/10 10:39
 * @description:
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import({MapperScannersRegistrar.class})
@Documented
public @interface MapperScans {

    MapperScan[] value() default {};
}
