package so.dian.huashan.framework.scan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScannerRegistrar;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.AnnotationMetadata;
import so.dian.huashan.framework.datasource.MultipleDataSourceConfiguration;

import java.util.Arrays;
import java.util.Optional;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/03/10 10:43
 * @description:
 */
@Slf4j
public class MapperScannersRegistrar implements ImportBeanDefinitionRegistrar, ResourceLoaderAware {

    private ResourceLoader resourceLoader;

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        log.info("扫描bean定义导入");
        AnnotationAttributes annotationAttributes = AnnotationAttributes.fromMap(importingClassMetadata.getAnnotationAttributes(MapperScans.class.getName()));
        if (CollectionUtil.isEmpty(annotationAttributes)) {
            return;
        }

        AnnotationAttributes[] values = annotationAttributes.getAnnotationArray("value");
        Arrays.stream(values).forEach(attributes -> {
            MapperScannerRegistrar scannerRegistrar = new MapperScannerRegistrar();
            scannerRegistrar.setResourceLoader(resourceLoader);

            String sqlSessionFactoryRef = attributes.getString("sqlSessionFactoryRef");
            if (StrUtil.isNotBlank(sqlSessionFactoryRef)) {
                attributes.put("sqlSessionFactoryRef", sqlSessionFactoryRef + "SqlSessionFactory");
            }

            String sqlSessionTemplateRef = attributes.getString("sqlSessionTemplateRef");
            if (StrUtil.isNotBlank(sqlSessionTemplateRef)) {
                attributes.put("sqlSessionTemplateRef", sqlSessionTemplateRef + "SqlSessionTemplate");
            }

            String[] basePackages = attributes.getStringArray("basePackages");
            MultipleDataSourceConfiguration.addMap(Optional.ofNullable(sqlSessionFactoryRef).orElse(sqlSessionTemplateRef), Arrays.asList(basePackages));
            AnnotationMetadata annotationMetadata = new MapperScanAnnotationMetadata(attributes);
            scannerRegistrar.registerBeanDefinitions(annotationMetadata, registry);
        });
    }

    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }
}
