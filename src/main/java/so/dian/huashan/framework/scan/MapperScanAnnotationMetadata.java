package so.dian.huashan.framework.scan;

import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.annotation.MergedAnnotations;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.MethodMetadata;

import java.util.Map;
import java.util.Set;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/03/10 11:01
 * @description:
 */
public class MapperScanAnnotationMetadata implements AnnotationMetadata {

    private final AnnotationAttributes annotationAttributes;

    MapperScanAnnotationMetadata(AnnotationAttributes annotationAttributes) {
        this.annotationAttributes = annotationAttributes;
    }

    @Override
    public Map<String, Object> getAnnotationAttributes(String annotationName) {
        return this.annotationAttributes;
    }

    @Override
    public Set<MethodMetadata> getAnnotatedMethods(String annotationName) {
        return null;
    }

    @Override
    public MergedAnnotations getAnnotations() {
        return null;
    }

    @Override
    public String getClassName() {
        return null;
    }

    @Override
    public boolean isInterface() {
        return false;
    }

    @Override
    public boolean isAnnotation() {
        return false;
    }

    @Override
    public boolean isAbstract() {
        return false;
    }

    @Override
    public boolean isFinal() {
        return false;
    }

    @Override
    public boolean isIndependent() {
        return false;
    }

    @Override
    public String getEnclosingClassName() {
        return null;
    }

    @Override
    public String getSuperClassName() {
        return null;
    }

    @Override
    public String[] getInterfaceNames() {
        return new String[0];
    }

    @Override
    public String[] getMemberClassNames() {
        return new String[0];
    }
}
