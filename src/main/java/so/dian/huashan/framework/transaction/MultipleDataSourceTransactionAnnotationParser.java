package so.dian.huashan.framework.transaction;

import cn.hutool.core.util.StrUtil;
import lombok.NonNull;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.transaction.annotation.SpringTransactionAnnotationParser;
import org.springframework.transaction.interceptor.RuleBasedTransactionAttribute;
import org.springframework.transaction.interceptor.TransactionAttribute;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/03/27 18:36
 * @description:
 */
public class MultipleDataSourceTransactionAnnotationParser extends SpringTransactionAnnotationParser {

    @Override
    @NonNull
    protected TransactionAttribute parseTransactionAnnotation(@NonNull AnnotationAttributes attributes) {
        RuleBasedTransactionAttribute transactionAttribute = (RuleBasedTransactionAttribute) super.parseTransactionAnnotation(attributes);
        String qualifier = transactionAttribute.getQualifier();
        if (StrUtil.isNotBlank(qualifier) && !qualifier.contains("TransactionManager")) {
            RuleBasedTransactionAttribute newAttribute = new RuleBasedTransactionAttribute(transactionAttribute);
            newAttribute.setQualifier(qualifier + "TransactionManager");
            transactionAttribute = newAttribute;
        }

        return transactionAttribute;
    }
}
