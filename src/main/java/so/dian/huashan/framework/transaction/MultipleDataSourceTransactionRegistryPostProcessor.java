package so.dian.huashan.framework.transaction;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.AnnotationTransactionAttributeSource;

/**
 * @author: miaoshuai
 * @create: 2023/03/27 15:35
 * @description:
 */
@Slf4j
@Component
public class MultipleDataSourceTransactionRegistryPostProcessor implements BeanDefinitionRegistryPostProcessor {

    private static final String TRANSACTION_ATTR_SOURCE = "transactionAttributeSource";

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        try {
            registry.removeBeanDefinition(TRANSACTION_ATTR_SOURCE);
            BeanDefinitionBuilder bean = BeanDefinitionBuilder
                    .genericBeanDefinition(AnnotationTransactionAttributeSource.class);
            bean.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
            bean.addConstructorArgValue(new MultipleDataSourceTransactionAnnotationParser());
            registry.registerBeanDefinition(TRANSACTION_ATTR_SOURCE, bean.getBeanDefinition());
        }catch (NoSuchBeanDefinitionException e) {
            log.warn("不存在指定的bean定义，bean名称是:[{}]", TRANSACTION_ATTR_SOURCE, e);
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

    }
}
