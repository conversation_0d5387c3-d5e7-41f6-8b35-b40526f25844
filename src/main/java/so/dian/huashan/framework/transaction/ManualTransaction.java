package so.dian.huashan.framework.transaction;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Builder;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/03/14 14:24
 * @description: 手动控制事务
 */
public class ManualTransaction {

    private static final String TX_MANAGER_SUFFIX = "TransactionManager";

    private final String dataSource;

    private final Propagation propagation;

    private final Isolation isolation;

    private final int timeout;

    private TransactionStatus transactionStatus;

    private boolean begin;

    private PlatformTransactionManager transactionManager;

    @Builder
    public ManualTransaction(String dataSource,
                             Propagation propagation,
                             Isolation isolation,
                             Integer timeout) {

        this.dataSource = dataSource;
        this.propagation = Optional.ofNullable(propagation).orElse(Propagation.REQUIRED);
        this.isolation = Optional.ofNullable(isolation).orElse(Isolation.DEFAULT);
        this.timeout = Optional.ofNullable(timeout).orElse(TransactionDefinition.TIMEOUT_DEFAULT);
    }

    public void invoke(Consumer<Void> bizFunc) {

        begin();
        try {
            bizFunc.accept(null);
        }catch (Throwable e) {
            rollback();
            throw e;
        }
        commit();
    }

    public void begin() {
        if (begin) {
            throw new RuntimeException("当前事务已经启动!");
        }

        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setIsolationLevel(isolation.value());
        transactionDefinition.setPropagationBehavior(propagation.value());
        transactionDefinition.setTimeout(timeout);
        this.transactionManager = StrUtil.isNotBlank(this.dataSource) ? SpringUtil.getBean(this.dataSource + TX_MANAGER_SUFFIX) : SpringUtil.getBean(PlatformTransactionManager.class);
        this.transactionStatus = transactionManager.getTransaction(transactionDefinition);
        this.begin = true;
    }

    public boolean tryBegin() {
        try {
            begin();
        }catch (RuntimeException e) {
            return false;
        }
        return true;
    }

    public void commit() {

        if (Objects.isNull(transactionStatus) || !begin) {
            throw new RuntimeException("事务还没有启动，请先调用事务的begin方法!");
        }

        this.transactionManager.commit(this.transactionStatus);
        this.transactionManager = null;
        this.transactionStatus = null;
    }

    public void rollback() {

        if (Objects.isNull(transactionStatus) || !begin) {
            throw new RuntimeException("事务还没有启动，请先调用事务的begin方法!");
        }

        this.transactionManager.rollback(this.transactionStatus);
        this.transactionManager = null;
        this.transactionStatus = null;
    }
}
