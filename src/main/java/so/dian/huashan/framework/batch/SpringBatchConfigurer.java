package so.dian.huashan.framework.batch;

import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.batch.BasicBatchConfigurer;
import org.springframework.boot.autoconfigure.batch.BatchProperties;
import org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/03/21 19:04
 * @description:
 */
@Component
public class SpringBatchConfigurer extends BasicBatchConfigurer {

    @Autowired
    @Qualifier("taskExecutor")
    private TaskExecutor taskExecutor;

    protected SpringBatchConfigurer(BatchProperties properties, DataSource dataSource, TransactionManagerCustomizers transactionManagerCustomizers) {
        super(properties, dataSource, transactionManagerCustomizers);
    }

    @Override
    protected String determineIsolationLevel() {
        return "ISOLATION_DEFAULT";
    }

//    @PostConstruct
    public void init() {
        JobLauncher jobLauncher = getJobLauncher();
        if (jobLauncher instanceof SimpleJobLauncher) {
            SimpleJobLauncher simpleJobLauncher = (SimpleJobLauncher) jobLauncher;
            simpleJobLauncher.setTaskExecutor(taskExecutor);
        }
    }
}
