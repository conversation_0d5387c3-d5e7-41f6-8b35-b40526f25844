package so.dian.huashan.framework.property;

import com.google.common.collect.Maps;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.PropertySources;
import org.springframework.core.env.PropertySourcesPropertyResolver;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/03/22 14:44
 * @description:
 */
public class FlatPropertySources {

    private final PropertySources propertySources;

    private final Map<String, Object> propertyValues;

    private final PropertySourcesPropertyResolver resolver;

    public FlatPropertySources(PropertySources propertySources) {
        this.propertySources = propertySources;
        this.resolver = new PropertySourcesPropertyResolver(propertySources);
        this.propertyValues = Maps.newHashMap();
        init();
    }

    public List<String> propertyNames() {
        return this.propertyValues.keySet().stream().collect(Collectors.toList());
    }

    public Object propertyValue(String propertyName) {
        return this.propertyValues.get(propertyName);
    }

    private void init() {
        for (PropertySource<?> propertySource : propertySources) {
            processPropertySource(propertySource, resolver);
        }
    }

    private void processPropertySource(PropertySource<?> source, PropertySourcesPropertyResolver resolver) {

        if (source instanceof CompositePropertySource) {
            processCompositePropertySource((CompositePropertySource) source, resolver);
        }
        else if (source instanceof EnumerablePropertySource) {
            processEnumerablePropertySource((EnumerablePropertySource<?>) source, resolver);
        }

    }

    private void processCompositePropertySource(CompositePropertySource source,
                                                PropertySourcesPropertyResolver resolver) {
        for (PropertySource<?> nested : source.getPropertySources()) {
            processPropertySource(nested, resolver);
        }
    }

    private void processEnumerablePropertySource(EnumerablePropertySource<?> source,
                                                 PropertySourcesPropertyResolver resolver) {
        if (source.getPropertyNames().length > 0) {
            for (String propertyName : source.getPropertyNames()) {
                Object value = getEnumerableProperty(source, resolver, propertyName);
                propertyValues.putIfAbsent(propertyName, value);
            }
        }
    }

    private Object getEnumerableProperty(EnumerablePropertySource<?> source, PropertySourcesPropertyResolver resolver,
                                         String propertyName) {
        try {
            return resolver.getProperty(propertyName, Object.class);
        }
        catch (RuntimeException ex) {
            // Probably could not resolve placeholders, ignore it here
        }
        return source.getProperty(propertyName);
    }
}
