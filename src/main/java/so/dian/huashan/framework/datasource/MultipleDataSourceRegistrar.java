package so.dian.huashan.framework.datasource;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.logging.slf4j.Slf4jImpl;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.AutoMappingBehavior;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ExecutorType;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import so.dian.himalaya.boot.interceptor.SqlRtInterceptor;
import so.dian.huashan.framework.property.FlatPropertySources;
import so.dian.huashan.framework.scan.MybatisOperationSetInterceptor;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: miaoshuai
 * @create: 2023/03/08 17:28
 * @description:
 */
@Slf4j
public class MultipleDataSourceRegistrar implements ImportBeanDefinitionRegistrar, EnvironmentAware {

    private ConfigurableEnvironment environment;

    private final static String DATA_SOURCE_PREFIX = "spring.datasource.multiple";

    private final static String PRIMARY_PROPERTY = "primary";

    @Override
    public void registerBeanDefinitions(@NonNull AnnotationMetadata metadata, @NonNull BeanDefinitionRegistry registry) {

        log.info("多数据源bean定义导入！");
        loadMapper();

        Map<String, GenericBeanDefinition> dataSourceBeanDefinitions = collectDataSourceDefinition();
        dataSourceBeanDefinitions.forEach((key, value) -> {
            registry.registerBeanDefinition(key + "DataSource", value);
            try {
                registry.registerBeanDefinition(key + "SqlSessionFactory", buildSqlSessionDefinition(key, value.isPrimary()));
            } catch (IOException e) {
                log.error("初始化数据库异常", e);
                throw new RuntimeException(e);
            }
            registry.registerBeanDefinition(key + "SqlSessionTemplate", buildSqlTemplateDefinition(key, value.isPrimary()));
            registry.registerBeanDefinition(key + "TransactionManager", buildTransactionDefinition(key, value.isPrimary()));
        });
    }

    private void loadMapper() {
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        try {
            MultipleDataSourceConfiguration.addMapperFile(resolver.getResources("classpath:mapper/**/*.xml"));
        }catch (IOException e) {
            log.error("初始化数据库mapper异常", e);
            throw new RuntimeException(e);
        }

    }

    private Map<String, GenericBeanDefinition> collectDataSourceDefinition() {

        Map<String, GenericBeanDefinition> beanDefinitions = new HashMap<>();
        FlatPropertySources flatPropertySources = new FlatPropertySources(environment.getPropertySources());
        for (String propertyName : flatPropertySources.propertyNames()) {
            if (propertyName.startsWith(DATA_SOURCE_PREFIX)) {
                String[] propertyNames = propertyName.split("\\.");
                String soruceName = propertyNames[3];
                GenericBeanDefinition beanDefinition = beanDefinitions.computeIfAbsent(soruceName, key -> new GenericBeanDefinition());
                String formatPropertyName = formatPropertyName(propertyNames[4]);
                if (PRIMARY_PROPERTY.equals(formatPropertyName) &&
                        Boolean.TRUE.toString().equals(flatPropertySources.propertyValue(propertyName).toString())) {
                    beanDefinition.setPrimary(true);
                }else {
                    beanDefinition.getPropertyValues().add(formatPropertyName, flatPropertySources.propertyValue(propertyName));
                }
                beanDefinition.setBeanClass(MultipleDataSourceWrapper.class);
            }
        }

        return beanDefinitions;
    }

    private AbstractBeanDefinition buildSqlSessionDefinition(String dataSourceId, boolean primary) throws IOException {
        BeanDefinitionBuilder bean = BeanDefinitionBuilder
                .genericBeanDefinition(SqlSessionFactoryBean.class);
        bean.addPropertyReference("dataSource", dataSourceId + "DataSource");
        Configuration configuration = new MultipleDataSourceConfiguration(dataSourceId);
        configuration.setUseGeneratedKeys(true);
        configuration.setAutoMappingBehavior(AutoMappingBehavior.PARTIAL);
        configuration.setDefaultExecutorType(ExecutorType.SIMPLE);
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setLogPrefix("lifeCycle.");
        configuration.setLogImpl(Slf4jImpl.class);
//        configuration.addInterceptor(new MybatisDateTimeSetInterceptor());
        configuration.addInterceptor(new MybatisOperationSetInterceptor());
        bean.addPropertyValue("configuration", configuration);
        bean.addPropertyValue("mapperLocations", MultipleDataSourceConfiguration.getMapperFile());
        bean.setPrimary(primary);

        String property = environment.getProperty("system.print-sql");
        if (StrUtil.isNotBlank(property)) {
            List<String> activeProfiles = Lists.newArrayList(environment.getActiveProfiles());
            List<String> printSqlEnvs = Lists.newArrayList(property.split(StrUtil.COMMA));
            if (activeProfiles.stream().anyMatch(printSqlEnvs::contains)) {
                bean.addPropertyValue("plugins", new Interceptor[]{new SqlRtInterceptor()});
            }
        }
        return bean.getBeanDefinition();
    }

    private AbstractBeanDefinition buildSqlTemplateDefinition(String dataSourceId, boolean primary) {
        BeanDefinitionBuilder bean = BeanDefinitionBuilder
                .genericBeanDefinition(SqlSessionTemplate.class);
        bean.addConstructorArgReference(dataSourceId + "SqlSessionFactory");
        bean.setPrimary(primary);
        return bean.getBeanDefinition();
    }

    private AbstractBeanDefinition buildTransactionDefinition(String dataSourceId, boolean primary) {
        BeanDefinitionBuilder bean = BeanDefinitionBuilder
                .genericBeanDefinition(DataSourceTransactionManager.class);
        bean.addConstructorArgReference(dataSourceId + "DataSource");
        bean.setPrimary(primary);
        return bean.getBeanDefinition();
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = (ConfigurableEnvironment) environment;
    }

    private String formatPropertyName(String propertyName) {
        if (!propertyName.contains("-")) {
            return propertyName;
        }

        return StrUtil.toCamelCase(StrUtil.replaceChars(propertyName, "-", "_"));
    }
}
