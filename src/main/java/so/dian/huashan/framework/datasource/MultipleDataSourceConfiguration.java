package so.dian.huashan.framework.datasource;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.builder.xml.XMLMapperEntityResolver;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.parsing.XNode;
import org.apache.ibatis.parsing.XPathParser;
import org.apache.ibatis.session.Configuration;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/03/10 14:52
 * @description:
 */
@Slf4j
public class MultipleDataSourceConfiguration extends Configuration {

    private final static Map<String, List<String>> dataSourcePackageMap = Maps.newConcurrentMap();

    private final static Map<String, Resource> resourceMap = Maps.newHashMap();

    private final String dataSource;

    MultipleDataSourceConfiguration(String dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public boolean isResourceLoaded(String resourcePath) {

        String mapperName = resourcePath;
        if (isXmlPath(resourcePath)) {
            Resource resource = resourceMap.get(resourcePath);
            XPathParser parser;
            try {
                parser = new XPathParser(resource.getInputStream(), true, getVariables(), new XMLMapperEntityResolver());
            } catch (IOException e) {
                log.error("文件解析异常, file[{}]", resource, e);
                throw new RuntimeException(e);
            }
            XNode xNode = parser.evalNode("/mapper");
            mapperName = xNode.getStringAttribute("namespace");
        }

        List<String> packages = dataSourcePackageMap.get(this.dataSource);
        if (CollUtil.isEmpty(packages)) {
            return true;
        }

        boolean match = false;
        for (String aPackage : packages) {
            if (mapperName.contains(aPackage)) {
                match = true;
                break;
            }
        }

        log.info("xml文件解析，resourcePath: {}, mapperName : {}, packages: {}, match: {}, dataSource: {}", resourcePath, mapperName, JSON.toJSONString(packages), match, dataSource);
        if (match) {
            return super.isResourceLoaded(resourcePath);
        }
        return true;
    }

    @Override
    public void setLogImpl(Class<? extends Log> logImpl) {
        super.setLogImpl(logImpl);
    }

    private boolean isXmlPath(String resourcePath) {

        int start = resourcePath.indexOf("[");
        int end = resourcePath.indexOf("]");

        return start > 0 && end > 0 && resourcePath.contains(".xml");
    }

    public static void addMapperFile(Resource[] mapperLocations) {
        for (Resource mapperLocation : mapperLocations) {
            resourceMap.putIfAbsent(mapperLocation.toString(), mapperLocation);
        }
    }

    public static Resource[] getMapperFile() {
        return resourceMap.values().toArray(new Resource[0]);
    }

    public static void addMap(String dataSource, List<String> packages) {
        dataSourcePackageMap.put(dataSource, packages);
    }
}
