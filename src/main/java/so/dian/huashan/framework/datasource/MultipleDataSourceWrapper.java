package so.dian.huashan.framework.datasource;

import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import com.mysql.cj.conf.ConnectionUrl;

import java.sql.SQLException;
import java.util.Properties;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/16 16:19
 * @description:
 */
public class MultipleDataSourceWrapper extends DruidDataSource {

    private static final String SCHEMA_KEY = "schema";

    private String schema;

    @Override
    public void init() throws SQLException {
        super.init();
        String rawJdbcUrl = getRawJdbcUrl();
        ConnectionUrl conStr = ConnectionUrl.getConnectionUrlInstance(rawJdbcUrl, new Properties());
        this.schema = conStr.getOriginalProperties().get(SCHEMA_KEY);
    }

    @Override
    public DruidPooledConnection getConnection(long maxWaitMillis) throws SQLException {
        DruidPooledConnection connection = super.getConnection(maxWaitMillis);
        if (StrUtil.isNotBlank(schema)) {
            connection.setSchema(schema);
        }

        return connection;
    }
}
