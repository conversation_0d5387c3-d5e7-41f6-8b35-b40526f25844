package so.dian.huashan.framework.cache;

import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;

import java.util.Map;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/22 14:21
 * @description:
 */
public class RedissionCacheExpireManager extends RedissonSpringCacheManager {

    Map<String, CacheConfig> configMap;

    public RedissionCacheExpireManager(RedissonClient redisson, Map<String, CacheConfig> configMap) {
        super(redisson, configMap, new JsonJacksonCodec());
        this.configMap = configMap;
    }

    public RedissionCacheExpireManager(RedissonClient redisson, Map<String, CacheConfig>config, Codec codec) {
        super(redisson, config, codec);
        this.configMap = config;
    }

    public void addConfigMap(String cacheName, CacheConfig cacheConfig) {
        this.configMap.put(cacheName, cacheConfig);
    }
}
