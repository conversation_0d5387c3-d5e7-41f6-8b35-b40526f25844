package so.dian.huashan.framework.cache;

import lombok.AllArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor;
import org.springframework.cache.interceptor.CacheAspectSupport;
import org.springframework.cache.interceptor.CacheOperationSource;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.interceptor.SimpleCacheErrorHandler;
import org.springframework.cache.interceptor.SimpleKeyGenerator;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/25 11:10
 * @description:
 */
@AllArgsConstructor
public class CacheExpirePostProcessor implements InstantiationAwareBeanPostProcessor {

    private ConfigurableListableBeanFactory beanFactory;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof CacheAspectSupport) {
            CacheAspectSupport cacheAspectSupport = (CacheAspectSupport) bean;
            CacheResolver cacheResolver = beanFactory.getBean(CacheResolver.class);
            CacheOperationSource cacheOperationSource = beanFactory.getBean(CacheOperationSource.class);
            cacheAspectSupport.setCacheResolver(cacheResolver);
            cacheAspectSupport.setErrorHandler(new SimpleCacheErrorHandler());
            cacheAspectSupport.setKeyGenerator(new SimpleKeyGenerator());
            cacheAspectSupport.setCacheOperationSource(cacheOperationSource);
        }

        return bean;
    }
}
