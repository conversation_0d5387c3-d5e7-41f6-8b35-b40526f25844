package so.dian.huashan.framework.cache;

import com.google.common.collect.Maps;
import org.redisson.api.RedissonClient;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/22 09:24
 * @description:
 */
@EnableCaching
@Configuration
public class SpringCacheConfig {

    @Bean
    public CacheManager cacheManager(RedissonClient redissonClient) {
        RedissionCacheExpireManager expireManager = new RedissionCacheExpireManager(redissonClient, Maps.newConcurrentMap());
        expireManager.setAllowNullValues(Boolean.FALSE);
        return expireManager;
    }

    @Bean
    public CacheResolver cacheResolver(CacheManager cacheManager) {
        return new SpringCacheExpireResolver(cacheManager);
    }
}
