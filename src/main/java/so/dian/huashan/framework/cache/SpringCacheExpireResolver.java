package so.dian.huashan.framework.cache;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import org.redisson.spring.cache.CacheConfig;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.interceptor.CacheOperationInvocationContext;
import org.springframework.cache.interceptor.SimpleCacheResolver;

import java.lang.reflect.Method;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/12/22 09:26
 * @description:
 */
public class SpringCacheExpireResolver extends SimpleCacheResolver {

    private final Map<Method, CacheExpire> methodCacheExpireMap = Maps.newHashMap();

    public SpringCacheExpireResolver(CacheManager cacheManager) {
        super(cacheManager);
    }

    @Override
    public Collection<? extends Cache> resolveCaches(CacheOperationInvocationContext<?> context) {

        Collection<String> cacheNames = getCacheNames(context);
        if (CollUtil.isEmpty(cacheNames)) {
            return super.resolveCaches(context);
        }

        if (!(getCacheManager() instanceof RedissionCacheExpireManager))
            return super.resolveCaches(context);

        RedissionCacheExpireManager cacheExpireManager = (RedissionCacheExpireManager) getCacheManager();

        Method method = context.getMethod();
        CacheExpire cacheExpire = methodCacheExpireMap.get(method);
        if (Objects.nonNull(cacheExpire)) {
            return super.resolveCaches(context);
        }

        if (!method.isAnnotationPresent(CacheExpire.class))
            return super.resolveCaches(context);

        cacheExpire = method.getAnnotation(CacheExpire.class);
        methodCacheExpireMap.put(method, cacheExpire);

        CacheConfig config = new CacheConfig(cacheExpire.unit().toMillis(cacheExpire.ttl()), cacheExpire.unit().toMillis(cacheExpire.ttl()));
        for (String cacheName : cacheNames) {
            cacheExpireManager.addConfigMap(cacheName, config);
        }
        return super.resolveCaches(context);
    }

}
