package so.dian.huashan.framework.cache;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/22 09:24
 * @description:
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheExpire {

    long ttl() default 60L;

    TimeUnit unit() default TimeUnit.SECONDS;
}
