package so.dian.huashan.framework.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.stereotype.Component;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/25 14:40
 * @description: 如果要使用TypedJsonJacksonCodec作为缓存json格式化，请放开postProcessBeanDefinitionRegistry和CacheJsonInterceptor的代码方法的代码
 */
@Slf4j
@Component
public class CacheExpireBeanFactoryPostProcessor implements BeanDefinitionRegistryPostProcessor {

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        beanFactory.addBeanPostProcessor(new CacheExpirePostProcessor(beanFactory));
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
//        boolean containsed = registry.containsBeanDefinition(CACHE_INTERCEPTOR_NAME);
//        if (containsed) {
//            registry.removeBeanDefinition(CACHE_INTERCEPTOR_NAME);
//
//            BeanDefinitionBuilder beanBuilder = BeanDefinitionBuilder
//                    .genericBeanDefinition(CacheJsonInterceptor.class)
//                    .setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
//
//            registry.registerBeanDefinition(CACHE_INTERCEPTOR_NAME, beanBuilder.getBeanDefinition());
//        }
    }
}
