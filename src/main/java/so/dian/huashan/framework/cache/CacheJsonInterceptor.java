package so.dian.huashan.framework.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.interceptor.CacheInterceptor;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/28 16:45
 * @description: 如果要使用TypedJsonJacksonCodec作为缓存json格式化，请放开CacheExpireBeanFactoryPostProcessor#postProcessBeanDefinitionRegistry和CacheJsonInterceptor的代码方法的代码
 */
@Slf4j
public class CacheJsonInterceptor extends CacheInterceptor {

//    @Override
//    protected Object execute(CacheOperationInvoker invoker, Object target, Method method, Object[] args) {
//        Object cacheValue = super.execute(invoker, target, method, args);
//        if (Objects.isNull(cacheValue))
//            return null;
//
//        if (cacheValue instanceof Map)
//            return BeanUtil.copyProperties(cacheValue, method.getReturnType());
//
//        if (cacheValue instanceof List) {
//            List<?> cacheValues = (List<?>) cacheValue;
//            Class<?> realClass = getRealClass(method);
//            List<Object> returnValues = Lists.newArrayList();
//            for (Object value : cacheValues) {
//                if (ClassUtil.isSimpleValueType(realClass)) {
//                    returnValues.add(value);
//                }else {
//                    returnValues.add(BeanUtil.copyProperties(value, realClass));
//                }
//            }
//            return returnValues;
//        }
//        return cacheValue;
//    }
//
//    public Class<?> getRealClass(Method method) {
//        ParameterizedType genericReturnType = (ParameterizedType) method.getGenericReturnType();
//        return (Class<?>) genericReturnType.getActualTypeArguments()[0];
//    }
}
