package so.dian.huashan.framework.kafka;

import cn.hutool.aop.aspects.Aspect;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

import static so.dian.huashan.common.constant.CacheNameConsts.BRAKE_CACHE_NAME;

@Slf4j
@Component
public class ListenerContainerFactoryAspect implements Aspect {

    private static final String INTERCEPT_METHOD = "createListenerContainer";

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public boolean before(Object target, Method method, Object[] args) {
        return true;
    }

    @Override
    public boolean after(Object target, Method method, Object[] args, Object returnVal) {

        try {
            if (target.getClass().equals(ConcurrentKafkaListenerContainerFactory.class)
                    && INTERCEPT_METHOD.equals(method.getName())) {
                RMap<String, Boolean> brakeMap = redissonClient.getMap(BRAKE_CACHE_NAME);
                if (!brakeMap.isExists())
                    return true;

                ConcurrentMessageListenerContainer<?, ?> listenerContainer = (ConcurrentMessageListenerContainer<?, ?>) returnVal;
                String consumerId = listenerContainer.getListenerId();
                Boolean brakeFlag = brakeMap.get(consumerId);
                if (Objects.nonNull(brakeFlag) && brakeFlag) {
                    listenerContainer.pause();
                    log.info("监听器制动处理成功，消费者ID：{}", consumerId);
                }
            }
        }catch (Exception e) {
            log.error("启动时制动处理异常，不做制动处理", e);
        }
        return true;
    }

    @Override
    public boolean afterException(Object target, Method method, Object[] args, Throwable e) {
        return true;
    }
}
