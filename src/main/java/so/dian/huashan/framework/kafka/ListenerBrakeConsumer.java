package so.dian.huashan.framework.kafka;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static so.dian.huashan.common.constant.CacheNameConsts.BRAKE_CACHE_NAME;

@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.listener-brake.topic}",
        consumerGroup = "${rocketmq.listener-brake.group}",
        selectorExpression = "${rocketmq.listener-brake.tag}", messageModel = MessageModel.BROADCASTING)
public class ListenerBrakeConsumer implements RocketMQListener<MessageExt> {

    @Autowired
    private KafkaListenerEndpointRegistry registry;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public void onMessage(MessageExt message) {
        String consumerId = new String(message.getBody());
        log.info("制动消息请求参数：{}", consumerId);
        if (StrUtil.isBlank(consumerId))
            return;

        MessageListenerContainer listenerContainer = registry.getListenerContainer(consumerId);
        if (Objects.isNull(listenerContainer)) {
            log.error("消费者[{}]不存在", consumerId);
            return;
        }

        RMap<String, Boolean> brakeFlagMap = redissonClient.getMap(BRAKE_CACHE_NAME);
        Boolean pause = brakeFlagMap.get(consumerId);
        if (!brakeFlagMap.isExists() || Objects.isNull(pause)) {
            log.debug("消费者[{}]制动处理，对应的缓存不存在", consumerId);
            return;
        }

        if (pause && !listenerContainer.isPauseRequested()) {
            listenerContainer.pause();
            log.info("消费者[{}]暂停成功", consumerId);
        }

        if (!pause && listenerContainer.isPauseRequested()) {
            listenerContainer.resume();
            log.info("消费者[{}]唤醒成功", consumerId);
        }
    }
}
