package so.dian.huashan.framework.kafka;

import cn.hutool.aop.aspects.Aspect;
import cn.hutool.aop.proxy.SpringCglibProxyFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.stereotype.Component;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/04/17 18:26
 * @description:
 */
@Component
public class ListenerContainerFactoryPostProcessor implements BeanPostProcessor {

    @Autowired
    private ApplicationContext context;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean.getClass().equals(ConcurrentKafkaListenerContainerFactory.class)) {
            SpringCglibProxyFactory proxyFactory = new SpringCglibProxyFactory();
            return proxyFactory.proxy(bean, context.getBean(Aspect.class));
        }
        return bean;
    }
}
