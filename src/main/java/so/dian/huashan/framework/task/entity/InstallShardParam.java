package so.dian.huashan.framework.task.entity;


import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/04/20 10:53
 * @description:
 */
@Builder
@Getter
public class InstallShardParam {

    private InstallJobParam jobParam;

    private Long pageNo;

    private Integer pageSize;

    private Map<String, Object> extParam;

    public void putExtParam(String key, Object value) {
        if (Objects.isNull(extParam))
            extParam = Maps.newHashMap();

        extParam.put(key, value);
    }

    public <V>V getExtParam(String key) {
        return (V) extParam.get(key);
    }
}
