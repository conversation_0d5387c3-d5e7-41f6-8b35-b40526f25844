package so.dian.huashan.framework.task.mq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.processor.ShardCompleteProcessor;

import java.util.List;
import java.util.Map;

import static so.dian.huashan.common.exception.LogMarkerFactory.SHARD_JOB_MARKER;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/12/19 14:07
 * @description:
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "${rocketmq.shard-task.topic}",
        consumerGroup = "${rocketmq.consumer.group}",
        selectorExpression = "${rocketmq.shard-task.tag}")
public class ShardCompleteListener implements RocketMQListener<MessageExt>, ApplicationContextAware {

    private final Map<String, List<ShardCompleteProcessor>> jobNameAndProcessorMap = Maps.newHashMap();

    @Override
    public void onMessage(MessageExt message) {

        log.info(SHARD_JOB_MARKER, "接收到任务执行完成消息通知，消息ID:{}", message.getMsgId());
        ShardCompleteMessage completeMessage = ShardCompleteMessage.parse(new String(message.getBody()));
        List<ShardCompleteProcessor> postProcessors = jobNameAndProcessorMap.get(completeMessage.getJobName());
        if (CollUtil.isEmpty(postProcessors)) {
            log.info(SHARD_JOB_MARKER, "接收到消息但是没有获取到任务对应的后置处理器,任务名称:{}", completeMessage.getJobName());
            return;
        }

        for (ShardCompleteProcessor postProcessor : postProcessors) {
            try {
                log.info(SHARD_JOB_MARKER, "开始执行后置处理器，处理器名称:{},任务名称:{}",postProcessor.getClass().getSimpleName(), completeMessage.getJobName());
                postProcessor.process(completeMessage);
            }catch (Throwable e) {
                log.warn(SHARD_JOB_MARKER, "执行分片任务后置处理器失败，名称:[{}]", postProcessor.getClass().getName(), e);
            }
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ShardCompleteProcessor> postProcessorMap = applicationContext.getBeansOfType(ShardCompleteProcessor.class);
        if (CollUtil.isEmpty(postProcessorMap))
            return;

        for (ShardCompleteProcessor postProcessor : postProcessorMap.values()) {
            String jobName = postProcessor.jobName();
            if (StrUtil.isBlank(jobName))
                continue;

            List<ShardCompleteProcessor> postProcessors = jobNameAndProcessorMap.computeIfAbsent(jobName, key -> Lists.newArrayList());
            postProcessors.add(postProcessor);
        }
    }
}
