package so.dian.huashan.framework.task.entity;

import lombok.Getter;

import java.util.Objects;
import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/13 18:32
 * @description:
 */
public class ShardTaskProperties {

    @Getter
    private final String jobName;

    /**
     * 分页查询大小
     */
    private Integer pageSize;

    /**
     * 一个分片存放多少个业务数据ID
     */
    private Integer shardSize;

    /**
     * 生成分片过程中，当内容中积累多少个分片数据就存储一次
     */
    private Integer flushSize;

    /**
     * 循环执行多少次告警
     */
    private Integer maxLoop;

    public ShardTaskProperties(String jobName) {
        this.jobName = Objects.requireNonNull(jobName, "jobName must not null");
    }

    public ShardTaskProperties pageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public ShardTaskProperties shardSize(Integer shardSize) {
        this.shardSize = shardSize;
        return this;
    }

    public ShardTaskProperties flushSize(Integer flushSize) {
        this.flushSize = flushSize;
        return this;
    }

    public ShardTaskProperties maxLoop(Integer maxLoop) {
        this.maxLoop = maxLoop;
        return this;
    }

    public Integer getPageSize() {
        return Optional.ofNullable(pageSize).orElse(10000);
    }

    public Integer getShardSize() {
        return Optional.ofNullable(shardSize).orElse(500);
    }

    public Integer getFlushSize() {
        return Optional.ofNullable(flushSize).orElse(1000);
    }

    public Integer getMaxLoop() {
        return Optional.ofNullable(maxLoop).orElse(10000000);
    }
}
