package so.dian.huashan.framework.task;

import cn.hutool.core.util.StrUtil;
import lombok.NonNull;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.ExecuteShardContext;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardRetryJobParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/04/14 10:12
 * @description:
 */
public abstract class ShardTaskService {

    @Autowired
    protected RedissonClient redissonClient;

    private final static String SHARD_COUNT_PREFIX = "huashan:shardCount";

    final static String EXECUTE_LOCK_PREFIX = "huashan:shardExe:";

    final static String EXECUTING_MASTER_ID = "huashan:master:id:";

    protected ShardTaskProperties taskProperties;

    ShardTaskService(@NonNull ShardTaskProperties taskProperties) {
        this.taskProperties = taskProperties;
    }

    protected boolean tryLock(String lockPrefix, String jobName) {
        RLock lock = redissonClient.getLock(StrUtil.join(StrUtil.COLON, lockPrefix, jobName));
        return lock.tryLock();
    }

    protected void lock(String lockPrefix, String jobName) {
        RLock lock = redissonClient.getLock(StrUtil.join(StrUtil.COLON, lockPrefix, jobName));
        lock.lock(10, TimeUnit.SECONDS);
    }

    protected void unlock(String lockPrefix, String jobName) {
        RLock lock = redissonClient.getLock(StrUtil.join(StrUtil.COLON, lockPrefix, jobName));
        lock.unlock();
    }

    protected void setShardCount(Long masterId, Long shardCount) {
        RAtomicLong shardBucket = redissonClient.getAtomicLong(StrUtil.join(StrUtil.COLON, SHARD_COUNT_PREFIX, taskProperties.getJobName(), masterId));
        shardBucket.set(shardCount);
        shardBucket.expire(3, TimeUnit.DAYS);
    }

    protected Long decrementShardCount(Long masterId) {
        RAtomicLong shardBucket = redissonClient.getAtomicLong(StrUtil.join(StrUtil.COLON, SHARD_COUNT_PREFIX, taskProperties.getJobName(), masterId));
        if (!shardBucket.isExists())
            return 0L;

        long count = shardBucket.decrementAndGet();
        if (count == 0) {
            shardBucket.delete();
        }
        return count;
    }


    public void retry(ShardRetryJobParam retryJobParam, Consumer<ExecuteShardParam> bizProcessService) {
        throw new UnsupportedOperationException("[" + getClass().getName() + "]不支持该方法");
    }

    public ExecuteShardContext executeShardTask(ExecuteJobParam jobParam, Consumer<ExecuteShardParam> bizProcessService) {
        throw new UnsupportedOperationException("[" + getClass().getName() + "]不支持该方法");
    }

    public Long installShardTask(InstallJobParam jobParam, Function<InstallShardParam, List<String>> findIdsFunc) {
        throw new UnsupportedOperationException("[" + getClass().getName() + "]不支持该方法");
    }
}

