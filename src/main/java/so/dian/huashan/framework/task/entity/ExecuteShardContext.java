package so.dian.huashan.framework.task.entity;

import lombok.Data;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/05/25 11:08
 * @description: 分片执行上下文
 */
@Data
public class ExecuteShardContext {

    private Boolean needSendCompleteMsg;

    private TaskMasterDO taskMaster;

    private final ExecuteJobParam jobParam;

    private Date startTime;

    private CompletableFuture<TaskStatusEnum> future = new CompletableFuture<>();

    public ExecuteShardContext(ExecuteJobParam jobParam) {
        this.jobParam = Objects.requireNonNull(jobParam, "jobParam must not null");
        this.needSendCompleteMsg = Boolean.FALSE;
    }

    public boolean isExecutable() {
        if (Objects.isNull(taskMaster) || !taskMaster.getStatus().equals(TaskStatusEnum.IN_EXECUTE.code()))
            return Boolean.FALSE;
        startTime = DateUtil.getNow();
        return Boolean.TRUE;
    }

    public Long getTaskMasterId() {
        if (Objects.nonNull(jobParam.getTaskMasterId()))
            return jobParam.getTaskMasterId();
        if (Objects.nonNull(taskMaster))
            return taskMaster.getId();
        return null;
    }

    public List<Integer> getSubStatuses() {
        return Optional.ofNullable(jobParam.getTaskSubStatuses()).orElse(Collections.singletonList(TaskSubStatusEnum.INIT.code()));
    }

    public Boolean isNeedSendCompleteMsg() {
        return this.needSendCompleteMsg;
    }

    public void needSendCompleteMsg() {
        this.needSendCompleteMsg = Boolean.TRUE;
    }

    public void complete(TaskStatusEnum status) {
        this.future.complete(status);
    }

    public TaskStatusEnum getExitStatus() throws ExecutionException, InterruptedException {
        return this.future.get();
    }
}
