package so.dian.huashan.framework.task.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;
import so.dian.huangshan.service.val.UseBaseParam;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/14 09:46
 * @description:
 */
@Setter
@Getter
public class ExecuteJobParam extends UseBaseParam {

    private Long taskMasterId;

    private List<Integer> taskSubStatuses;

    public ExecuteJobParam() {
        super(StrUtil.EMPTY, StrUtil.EMPTY);
    }

    public ExecuteJobParam(String batchNo, String taskCode) {
        super(batchNo, taskCode);
    }
}
