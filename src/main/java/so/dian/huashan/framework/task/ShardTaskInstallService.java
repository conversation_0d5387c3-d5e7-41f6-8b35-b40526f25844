package so.dian.huashan.framework.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.huashan.collection.facade.BizDomainFacade;
import so.dian.huashan.collection.service.entity.BizDomainBO;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.task.processor.ShardInstallProcessor;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.job.dto.TaskSubDTO;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import static so.dian.huashan.common.exception.LogMarkerFactory.SHARD_JOB_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/05/12 17:57
 * @description:
 */
@Slf4j
public class ShardTaskInstallService extends ShardTaskService {

    @Autowired
    private TaskMasterMapper taskMasterMapper;

    @Autowired
    private TaskSubMapper taskSubMapper;

    @Autowired
    private TaskRegistryMapper taskRegistryMapper;

    @Autowired
    private BizDomainFacade bizDomainFacade;

    private final List<ShardInstallProcessor> processors = Lists.newArrayList();

    ShardTaskInstallService(@NonNull ShardTaskProperties taskProperties) {
        super(taskProperties);
    }

    @Override
    public Long installShardTask(InstallJobParam jobParam, Function<InstallShardParam, List<String>> findIdsFunc) {

        log.info(SHARD_JOB_MARKER, "开始安装分片任务[{}]，参数：{}", taskProperties.getJobName(), JSON.toJSONString(jobParam));
        boolean tryLock = tryLock("install", taskProperties.getJobName());
        if (!tryLock)
            return null;

        try {
            if (!preCheck(jobParam)) {
                log.warn(SHARD_JOB_MARKER, "任务分片安装前置校验没通过,任务名称:{}", taskProperties.getJobName());
                return null;
            }

            TaskMasterDO masterDO = start();
            Date start = DateUtil.getNow();
            doInstallShardTask(jobParam, masterDO.getId(), findIdsFunc);
            long shardCount = complete(masterDO);
            log.info(SHARD_JOB_MARKER, "结束安装分片任务[{}]，总的分片数：{}, 耗时: {}", taskProperties.getJobName(), shardCount, DateUtil.formatBetween(start, DateUtil.getNow()));
            return masterDO.getId();
        }catch (Throwable e) {
            log.error(SHARD_JOB_MARKER, "生成分片异常，请求参数：{}", JSON.toJSONString(jobParam), e);
        }finally {
            unlock("install", taskProperties.getJobName());
        }

        return null;
    }

    private boolean preCheck(InstallJobParam jobParam) {
        TaskRegistryDO taskRegistryDO = taskRegistryMapper.selectByName(taskProperties.getJobName());
        if (Objects.isNull(taskRegistryDO)) {
            log.warn(SHARD_JOB_MARKER, "分片任务安装校验到任务没有注册，任务{}", taskProperties.getJobName());
            return Boolean.FALSE;
        }
        jobParam.putExtParam("taskRegistryId",taskRegistryDO.getId());
        boolean check = Boolean.TRUE;
        for (ShardInstallProcessor processor : processors) {
            if (!check)
                return Boolean.FALSE;
            check = processor.preProcess(jobParam);
        }
        return check;
    }

    private TaskMasterDO start() {
        TaskRegistryDO taskRegistryDO = taskRegistryMapper.selectByName(taskProperties.getJobName());
        BizDomainBO bizDomainBO = bizDomainFacade.find(taskRegistryDO.getBizDomainId());
        TaskMasterDO taskMasterDO = new TaskMasterDO();
        taskMasterDO.setTaskRegistryId(taskRegistryDO.getId());
        taskMasterDO.setBatchNo(StrUtil.join(StrUtil.UNDERLINE, bizDomainBO.getCode(), System.currentTimeMillis()));
        taskMasterDO.setStep(taskProperties.getShardSize());
        taskMasterDO.setInvokeDate(DateUtil.parseDateYyyyMMdd2Int(new Date()));
        taskMasterDO.setSuccessTimes(0L);
        taskMasterDO.setFailTimes(0L);
        taskMasterDO.setStatus(TaskStatusEnum.PROGRESS.code());
        taskMasterMapper.insertSelective(taskMasterDO);

        return taskMasterDO;
    }

    private void doInstallShardTask(InstallJobParam jobParam, Long taskMasterId, Function<InstallShardParam, List<String>> findIdsFunc) {
        long pageNo = 1;
        InstallShardParam shardParam = InstallShardParam.builder()
                .jobParam(Optional.ofNullable(jobParam).map(InstallJobParam::copy).orElse(null))
                .extParam(Maps.newHashMap())
                .pageNo(pageNo).pageSize(taskProperties.getPageSize()).build();
        List<String> bizIds = findIdsFunc.apply(shardParam);

        List<TaskSubDO> taskSubShards = Lists.newArrayList();
        int loopCount = 0;
        while (CollUtil.isNotEmpty(bizIds)) {
            if (++ loopCount == taskProperties.getMaxLoop()) {
                log.warn(SHARD_JOB_MARKER, "分片生成循环次数已达上限，存在死循环的可能性");
            }
            List<List<String>> partitionId = Lists.partition(bizIds, taskProperties.getShardSize());
            for (List<String> ids : partitionId) {
                TaskSubDO taskSubDO = new TaskSubDO();
                taskSubDO.setTaskMasterId(taskMasterId);
                taskSubDO.setVoucher(JSON.toJSONString(ids));
                taskSubDO.setStatus(TaskSubStatusEnum.INIT.code());
                taskSubShards.add(taskSubDO);
            }

            log.info(SHARD_JOB_MARKER, "任务分片中，当前已推移到第{}页", pageNo);
            shardParam = InstallShardParam.builder()
                    .jobParam(shardParam.getJobParam())
                    .extParam(shardParam.getExtParam())
                    .pageNo(++ pageNo).pageSize(taskProperties.getPageSize()).build();
            bizIds = findIdsFunc.apply(shardParam);

            if (taskSubShards.size() >= taskProperties.getFlushSize() || CollUtil.isEmpty(bizIds)) {
                log.info(SHARD_JOB_MARKER, "任务分片中, 做一次分片写入，当前已推移到第{}页", pageNo - 1);
                taskSubMapper.insertBatch(taskSubShards);
                taskSubShards.clear();
            }
        }
    }

    private long complete(TaskMasterDO masterDO) {

        List<TaskSubDTO> statistics = taskSubMapper.statistics(masterDO.getId());
        long allShardCount = statistics.stream().mapToLong(TaskSubDTO::getCount).sum();
        setShardCount(masterDO.getId(), allShardCount);

        masterDO.setStatus(TaskStatusEnum.BURST_SUCCESS.code());
        masterDO.setAllTimes(allShardCount);
        taskMasterMapper.updateByPrimaryKeySelective(masterDO);

        for (ShardInstallProcessor processor : processors) {
            processor.postProcess(masterDO);
        }
        return allShardCount;
    }

    public void addProcessor(ShardInstallProcessor preProcessor) {
        if (Objects.isNull(preProcessor))
            return;
        processors.add(preProcessor);
    }
}
