package so.dian.huashan.framework.task.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import lombok.Getter;
import lombok.Setter;
import so.dian.huangshan.service.val.UseBaseParam;

import java.util.Map;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/13 17:32
 * @description:
 */
@Setter
@Getter
public class InstallJobParam extends UseBaseParam {

    private Map<String, Object> extParam;

    public InstallJobParam() {
        this(StrUtil.EMPTY, StrUtil.EMPTY);
    }

    public InstallJobParam(String batchNo, String taskCode) {
        super(batchNo, taskCode);
        this.extParam = Maps.newHashMap();
    }

    public void putExtParam(String key, Object value) {
        extParam.put(key, value);
    }

    public <V>V getExtParam(String key) {
        return (V) extParam.get(key);
    }

    public InstallJobParam copy() {
        InstallJobParam jobParam = new InstallJobParam(this.getBatchNo(), this.getTaskCode());
        BeanUtil.copyProperties(this, jobParam);
        return jobParam;
    }
}
