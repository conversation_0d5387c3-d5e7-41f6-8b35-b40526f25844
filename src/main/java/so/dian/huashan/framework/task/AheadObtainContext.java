package so.dian.huashan.framework.task;

import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/18 15:14
 * @description:
 */
public class AheadObtainContext implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext applicationContext) {
        ShardTaskFactory.applicationContext = applicationContext;
    }
}
