package so.dian.huashan.framework.task.processor;

import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/13 17:47
 * @description:
 */
public abstract class ShardInstallProcessor implements ShardProcessor {

    public boolean preProcess(InstallJobParam param) {
        return Boolean.TRUE;
    }

    public void postProcess(TaskMasterDO taskMaster) {}
}
