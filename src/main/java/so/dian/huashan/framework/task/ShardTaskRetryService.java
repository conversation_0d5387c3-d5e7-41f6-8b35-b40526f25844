package so.dian.huashan.framework.task;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.xxl.job.core.util.ShardingUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.ShardRetryJobParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.job.dto.TaskSubDTO;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.mapper.entity.TaskSubDO;
import so.dian.huashan.task.mapper.example.IdHashExample;
import so.dian.huashan.task.mapper.example.TaskSubExample;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static so.dian.huashan.common.exception.LogMarkerFactory.SHARD_JOB_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/12/19 09:30
 * @description: 重试任务跟分片执行任务在逻辑上有大部分是重叠的，是否有存在的必要性？
 */
@Slf4j
public class ShardTaskRetryService extends ShardTaskService {

    @Autowired
    private TaskRegistryMapper taskRegistryMapper;

    @Autowired
    private TaskSubMapper taskSubMapper;

    @Autowired
    private TaskMasterMapper taskMasterMapper;

    ShardTaskRetryService(@NonNull ShardTaskProperties taskProperties) {
        super(taskProperties);
    }

    @Override
    public void retry(ShardRetryJobParam retryJobParam, Consumer<ExecuteShardParam> bizProcessService) {

        log.info(SHARD_JOB_MARKER, "开始执行分片重试任务，参数:{}", JSON.toJSONString(retryJobParam));
        if (!preCheck(retryJobParam) || Objects.isNull(bizProcessService))
            return;

        List<Integer> taskSubStatuses = Lists.newArrayList();
        if (CollUtil.isNotEmpty(retryJobParam.getStatuses())) {
            taskSubStatuses.addAll(retryJobParam.getStatuses());
        }else {
            taskSubStatuses.add(TaskSubStatusEnum.FAIL.code());
        }

        long pageNo = 1;
        TaskSubExample subExample = TaskSubExample.builder()
                .taskMasterId(retryJobParam.getTaskMasterId())
                .ids(retryJobParam.getTaskSubIds())
                .statuses(taskSubStatuses)
                .page(PageRequest.of(pageNo, 100))
                .idHash(new IdHashExample(ShardingUtil.getShardingVo().getTotal(), ShardingUtil.getShardingVo().getIndex()))
                .build();
        List<TaskSubDO> taskSubDOS = taskSubMapper.selectByExample(subExample);

        int loopCount = 0;
        Set<Long> taskMasterIds = Sets.newHashSet();
        while (CollUtil.isNotEmpty(taskSubDOS)) {

            if (++ loopCount == taskProperties.getMaxLoop()) {
                log.warn(SHARD_JOB_MARKER, "分片重试任务达到最大次数，存在死循环的可能性");
            }

            for (TaskSubDO taskSub : taskSubDOS) {
                int result = taskSubMapper.updateStatusByPrimaryKey(taskSub.getId(), taskSub.getStatus(), TaskSubStatusEnum.PROGRESS.code());
                if (result <= 0)
                    continue;

                TaskSubDO modify = new TaskSubDO();
                modify.setId(taskSub.getId());
                try {
                    List<String> bizIds = JSON.parseArray(taskSub.getVoucher(), String.class);
                    // 执行业务逻辑
                    ExecuteShardParam shardParam = ExecuteShardParam.builder()
                            .bizIds(bizIds)
                            .subTaskId(taskSub.getId())
                            .batchNo(retryJobParam.getBatchNo())
                            .taskMasterId(taskSub.getTaskMasterId())
                            .build();
                    bizProcessService.accept(shardParam);
                    modify.setStatus(TaskSubStatusEnum.SUCCESS.code());
                } catch (Exception e) {
                    log.warn(SHARD_JOB_MARKER, "分片任务执行,业务处理失败,分片任务ID[{}]", taskSub.getId(), e);
                    modify.setStatus(TaskSubStatusEnum.FAIL.code());
                }
                modify.modify();
                taskSubMapper.updateByPrimaryKeySelective(modify);
                taskMasterIds.add(taskSub.getTaskMasterId());
            }

            subExample = TaskSubExample.builder()
                    .taskMasterId(retryJobParam.getTaskMasterId())
                    .ids(retryJobParam.getTaskSubIds())
                    .statuses(Collections.singletonList(TaskSubStatusEnum.FAIL.code()))
                    .page(PageRequest.of(++ pageNo, 100))
                    .idHash(new IdHashExample(ShardingUtil.getShardingVo().getTotal(), ShardingUtil.getShardingVo().getIndex()))
                    .build();
            taskSubDOS = taskSubMapper.selectByExample(subExample);
        }

        complete(Lists.newArrayList(taskMasterIds));
        log.info(SHARD_JOB_MARKER, "任务重试结束, 任务名称:{}", taskProperties.getJobName());
    }

    private boolean preCheck(ShardRetryJobParam retryJobParam) {
        if (Objects.isNull(retryJobParam) || !retryJobParam.validate()) {
            log.error(SHARD_JOB_MARKER, "重试任务参数校验没通过");
            return Boolean.FALSE;
        }

        TaskRegistryDO taskRegistryDO = taskRegistryMapper.selectByName(taskProperties.getJobName());
        if (Objects.isNull(taskRegistryDO)) {
            log.error(SHARD_JOB_MARKER, "重试任务查询不到相关的任务注册信息，任务名称:[{}]", taskProperties.getJobName());
            return Boolean.FALSE;
        }

        List<Long> taskMasterIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(retryJobParam.getTaskSubIds())) {
            List<Long> masterIds = taskSubMapper.selectMasterIdBySubIds(retryJobParam.getTaskSubIds());
            if (CollUtil.isEmpty(masterIds)) {
                log.error(SHARD_JOB_MARKER, "任务重试执行指定的子任务查询不到主任务ID");
                return Boolean.FALSE;
            }
            taskMasterIds.addAll(masterIds);
        }

        if (Objects.nonNull(retryJobParam.getTaskMasterId()))
            taskMasterIds.add(retryJobParam.getTaskMasterId());

        List<TaskMasterDO> taskMasterDOS = taskMasterMapper.selectByPrimaryKeys(taskMasterIds);
        if (CollUtil.isEmpty(taskMasterDOS)) {
            log.error(SHARD_JOB_MARKER, "重试任务时查询不到主任务信息");
            return Boolean.FALSE;
        }

        for (TaskMasterDO taskMasterDO : taskMasterDOS) {
            if (!taskRegistryDO.getId().equals(taskMasterDO.getTaskRegistryId())) {
                log.error(SHARD_JOB_MARKER, "重试任务时存在非法的任务注册信息ID");
                return Boolean.FALSE;
            }
        }

        return Boolean.TRUE;
    }

    private void complete(List<Long> taskMasterIds) {
        if (CollUtil.isEmpty(taskMasterIds))
            return;

        List<TaskSubDTO> statistics = taskSubMapper.statisticsByMasterIds(taskMasterIds);
        Map<Long, List<TaskSubDTO>> masterIdAdStatisticsMap = statistics.stream().collect(Collectors.groupingBy(TaskSubDTO::getTaskMasterId));

        List<TaskMasterDO> taskMasterDOS = Lists.newArrayList();
        masterIdAdStatisticsMap.forEach((taskMasterId, taskSubDTOS) -> {

            List<TaskSubDTO> subDTOS = taskSubDTOS.stream()
                    .filter(taskSubDTO -> TaskSubStatusEnum.FAIL.code().equals(taskSubDTO.getStatus()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(subDTOS)) {
                TaskMasterDO taskMasterDO = new TaskMasterDO();
                taskMasterDO.setId(taskMasterId);
                taskMasterDO.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
                taskMasterDOS.add(taskMasterDO);
            }
        });

        if (CollUtil.isNotEmpty(taskMasterDOS))
            taskMasterMapper.batchUpdateStatus(taskMasterDOS);
    }
}
