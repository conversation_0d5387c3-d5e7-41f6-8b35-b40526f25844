package so.dian.huashan.framework.task;

import lombok.extern.slf4j.Slf4j;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huangshan.service.TaskParamRecordService;
import so.dian.huangshan.service.val.UseBaseParam;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.ExecuteShardContext;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.task.entity.ShardTaskResult;
import so.dian.huashan.task.enums.TaskStatusEnum;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;

import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;

@Slf4j
public abstract class ShardParamRecordService implements TaskParamRecordService<UseBaseParam, ShardTaskResult> {

    private ShardTaskInstallService installTaskService;
    private ShardTaskExecuteService executeTaskService;

    private ShardTaskProperties shardTaskProperties;

    @PostConstruct
    public void init() {
        ShardTaskProperties properties = shardTaskProperties();
        installTaskService = ShardTaskFactory.createInstallService(properties);
        executeTaskService = ShardTaskFactory.createExecuteService(properties);
    }

    @Override
    public ShardTaskResult execute(UseBaseParam param) {

        if (param instanceof InstallJobParam) {
            InstallJobParam jobParam = (InstallJobParam) param;
            Long taskMasterId = installTaskService.installShardTask(jobParam, findIdsFunc());
            if (Objects.isNull(taskMasterId)) {
                throw BizException.create(SHARD_TASK_FAIL);
            }
            return new ShardTaskResult(taskMasterId);
        }else if (param instanceof ExecuteJobParam) {
            ExecuteJobParam jobParam = (ExecuteJobParam) param;
            ExecuteShardContext shardContext = executeTaskService.executeShardTask(jobParam, bizProcessService());

            try {
                TaskStatusEnum taskStatus = shardContext.getExitStatus();
                if (TaskStatusEnum.EXECUTE_FAIL.equals(taskStatus))
                    throw BizException.create(SHARD_TASK_FAIL);
            }catch (Exception e) {
                log.error("分片任务执行失败，taskMasterId:{}", shardContext.getTaskMasterId(), e);
                throw BizException.create(SHARD_TASK_FAIL);
            }

            return null;
        }else {
            throw BizException.create(SHARD_TASK_FAIL);
        }
    }

    public ShardTaskProperties shardTaskProperties() {
        if (Objects.isNull(shardTaskProperties))
            shardTaskProperties = getProperties();

        return shardTaskProperties;
    }

    protected abstract ShardTaskProperties getProperties();

    protected abstract Function<InstallShardParam, List<String>> findIdsFunc();

    protected abstract Consumer<ExecuteShardParam> bizProcessService();
}
