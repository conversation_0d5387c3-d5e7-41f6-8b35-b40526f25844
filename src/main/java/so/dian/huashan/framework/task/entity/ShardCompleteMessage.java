package so.dian.huashan.framework.task.entity;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/19 14:15
 * @description:
 */
@Builder
@Data
public class ShardCompleteMessage {

    private String jobName;

    private Long taskMasterId;

    private Long allShardCount;

    private Long successShardCount;

    private Long failShardCount;

    public static ShardCompleteMessage parse(String message) {
        JSONObject jsonObject = JSON.parseObject(message);
        return BeanUtil.copyProperties(jsonObject, ShardCompleteMessage.class);
    }
}
