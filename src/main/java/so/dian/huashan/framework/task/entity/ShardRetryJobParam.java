package so.dian.huashan.framework.task.entity;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/19 09:25
 * @description:
 */
@Data
public class ShardRetryJobParam {

    private Long taskMasterId;

    private List<Long> taskSubIds;

    private List<Integer> statuses;

    private String batchNo;

    public boolean validate() {
        if (Objects.isNull(taskMasterId) && CollUtil.isEmpty(taskSubIds))
            return Boolean.FALSE;

        return Boolean.TRUE;
    }
}
