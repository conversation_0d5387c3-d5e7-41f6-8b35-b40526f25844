package so.dian.huashan.framework.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.util.ShardingUtil;
import io.opentelemetry.context.Context;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.ExecuteShardContext;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.job.dto.TaskSubDTO;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.mapper.entity.TaskSubDO;
import so.dian.huashan.task.mapper.example.IdHashExample;
import so.dian.huashan.task.mapper.example.TaskMasterExample;
import so.dian.huashan.task.mapper.example.TaskSubExample;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SHARD_JOB_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/05/12 18:01
 * @description:
 */
@Slf4j
public class ShardTaskExecuteService extends ShardTaskService {

    @Autowired
    private TaskMasterMapper taskMasterMapper;

    @Autowired
    private TaskSubMapper taskSubMapper;

    @Autowired
    private TaskRegistryMapper taskRegistryMapper;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.shard-task.topic}")
    private String topic;

    @Value("${rocketmq.shard-task.tag}")
    private String tag;

    private final AsyncTaskExecutor taskExecutor;

    ShardTaskExecuteService(AsyncTaskExecutor taskExecutor, ShardTaskProperties taskProperties) {
        super(taskProperties);
        this.taskExecutor = taskExecutor;
    }

    @Override
    public ExecuteShardContext executeShardTask(ExecuteJobParam jobParam, Consumer<ExecuteShardParam> bizProcessService) {
        log.info(SHARD_JOB_MARKER, "开始执行分片任务[{}]", taskProperties.getJobName());
        lock(EXECUTE_LOCK_PREFIX, taskProperties.getJobName());
        ExecuteShardContext shardContext = null;
        try {
            shardContext = startTask(jobParam);
        }catch (Exception e) {
            log.error(SHARD_JOB_MARKER, "任务启动失败，任务名称：{}", taskProperties.getJobName(), e);
            throw BizException.create(SHARD_TASK_FAIL);
        }finally {
            unlock(EXECUTE_LOCK_PREFIX, taskProperties.getJobName());
        }

        if (!shardContext.isExecutable()) {
            log.warn("任务启动失败, 参数不合法，context: {}", JSON.toJSONString(shardContext));
            throw BizException.create(SHARD_TASK_FAIL);
        }

        if (shardContext.getTaskMaster().getAllTimes() <= 0) {
            shardContext.needSendCompleteMsg();
            complete(shardContext);
            log.info(SHARD_JOB_MARKER, "任务没有分片子任务,直接结束,任务名称:{}", taskProperties.getJobName());
            return shardContext;
        }

        Date start = DateUtil.getNow();
        TaskSubDO taskShard = findOneShard(shardContext);
        int loopCount = 0;
        Context context = Context.current();
        while (Objects.nonNull(taskShard)) {
            if (++ loopCount == taskProperties.getMaxLoop()) {
                log.warn(SHARD_JOB_MARKER, "分片执行已达到最大次数，存在死循环的可能性");
            }
            try {
                int result = taskSubMapper.updateStatusByPrimaryKey(taskShard.getId(), taskShard.getStatus(), TaskSubStatusEnum.PROGRESS.code());
                if (result > 0) {
                    ShardTask shardTask = new ShardTask(shardContext, taskShard, bizProcessService);
                    taskExecutor.submit(context.wrap(shardTask));
                }
                taskShard = findOneShard(shardContext);
            } catch (Throwable e) {
                taskSubMapper.updateStatusByPrimaryKey(taskShard.getId(), taskShard.getStatus(), TaskSubStatusEnum.FAIL.code());
            }
        }

        log.info(SHARD_JOB_MARKER, "分片任务已全部提交[{}],, 耗时:{}", taskProperties.getJobName(), DateUtil.formatBetween(start, DateUtil.getNow()));
        return shardContext;
    }

    private ExecuteShardContext startTask(ExecuteJobParam jobParam) {

        String masterIdKey = StrUtil.join(StrUtil.COLON, EXECUTING_MASTER_ID, taskProperties.getJobName());
        RBucket<Long> masterIdBucket = redissonClient.getBucket(masterIdKey);
        if (masterIdBucket.isExists()) {
            Long masterId = masterIdBucket.get();
            if (Objects.isNull(jobParam.getTaskMasterId())) {
                jobParam.setTaskMasterId(masterId);
            }
        }

        ExecuteShardContext context = new ExecuteShardContext(jobParam);
        TaskMasterDO taskMaster;
        if (Objects.nonNull(jobParam.getTaskMasterId())) {
            taskMaster = taskMasterMapper.selectByPrimaryKey(jobParam.getTaskMasterId());
        } else {

            TaskRegistryDO taskRegistryDO = taskRegistryMapper.selectByName(taskProperties.getJobName());
            if (Objects.isNull(taskRegistryDO)) {
                log.warn(SHARD_JOB_MARKER, "分片任务执行查询不到任务注册信息，任务{}", taskProperties.getJobName());
                return context;
            }

            TaskMasterExample example = TaskMasterExample.builder()
                    .taskRegistryId(taskRegistryDO.getId())
                    .statuses(Collections.singletonList(TaskStatusEnum.BURST_SUCCESS.code()))
                    .build();
            List<TaskMasterDO> taskMasters = taskMasterMapper.selectByExample(example);
            taskMaster = CollUtil.getFirst(taskMasters);
        }
        context.setTaskMaster(taskMaster);

        if (!Objects.isNull(taskMaster) && taskMaster.getStatus().equals(TaskStatusEnum.BURST_SUCCESS.code())) {
            taskMaster.setStartTime(new Date());
            taskMaster.setStatus(TaskStatusEnum.IN_EXECUTE.code());
            taskMasterMapper.updateByPrimaryKeySelective(taskMaster);
            masterIdBucket.set(taskMaster.getId(), 10, TimeUnit.SECONDS);
        }

        return context;
    }

    private TaskSubDO findOneShard(ExecuteShardContext context) {

        TaskSubExample.TaskSubExampleBuilder exampleBuilder = TaskSubExample.builder()
                .statuses(context.getSubStatuses())
                .taskMasterId(context.getTaskMasterId())
                .page(PageRequest.of(1, 1));
        if (Objects.nonNull(ShardingUtil.getShardingVo())) {
            exampleBuilder.idHash(new IdHashExample(ShardingUtil.getShardingVo().getTotal(), ShardingUtil.getShardingVo().getIndex()));
        }
        List<TaskSubDO> taskShards = taskSubMapper.selectByExample(exampleBuilder.build());
        return CollUtil.isNotEmpty(taskShards) ? taskShards.get(0) : null;
    }

    private void complete(ExecuteShardContext context) {
        TaskMasterDO taskMaster = new TaskMasterDO();
        taskMaster.setId(context.getTaskMasterId());
        taskMaster.setFailTimes(0L);

        List<TaskSubDTO> statistics = taskSubMapper.statistics(context.getTaskMasterId());
        for (TaskSubDTO statistic : statistics) {
            TaskSubStatusEnum taskSubStatus = TaskSubStatusEnum.from(statistic.getStatus());
            switch (taskSubStatus) {
                case FAIL:
                    taskMaster.setFailTimes(statistic.getCount());
                    break;
                case SUCCESS:
                    taskMaster.setSuccessTimes(statistic.getCount());
            }
        }

        if (taskMaster.getFailTimes() > 0) {
            taskMaster.setStatus(TaskStatusEnum.EXECUTE_FAIL.code());
        }else {
            taskMaster.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
        }

        taskMaster.setFinishTime(new Date());
        taskMasterMapper.updateByPrimaryKeySelective(taskMaster);

        if (context.isNeedSendCompleteMsg()) {
            // 发送消息
            ShardCompleteMessage completeMessage = ShardCompleteMessage.builder()
                    .jobName(taskProperties.getJobName())
                    .taskMasterId(context.getTaskMasterId())
                    .allShardCount(statistics.stream().mapToLong(TaskSubDTO::getCount).sum())
                    .successShardCount(taskMaster.getSuccessTimes())
                    .failShardCount(taskMaster.getFailTimes())
                    .build();
            log.info(SHARD_JOB_MARKER, "任务执行完成,开始发送后置消息,消息体:{}", JSON.toJSONString(completeMessage));
            Message<ShardCompleteMessage> message = MessageBuilder.withPayload(completeMessage).build();
            SendResult sendResult = rocketMQTemplate.syncSend(StrUtil.join(StrUtil.COLON, topic, tag), message);
            log.info(SHARD_JOB_MARKER, "任务执行完成，消息发送成功，{}", JSON.toJSONString(sendResult));
        }

        context.complete(TaskStatusEnum.from(taskMaster.getStatus()));
    }

    @AllArgsConstructor
    class ShardTask implements Runnable {

        ExecuteShardContext context;
        TaskSubDO taskSub;
        Consumer<ExecuteShardParam> bizProcessService;
        @Override
        public void run() {
            log.info(SHARD_JOB_MARKER, "开始执行当前分片任务，任务ID：{}", taskSub.getId());
            TaskSubDO modify = new TaskSubDO();
            modify.setId(taskSub.getId());
            try {
                List<String> bizIds = JSON.parseArray(taskSub.getVoucher(), String.class);
                ExecuteShardParam shardParam = ExecuteShardParam.builder()
                        .bizIds(bizIds)
                        .subTaskId(taskSub.getId())
                        .taskMasterId(taskSub.getTaskMasterId())
                        .batchNo(context.getJobParam().getBatchNo())
                        .build();
                bizProcessService.accept(shardParam);
                modify.setStatus(TaskSubStatusEnum.SUCCESS.code());
            } catch (Exception e) {
                log.warn(SHARD_JOB_MARKER, "分片任务执行业务处理失败,分片任务ID【{}】", taskSub.getId(), e);
                modify.setStatus(TaskSubStatusEnum.FAIL.code());
            }
            taskSubMapper.updateByPrimaryKeySelective(modify);
            Long shardCount = decrementShardCount(context.getTaskMasterId());
            log.info(SHARD_JOB_MARKER, "当前分片任务执行结束,任务ID：{}, 剩余{}个分片数", taskSub.getId(), shardCount);
            if (shardCount == 0) {
                context.needSendCompleteMsg();
                complete(context);
            }
        }
    }
}
