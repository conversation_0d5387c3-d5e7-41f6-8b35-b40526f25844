package so.dian.huashan.framework.task.entity;

import cn.hutool.core.collection.CollUtil;
import lombok.Builder;
import lombok.Getter;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/12 09:45
 * @description:
 */
@Builder
@Getter
public class ExecuteShardParam {

    private List<String> bizIds;

    private Long subTaskId;

    private Long taskMasterId;

    private String batchNo;

    public List<Long> bizIdToLong() {
        if (CollUtil.isEmpty(this.bizIds))
            return Collections.emptyList();

        return this.bizIds.stream().map(Long::parseLong).collect(Collectors.toList());
    }
}
