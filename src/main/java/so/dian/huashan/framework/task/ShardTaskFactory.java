package so.dian.huashan.framework.task;

import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.AsyncTaskExecutor;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.task.processor.ShardInstallProcessor;
import so.dian.huashan.framework.task.processor.ShardProcessor;

import java.util.Map;

/**
 * @author: miaoshuai
 * @create: 2023/12/14 09:28
 * @description:
 */
public class ShardTaskFactory {

    private static final String SHARD_EXE_THREAD_POOL = "shardExeThreadPool";

    static ApplicationContext applicationContext;

    public static ShardTaskInstallService createInstallService(ShardTaskProperties properties) {
        ShardTaskInstallService installService = new ShardTaskInstallService(properties);
        Map<String, ShardProcessor> beansOfType = applicationContext.getBeansOfType(ShardProcessor.class);
        for (ShardProcessor shardProcessor : beansOfType.values()) {
            if (!properties.getJobName().equals(shardProcessor.jobName()))
                continue;
            if (shardProcessor instanceof ShardInstallProcessor)
                installService.addProcessor((ShardInstallProcessor) shardProcessor);
        }
        AutowireCapableBeanFactory capableBeanFactory = applicationContext.getAutowireCapableBeanFactory();
        capableBeanFactory.autowireBean(installService);
        return installService;
    }

    public static ShardTaskExecuteService createExecuteService(ShardTaskProperties properties) {
        AsyncTaskExecutor shardExeThreadPool = applicationContext.getBean(SHARD_EXE_THREAD_POOL, AsyncTaskExecutor.class);
        ShardTaskExecuteService executeService = new ShardTaskExecuteService(shardExeThreadPool, properties);
        AutowireCapableBeanFactory capableBeanFactory = applicationContext.getAutowireCapableBeanFactory();
        capableBeanFactory.autowireBean(executeService);
        return executeService;
    }

    public static ShardTaskRetryService createRetryService(ShardTaskProperties properties) {
        ShardTaskRetryService retryService = new ShardTaskRetryService(properties);
        AutowireCapableBeanFactory capableBeanFactory = applicationContext.getAutowireCapableBeanFactory();
        capableBeanFactory.autowireBean(retryService);
        return retryService;
    }
}
