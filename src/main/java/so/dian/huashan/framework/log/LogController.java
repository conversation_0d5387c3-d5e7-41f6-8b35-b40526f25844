package so.dian.huashan.framework.log;

import ch.qos.logback.classic.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;

import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/04/19 14:58
 * @description:
 */
@RestController
@RequestMapping
public class LogController {

    @Autowired
    private LogService logService;

    @RequestMapping("log/level")
    public BizResult<String> level(@RequestParam("logTopic") String logTopic) {
        return BizResult.create(Optional.ofNullable(logService.level(logTopic)).map(Level::toString).orElse(null));
    }

    @RequestMapping("log/change/level")
    public BizResult<Boolean> changeLevel(@RequestParam("logTopic") String logTopic,
                                          @RequestParam(value = "level", required = false) String levelStr) {
        return BizResult.create(logService.changeLevel(logTopic, levelStr));
    }
}
