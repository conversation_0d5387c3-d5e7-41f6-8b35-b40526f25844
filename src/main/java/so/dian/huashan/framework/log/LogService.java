package so.dian.huashan.framework.log;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class LogService {

    public Level level(String logTopic) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = loggerContext.exists(logTopic);
        if (Objects.isNull(logger))
            return null;

        return logger.getEffectiveLevel();
    }

    Boolean changeLevel(String logTopic, String levelStr) {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = loggerContext.exists(logTopic);
        if (Objects.isNull(logger))
            return Boolean.FALSE;

        Level level = Level.toLevel(levelStr, null);
        logger.setLevel(level);

        return Boolean.TRUE;
    }
}
