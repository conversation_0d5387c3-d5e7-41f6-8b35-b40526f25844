package so.dian.huashan.framework.sftp;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * @Author: ahuang
 * @CreateTime: 2024-12-16 11:19
 * @Description:
 */
@Component
public class SftpService {

    @Value("${sftp.host}")
    private String host;

    @Value("${sftp.port}")
    private int port;

    @Value("${sftp.username}")
    private String username;

    @Value("${sftp.password}")
    private String password;

    private final SftpClient sftpClient;

    public SftpService(SftpClient sftpClient) {
        this.sftpClient = sftpClient;
    }

    public void uploadFile(File localFile, String remoteDirectory) {
        try {
            // 建立连接
            sftpClient.connect(host, port, username, password);

            // 上传文件
            sftpClient.uploadFile(localFile.getAbsolutePath(), remoteDirectory, localFile.getName());
        } catch (Exception e) {
            throw new RuntimeException("SFTP 文件上传失败", e);
        } finally {
            // 关闭连接
            sftpClient.disconnect();
        }
    }
}