package so.dian.huashan.framework.sftp;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.util.Properties;

/**
 * @Author: ahuang
 * @CreateTime: 2024-12-16 11:16
 * @Description:
 */
@Slf4j
@Component
public class SftpClient {

    private ChannelSftp channelSftp;
    private Session session;

    public void connect(String host, int port, String username, String password) throws Exception {
        JSch jsch = new JSch();
        session = jsch.getSession(username, host, port);
        session.setPassword(password);

        // 设置 SFTP 配置项
        Properties config = new Properties();
        config.put("StrictHostKeyChecking", "no"); // 忽略 HostKey 检查
        session.setConfig(config);

        // 连接到服务器
        session.connect();
        log.info(">>> SFTP Session connected to {}:{}", host, port);

        // 打开 SFTP 通道
        channelSftp = (ChannelSftp) session.openChannel("sftp");
        channelSftp.connect();
        log.info(">>> SFTP Channel connected.");
    }

    public void uploadFile(String localFilePath, String remoteDirectory, String remoteFileName) throws Exception {
        if (channelSftp == null || !channelSftp.isConnected()) {
            throw new IllegalStateException("SFTP Channel is not connected.");
        }
        // 确保远程目录存在
        ensureRemoteDirectoryExists(remoteDirectory);

        // 上传文件
        try (FileInputStream fis = new FileInputStream(localFilePath)) {
            channelSftp.put(fis, remoteFileName); // 上传文件
            log.info(">>> 文件上传成功: {} -> {}/{}", localFilePath, remoteDirectory, remoteFileName);
        } catch (Exception e) {
            log.error(">>> 文件上传失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 确保远程目录存在
     * @param remoteDirectory 远程目录路径
     * @throws Exception 如果无法创建目录
     */
    private void ensureRemoteDirectoryExists(String remoteDirectory) throws Exception {
        try {
            channelSftp.cd(remoteDirectory); // 尝试切换到远程目录
        } catch (Exception e) {
            log.warn(">>> 远程目录不存在，尝试创建: {}", remoteDirectory);

            // 创建远程目录
            String[] folders = remoteDirectory.split("/");
            String currentPath = "";
            for (String folder : folders) {
                if (folder.isEmpty()) {
                    continue;
                }
                currentPath += "/" + folder;
                try {
                    channelSftp.cd(currentPath);
                } catch (Exception ex) {
                    channelSftp.mkdir(currentPath); // 创建当前目录
                    log.info(">>> 远程目录已创建: {}", currentPath);
                    channelSftp.cd(currentPath);
                }
            }
        }
    }

    public void disconnect() {
        if (channelSftp != null && channelSftp.isConnected()) {
            channelSftp.disconnect();
        }
        if (session != null && session.isConnected()) {
            session.disconnect();
        }
        log.info(">>> SFTP 连接已关闭");
    }
}
