package so.dian.huashan.framework.openTelemetry;

import com.xxl.job.core.handler.annotation.XxlJob;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

@Slf4j
@Aspect
@Component
public class XxlJobOpenTelemetryAspect {

    private final static String SCOPE_VERSION = "0.1.0";

    @Autowired
    private OpenTelemetry openTelemetry;

    @Around("@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public Object execute(ProceedingJoinPoint joinPoint) throws Throwable {

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        XxlJob xxlJob = method.getAnnotation(XxlJob.class);

        Tracer tracer = openTelemetry.getTracer(methodSignature.getDeclaringType().getName(), SCOPE_VERSION);
        Span span = tracer.spanBuilder(xxlJob.value()).startSpan();
        try (Scope scope = span.makeCurrent()) {
            return joinPoint.proceed(joinPoint.getArgs());
        } finally {
            span.end();
        }
    }
}
