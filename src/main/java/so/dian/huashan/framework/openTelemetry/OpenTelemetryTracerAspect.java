package so.dian.huashan.framework.openTelemetry;

import cn.hutool.core.util.StrUtil;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

@Aspect
@Component
public class OpenTelemetryTracerAspect {

    private final static String SCOPE_VERSION = "0.1.0";

    @Autowired
    private OpenTelemetry openTelemetry;

    @Around("@annotation(so.dian.huashan.framework.openTelemetry.OpenTelemetryTracer)")
    public Object execute(ProceedingJoinPoint joinPoint) throws Throwable {

        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Class<?> declaringType = methodSignature.getDeclaringType();
        Method method = methodSignature.getMethod();
        OpenTelemetryTracer openTelemetryTracer = method.getAnnotation(OpenTelemetryTracer.class);
        if (Objects.isNull(openTelemetryTracer))
            openTelemetryTracer = declaringType.getAnnotation(OpenTelemetryTracer.class);

        String name = openTelemetryTracer.name();
        if (StrUtil.isBlank(name))
            name = declaringType.getName();

        String spanName = openTelemetryTracer.spanName();
        if (StrUtil.isBlank(spanName))
            spanName = method.getName();

        Tracer tracer = openTelemetry.getTracer(name, SCOPE_VERSION);
        Span span = tracer.spanBuilder(StrUtil.join(StrUtil.DOT, declaringType.getSimpleName(), spanName)).startSpan();
        try (Scope scope = span.makeCurrent()) {
            return joinPoint.proceed(joinPoint.getArgs());
        } finally {
            span.end();
        }
    }
}
