package so.dian.huashan.framework.openTelemetry;

import feign.Client;
import feign.hc5.ApacheHttp5Client;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.contrib.sampler.RuleBasedRoutingSampler;
import io.opentelemetry.instrumentation.apachehttpclient.v5_2.ApacheHttpClient5Telemetry;
import io.opentelemetry.instrumentation.rocketmqclient.v4_8.RocketMqTelemetry;
import io.opentelemetry.sdk.autoconfigure.spi.AutoConfigurationCustomizerProvider;
import io.opentelemetry.semconv.HttpAttributes;
import io.opentelemetry.semconv.UrlAttributes;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import so.dian.spring.cloud.mesh.openfeign.FeignSvcLoadbalancerClient;

/**
 * @Author: ahuang
 * @CreateTime: 2024-05-15 17:25
 * @Description:
 */
@Configuration
public class OpenTelemetryConfig {

    @Bean
    public AutoConfigurationCustomizerProvider otelCustomizer() {
        return p ->
                p.addSamplerCustomizer(
                        (fallback, config) ->
                                RuleBasedRoutingSampler.builder(SpanKind.SERVER, fallback)
                                        .drop(UrlAttributes.URL_PATH, "^/actuator")
                                        .drop(HttpAttributes.HTTP_REQUEST_METHOD, "OPTIONS")
                                        .build());
    }

    @Bean
    public Client feignClient(LoadBalancerClient loadBalancerClient, OpenTelemetry openTelemetry) {
        Client delegate = new ApacheHttp5Client(ApacheHttpClient5Telemetry.builder(openTelemetry).build().newHttpClient());
        return new FeignSvcLoadbalancerClient(delegate, loadBalancerClient);
    }

    @Bean
    public RocketMqTelemetry rocketMqTelemetry(OpenTelemetry openTelemetry) {
        return RocketMqTelemetry.create(openTelemetry);
    }

    @Bean
    public BeanPostProcessor rocketMqBeanPostProcessor(RocketMqTelemetry rocketMqTelemetry) {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) {
                if (bean instanceof DefaultMQProducer) {
                    DefaultMQProducer producer = (DefaultMQProducer) bean;
                    producer.getDefaultMQProducerImpl().registerSendMessageHook(rocketMqTelemetry.newTracingSendMessageHook());
                } else if (bean instanceof DefaultMQPushConsumer) {
                    DefaultMQPushConsumer consumer = (DefaultMQPushConsumer) bean;
                    consumer.getDefaultMQPushConsumerImpl().registerConsumeMessageHook(rocketMqTelemetry.newTracingConsumeMessageHook());
                }
                return bean;
            }
        };
    }
}