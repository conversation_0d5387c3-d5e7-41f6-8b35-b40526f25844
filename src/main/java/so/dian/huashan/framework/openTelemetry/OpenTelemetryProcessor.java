package so.dian.huashan.framework.openTelemetry;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.instrumentation.jdbc.datasource.JdbcTelemetry;
import io.opentelemetry.instrumentation.rocketmqclient.v4_8.RocketMqTelemetry;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class OpenTelemetryProcessor implements BeanPostProcessor {

    @Autowired
    private RocketMqTelemetry rocketMqTelemetry;

    @Autowired
    private OpenTelemetry openTelemetry;

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof DefaultRocketMQListenerContainer) {
            DefaultRocketMQListenerContainer listenerContainer = (DefaultRocketMQListenerContainer) bean;
            listenerContainer.getConsumer().getDefaultMQPushConsumerImpl().registerConsumeMessageHook(rocketMqTelemetry.newTracingConsumeMessageHook());
        }
        if (bean instanceof RocketMQTemplate) {
            RocketMQTemplate template = (RocketMQTemplate) bean;
            template.getProducer().getDefaultMQProducerImpl().registerSendMessageHook(rocketMqTelemetry.newTracingSendMessageHook());
        }

        if (bean instanceof DataSource) {
            bean = JdbcTelemetry.create(openTelemetry).wrap((DataSource) bean);
        }

        if (bean instanceof RedissonClient) {
            bean = JdbcTelemetry.create(openTelemetry).wrap((DataSource) bean);
        }

        return bean;
    }
}
