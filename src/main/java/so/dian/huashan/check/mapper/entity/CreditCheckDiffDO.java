package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

@Data
public class CreditCheckDiffDO  extends BaseDO {

    private Long id;

    private String batchNo;
    /**
     * 业务id
     */
    private Long bizId;

    /**
     *  业务类型
     */
    private Integer bizType;

    /**
     * 原因
     */
    private Integer reason;

    /**
     * 状态（0-未平账，1-手动平账，2-不参与平账）
     */
    private Integer status;

    /**
     * 状态（0-未平账，1-手动平账，2-不参与平账）
     */
    private String remark;

}
