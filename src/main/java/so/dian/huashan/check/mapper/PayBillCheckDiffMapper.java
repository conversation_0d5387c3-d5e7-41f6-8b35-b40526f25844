package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.model.dto.CheckOffSumAndCountDTO;
import so.dian.huashan.model.param.CheckDiffParam;
import so.dian.huashan.check.job.dto.PayBillCheckDiffDTO;
import so.dian.huashan.check.job.dto.PayBillCheckDiffGroupDTO;
import so.dian.huashan.check.job.param.PayBillCheckDiffGroupParam;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.model.param.CheckOffSumAndCountParam;
import so.dian.huashan.model.query.CheckDiffQuery;

import java.util.List;

public interface PayBillCheckDiffMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PayBillCheckDiffDO record);

    int insertSelective(PayBillCheckDiffDO record);

    PayBillCheckDiffDO selectByPrimaryKey(Long id);

    List<PayBillCheckDiffDTO> selectByCheckDiffParam (CheckDiffParam checkDiffParam);

    List<PayBillCheckDiffGroupDTO> selectCheckDiffGroupByParam(PayBillCheckDiffGroupParam payBillCheckDiffGroupParam);

    CheckOffSumAndCountDTO checkOffDiffAmountAndCount(CheckOffSumAndCountParam checkOffSumAndCountParam);
    /**
     * 查询列表
     */
    List<PayBillCheckDiffDO> selectByQuery (CheckDiffQuery checkDiffQuery);
    int updateByPrimaryKeySelective(PayBillCheckDiffDO record);

    int updateByPrimaryKey(PayBillCheckDiffDO record);

    int batchInsert(@Param("checkDiffs") List<PayBillCheckDiffDO> checkDiffs);

    int batchUpdate(@Param("checkDiffs") List<PayBillCheckDiffDO> checkDiffs);

    int batchDeleteByPayBillIds(@Param("payBillIds") List<Long> payBillIds);

    int batchDeleteByPayBillThirdIds(@Param("payBillThirdIds") List<Long> payBillThirdIds);

    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime, @Param("maxId") Long maxId);

    List<PayBillCheckDiffDO> selectByIds(@Param("ids") List<Long> ids);

    PayBillCheckDiffDO getOne(CheckDiffParam checkDiffParam);

    List<PayBillCheckDiffDO> countDiff();
}