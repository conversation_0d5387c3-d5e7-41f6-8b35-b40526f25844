package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO;

import java.util.Date;
import java.util.List;

public interface SplitBillCheckDiffMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SplitBillCheckDiffDO record);

    int insertSelective(SplitBillCheckDiffDO record);

    SplitBillCheckDiffDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SplitBillCheckDiffDO record);

    int updateByPrimaryKey(SplitBillCheckDiffDO record);

    int batchInsert(@Param("checkDiffs") List<SplitBillCheckDiffDO> checkDiffs);

    int batchUpdate(@Param("checkDiffs") List<SplitBillCheckDiffDO> checkDiffs);

    int batchDeleteByWxSplitIds(@Param("wxSplitIds") List<Long> wxSplitIds);

    int batchDeleteBySettleDetailIds(@Param("settleDetailIds") List<Long> settleDetailIds);

    int batchDeleteByIds(@Param("ids") List<Long> ids);

    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime, @Param("maxId") Long maxId);

    List<SplitBillCheckDiffDO> selectByIds(@Param("ids") List<Long> ids);

    Long totalDiffStatistics(@Param("startBillDate") Long startBillDate, @Param("endBillDate") Long endBillDate);
}