package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.ResultBillDO;

import java.util.List;

public interface ResultBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ResultBillDO record);

    int insertSelective(ResultBillDO record);

    int insertOrUpdateBatch(List<ResultBillDO> list);

    ResultBillDO selectByPrimaryKey(Long id);

    ResultBillDO selectByKey(@Param("tradeType") String tradeType, @Param("tradeNo")String tradeNo, @Param("amount")Integer amount, @Param("payWay")Integer payWay);

    int updateByPrimaryKeySelective(ResultBillDO record);

    int updateByPrimaryKey(ResultBillDO record);
}