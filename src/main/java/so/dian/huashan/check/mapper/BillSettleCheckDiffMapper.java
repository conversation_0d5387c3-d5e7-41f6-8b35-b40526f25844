package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO;

import java.util.List;

public interface BillSettleCheckDiffMapper {
    int deleteByPrimaryKey(Long id);

    int batchDeleteByIds(@Param("ids") List<Long> ids);
    int batchAddVersion(@Param("ids") List<Long> ids);

    int insert(BillSettleCheckDiffDO record);

    int insertSelective(BillSettleCheckDiffDO record);

    BillSettleCheckDiffDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillSettleCheckDiffDO record);

    int updateByPrimaryKey(BillSettleCheckDiffDO record);

    int batchInsert(@Param("checkDiffs")List<BillSettleCheckDiffDO> checkDiffs);

    List<BillSettleCheckDiffDO> selectReCheck(@Param("page")PageRequest page);

    List<BillSettleCheckDiffDO> selectRecently(@Param("page")PageRequest page, @Param("startGmtCreate") Long startGmtCreate, @Param("endGmtCreate") Long endGmtCreate);

    int countDiff(@Param("startGmtCreate") Long startGmtCreate, @Param("endGmtCreate") Long endGmtCreate);

    List<BillSettleCheckDiffDO> selectByIds(@Param("ids") List<Long> ids);
}