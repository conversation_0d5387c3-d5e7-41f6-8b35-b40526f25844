package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO;

import java.util.List;

public interface PayBillCheckConsistencyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PayBillCheckConsistencyDO record);

    int insertSelective(PayBillCheckConsistencyDO record);

    PayBillCheckConsistencyDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayBillCheckConsistencyDO record);

    int updateByPrimaryKey(PayBillCheckConsistencyDO record);

    int batchInsert(@Param("checkDiffs") List<PayBillCheckConsistencyDO> checkDiffs);

    int batchDeleteByPayBillIds(@Param("payBillIds") List<Long> payBillIds);

    int batchDeleteByPayBillThirdIds(@Param("payBillThirdIds") List<Long> payBillThirdIds);

}