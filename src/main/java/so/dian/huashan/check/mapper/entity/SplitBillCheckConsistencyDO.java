package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * split_bill_check_consistency
 * <AUTHOR>
@Data
public class SplitBillCheckConsistencyDO extends BaseDO {

    /**
     * 微信分账id
     */
    private Long wxSplitBillDetailId;

    /**
     * 结算详情底表id
     */
    private Long settleDetailOriginalId;

    /**
     * 分账明细单号
     */
    private String splitDetailNo;

    /**
     * 分账方
     */
    private String splitSourceId;

    /**
     * 分账接受方
     */
    private String splitReceiveId;

    /**
     * 分账金额（分）
     */
    private Long splitAmt;
}