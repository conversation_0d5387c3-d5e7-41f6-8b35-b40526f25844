package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO;

import java.util.List;

public interface SplitBillCheckConsistencyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SplitBillCheckConsistencyDO record);

    int insertSelective(SplitBillCheckConsistencyDO record);

    SplitBillCheckConsistencyDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SplitBillCheckConsistencyDO record);

    int updateByPrimaryKey(SplitBillCheckConsistencyDO record);

    int batchInsert(@Param("checkDiffs") List<SplitBillCheckConsistencyDO> checkDiffs);

    int batchDeleteBySplitBillIds(@Param("splitBillIds") List<Long> splitBillIds);

    int batchDeleteBySplitBillThirdIds(@Param("splitBillThirdIds") List<Long> splitBillThirdIds);
}