package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

import java.util.Date;

/**
 * result_bill
 * <AUTHOR>
@Data
public class ResultBillDO extends BaseDO {
    /**
     * 渠道数据id
     */
    private Long billId;

    /**
     * 幂等键
     */
    private String idempotentNo;

    /**
     * 核对日
     */
    private Integer checkDate;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 账单时间
     */
    private Date billDate;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 交易金额，单位：分
     */
    private Integer amount;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 支付方式（1-支付宝，3-微信，4-苏宁，5-QQ，6-银联，7-会员，8-优惠券，9-积分，10-钱包或押金，11-宝付，12-微美）
     */
    private Integer payWay;

    /**
     * 核对结果描述
     */
    private String checkResult;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 主体编码
     */
    private String subjectCode;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 主体类型
     */
    private Byte subjectType;

    /**
     * 小电支付系统流水号，分别对应payment#trade_no和refund_order#refund_order_no
     */
    private String dianPayNo;

    /**
     * 第三方账单流水号
     */
    private String channelTradeNo;
}