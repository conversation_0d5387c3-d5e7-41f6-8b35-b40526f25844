package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

import java.util.Date;

/**
 * pay_bill_check_consistency
 * <AUTHOR>
@Data
public class PayBillCheckConsistencyDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 付款单账单ID
     */
    private Long payBillId;

    /**
     * 三方付款单支付账单ID
     */
    private Long payBillThirdId;

    /**
     * 付款主体id
     */
    private Long paySubjectId;

    /**
     * 付款主体类型
     */
    private Integer paySubjectType;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 账单来源（0-中喜，1-工猫，2-招商cbs）
     */
    private Integer source;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 第三方账单流水号
     */
    private String channelTradeNo;

    /**
     * 交易金额，单位：分
     */
    private Long tradeAmount;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 对账类型，1-增量平账，2-差异平账
     */
    private Integer checkType;
}