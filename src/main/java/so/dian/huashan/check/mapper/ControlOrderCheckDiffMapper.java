package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO;

import java.util.List;

public interface ControlOrderCheckDiffMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ControlOrderCheckDiffDO record);

    int insertSelective(ControlOrderCheckDiffDO record);

    ControlOrderCheckDiffDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ControlOrderCheckDiffDO record);

    int updateByPrimaryKey(ControlOrderCheckDiffDO record);

    int batchInsert(@Param("checkDiffs") List<ControlOrderCheckDiffDO> checkDiffs);

    List<ControlOrderCheckDiffDO> selectReCheck(@Param("page") PageRequest page);

    int batchUpdate(@Param("checkDiffDOS") List<ControlOrderCheckDiffDO> checkDiffDOS);

    int batchLogicDeleteByIds(@Param("ids") List<Long> ids, @Param("count") Integer count);

    int batchDeleteByIds(@Param("ids") List<Long> ids);

    List<ControlOrderCheckDiffDO> selectIncrementDiff(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<ControlOrderCheckDiffDO> selectByControlOrderIds(@Param("controlOrderIds") List<Long> controlOrderIds);
}