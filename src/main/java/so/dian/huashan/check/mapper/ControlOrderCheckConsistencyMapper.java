package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO;

import java.util.List;

public interface ControlOrderCheckConsistencyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ControlOrderCheckConsistencyDO record);

    int insertSelective(ControlOrderCheckConsistencyDO record);

    ControlOrderCheckConsistencyDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ControlOrderCheckConsistencyDO record);

    int updateByPrimaryKey(ControlOrderCheckConsistencyDO record);

    int batchInsert(@Param("consistencyDOS")List<ControlOrderCheckConsistencyDO> consistencyDOS);

    List<ControlOrderCheckConsistencyDO> selectByControlOrderIds(@Param("controlOrderIds") List<Long> controlOrderIds);

    int batchLogicDelete(@Param("ids") List<Long> ids);
}