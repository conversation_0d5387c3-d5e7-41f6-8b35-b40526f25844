package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * split_bill_check_diff
 * <AUTHOR>
@Data
public class SplitBillCheckDiffDO extends BaseDO {

    /**
     * 状态：0-继续对账 1-手动平账
     */
    private Integer status;

    /**
     * 结算明细单号
     */
    private String settleDetailNo;

    /**
     * 微信分账底表id
     */
    private Long wxDetailId;

    /**
     * 结算详情底表id
     */
    private Long bizOriginalId;

    /**
     * 场景：0-业务不存在 1-渠道不存在 2-字段差异
     */
    private Integer diffScene;

    /**
     * 差异原因，json存储
     */
    private String diffReason;

    /**
     * 对账次数
     */
    private Integer checkCount;
}