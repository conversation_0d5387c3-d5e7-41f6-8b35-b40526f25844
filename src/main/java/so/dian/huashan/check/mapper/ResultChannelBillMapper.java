package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.manage.dto.ChannelDiffStatisticsDTO;
import so.dian.huashan.check.mapper.entity.ResultChannelBillDO;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface ResultChannelBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ResultChannelBillDO record);

    int insertSelective(ResultChannelBillDO record);

    int insertOrUpdateBatch(List<ResultChannelBillDO> list);

    ResultChannelBillDO selectByPrimaryKey(Long id);

    List<ResultChannelBillDO> selectByIds(@Param("ids")List<Long> ids);

    ResultChannelBillDO selectByKey(@Param("tradeType") String tradeType,@Param("tradeNo")String tradeNo,@Param("amount")Integer amount,@Param("payWay")Integer payWay);

    List<Long> selectByCheckDate(@Param("checkDate") Integer checkDate,@Param("startDateTime")Long startDateTime,@Param("endDateTime")Long endDateTime,@Param("ids")List<Long> ids);

    List<Long> selectIdForShard(@Param("checkDate") Integer checkDate, @Param("maxId") Long maxId);

    int updateByPrimaryKeySelective(ResultChannelBillDO record);

    int updateByPrimaryKey(ResultChannelBillDO record);

    int updateByIdList(@Param("ids") Set<Long> ids);

    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("operationType") Byte operationType);

    ChannelDiffStatisticsDTO diffStatistics(@Param("startCreate") Long startCreate, @Param("endCreate") Long endCreate);

    ChannelDiffStatisticsDTO squareAccountStatistics(@Param("startUpdate") Long startUpdate, @Param("endUpdate") Long endUpdate);

    List<ChannelDiffStatisticsDTO> totalDiffStatistics(@Param("startBillDate") Date startBillDate, @Param("endBillDate") Date endBillDate);
}