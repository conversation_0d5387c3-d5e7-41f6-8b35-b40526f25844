package so.dian.huashan.check.mapper;

import so.dian.huashan.check.mapper.entity.PayBillSummaryDO;
import so.dian.huashan.model.param.SummaryParam;
import so.dian.huashan.model.query.GetSummaryPageQuery;

import java.util.List;

public interface PayBillSummaryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PayBillSummaryDO record);

    int insertSelective(PayBillSummaryDO record);

    PayBillSummaryDO selectByPrimaryKey(Long id);


    List<PayBillSummaryDO> selectPageByQuery(GetSummaryPageQuery getSummaryPageQuery);

    List<PayBillSummaryDO> selectByParam(SummaryParam summaryParam);

    int updateByPrimaryKeySelective(PayBillSummaryDO record);

    int updateByPrimaryKey(PayBillSummaryDO record);



}