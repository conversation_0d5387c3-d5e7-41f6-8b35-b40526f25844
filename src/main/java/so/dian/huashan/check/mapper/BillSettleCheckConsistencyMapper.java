package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO;

import java.util.List;

public interface BillSettleCheckConsistencyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BillSettleCheckConsistencyDO record);

    int insertSelective(BillSettleCheckConsistencyDO record);

    BillSettleCheckConsistencyDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillSettleCheckConsistencyDO record);

    int updateByPrimaryKey(BillSettleCheckConsistencyDO record);

    int batchInsert(@Param("checkConsistencys") List<BillSettleCheckConsistencyDO> checkConsistencys);

    Long selectMaxIdByGmtCreate(Long gmtCreate);

    int deleteByGmtCreateAndMaxId(@Param("gmtCreate") Long gmtCreate, @Param("maxId") Long maxId, @Param("size") Integer size);
}