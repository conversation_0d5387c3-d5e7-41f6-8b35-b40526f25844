package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * bill_settle_check_consistency
 * <AUTHOR>
@Data
public class BillSettleCheckConsistencyDO extends BaseDO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单底表ID
     */
    private Long originalId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 对账类型，1-增量平账，2-差异平账
     */
    private Byte checkType;

}