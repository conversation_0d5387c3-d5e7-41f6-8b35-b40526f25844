package so.dian.huashan.check.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.check.mapper.entity.CreditCheckDiffDO;

import java.util.List;

public interface CreditCheckDiffMapper {
    int insertBatch (List<CreditCheckDiffDO> list);

    Long totalDiffStatistics(@Param("startBillDate") Long startBillDate, @Param("endBillDate") Long endBillDate,@Param("bizType") Integer bizType);

    String selectMaxBatchNo();
    List<Long> selectByCheckBatchNo(@Param("batchNo") String batchNo, @Param("maxId") Long maxId);

    List<CreditCheckDiffDO> selectByIds(@Param("ids") List<Long> ids);

    int updateStatusByIds(@Param("ids") List<Long> ids);

    int deleteByBizIdAndBizType (@Param("bizIds") List<Long> bizIds,@Param("bizType") Integer bizType);
}
