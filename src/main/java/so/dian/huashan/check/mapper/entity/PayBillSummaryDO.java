package so.dian.huashan.check.mapper.entity;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * pay_bill_summary
 * <AUTHOR>
@Data
public class PayBillSummaryDO extends BaseDO {

    /**
     * 自然日
     */
    private Integer naturalDate;

    /**
     * 付款主体id
     */
    private Long paySubjectId;

    /**
     * 付款主体类型
     */
    private Integer paySubjectType;

    /**
     * 账单来源（0-中喜，1-工猫，2-招商cbs）
     */
    private Integer source;

    /**
     * 付款差异金额，单位：分
     */
    private Long payDiffAmount;

    /**
     * 付款差异数量
     */
    private Integer payDiffCount;
}