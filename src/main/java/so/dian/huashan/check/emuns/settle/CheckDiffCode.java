package so.dian.huashan.check.emuns.settle;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.EnumInterface;

@AllArgsConstructor
@Getter
public enum CheckDiffCode implements EnumInterface<CheckDiffCode> {
    CONTROL_ES_NOT_EXIST(500100, "ES不存在抽单数据"),
    CONTROL_ES_MULTI(500101, "ES存在多笔抽单数据"),
    CONTROL_STATUS_NOT_SAME(500102, "抽单状态不一致")
    ;

    private final Integer code;
    private final String desc;

    @Override
    public CheckDiffCode getDefault() {
        return null;
    }
}
