package so.dian.huashan.check.emuns.payBill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum PayBillDiffStatus {

    NOT_ACCOUNTS(0, "未平账"),
    HAND_ACCOUNTS(1, "手动平账"),
    NOT_JOIN_ACCOUNTS(2, "不参与平账"),
    ;
    private final Integer code;
    private final String desc;

    public static PayBillDiffStatus from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }

    public static String getDesc(Integer code) {
        if (Objects.isNull(code)){
            return null;
        }
        for (PayBillDiffStatus payBillDiffStatus : PayBillDiffStatus.values()) {
            if (payBillDiffStatus.getCode().equals(code)) {
                return payBillDiffStatus.getDesc();
            }
        }
        return null;
    }
}
