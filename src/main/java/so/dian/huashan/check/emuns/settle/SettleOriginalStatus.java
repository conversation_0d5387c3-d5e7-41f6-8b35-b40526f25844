package so.dian.huashan.check.emuns.settle;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 17:38
 * @description:
 */
@AllArgsConstructor
@Getter
public enum SettleOriginalStatus {

    INITIALIZE((byte)0, "初始化"),
    CONSISTENCY((byte)1, "一致"),
    DIFF((byte)2, "差异"),
    DIFF_CONSISTENCY((byte)3, "差异一致"),
    NO_CHECK((byte)4, "不对账"),
    ;
    private final Byte code;
    private final String desc;
}
