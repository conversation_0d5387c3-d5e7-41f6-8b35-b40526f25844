package so.dian.huashan.check.emuns.payBill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum BillSourceEnum {
    XIYOUKE(0,"僖游客",14),
    GONG_MALL(1,"工猫",6),
    CMB(2, "招商银行",3);

    private final Integer code;
    private final String desc;
    // yandang的枚举值
    private final Integer outerCode;

    public static String getDesc (Integer code) {
        if (Objects.isNull(code)){
            return null;
        }
        for (BillSourceEnum billSourceEnum : BillSourceEnum.values()) {
            if (billSourceEnum.getCode().equals(code)) {
                return billSourceEnum.getDesc();
            }
        }
        return null;
    }

    public static Integer outerCode2Code (Integer outCode){
        if (Objects.isNull(outCode)){
            return null;
        }
        for (BillSourceEnum billSourceEnum : BillSourceEnum.values()) {
            if (billSourceEnum.getOuterCode().equals(outCode)) {
                return billSourceEnum.getCode();
            }
        }
        return null;
    }
}
