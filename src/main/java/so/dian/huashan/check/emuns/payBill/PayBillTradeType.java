package so.dian.huashan.check.emuns.payBill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum PayBillTradeType {

    IN("in", "收入"),
    OUT("out", "支出"),
    ;
    private final String code;
    private final String desc;

    public static PayBillTradeType from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }
}
