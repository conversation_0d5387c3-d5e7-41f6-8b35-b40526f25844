package so.dian.huashan.check.emuns.payBill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum PayBillThirdStatus {

    SUCCESS(0, "成功"),
    REFUND(1, "退票"),
    PAYMENTS(2, "支付中"),
    FAIL(3, "失败"),
    ;
    private final Integer code;
    private final String desc;

    public static PayBillThirdStatus from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }
}
