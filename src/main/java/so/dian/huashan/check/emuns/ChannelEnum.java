package so.dian.huashan.check.emuns;

import so.dian.huashan.common.constant.CommonConstants;

import java.util.Arrays;

import static so.dian.huashan.common.constant.CommonConstants.HUAZHU_BILL;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/01/25 14:18
 * @description:
 */
public enum ChannelEnum {
    CHANNEL_ACCOUNT(2,"huashan_channel_account"),
    CHANNEL_DIFFERENCE(4,"huashan_channel_difference"),
    HUAZHU_CHANNEL_BILL(5, HUAZHU_BILL),
    CHANNEL_INCREMENT_ACCOUNT(6, CommonConstants.CHANNEL_INCREMENT_ACCOUNT),
    CHANNEL_INCREMENT_DIFFERENCE(7, CommonConstants.CHANNEL_INCREMENT_DIFFERENCE),
    ;
    private final Integer code;
    private final String desc;
    ChannelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer code() {
        return this.code;
    }
    public String desc() {
        return this.desc;
    }

    public static ChannelEnum from(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
