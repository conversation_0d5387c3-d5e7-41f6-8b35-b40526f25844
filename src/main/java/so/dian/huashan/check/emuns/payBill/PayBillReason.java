package so.dian.huashan.check.emuns.payBill;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum PayBillReason {

    //1-三方不存在，2-业务方不存在，3-金额不想等，4-退票
    NOT_THIRD(1, "三方不存在"),
    NOT_BIZ(2, "业务方不存在"),
    NOT_AMOUNT(3, "金额不相等"),
    REFUND_TICKET(4, "退票"),
    ;
    private final Integer code;
    private final String desc;

    public static PayBillReason from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }
    public static String getDesc(Integer code) {
        if (Objects.isNull(code)){
            return null;
        }
        for (PayBillReason payBillReason : PayBillReason.values()) {
            if (payBillReason.getCode().equals(code)) {
                return payBillReason.getDesc();
            }
        }
        return null;
    }

}
