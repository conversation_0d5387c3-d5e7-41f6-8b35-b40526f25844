package so.dian.huashan.check.emuns.settle;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:42
 * @description:
 */
@AllArgsConstructor
@Getter
public enum TradeType {
    PAYMENT(1, "支付"),
    REFUND(2, "退款"),
    ORDER_RETURN(3, "充电宝租赁订单归还"),
    ORDER_TRACE(4, "充电宝订单追溯"),
    TIME_CARD_VERIFY(5, "次卡订单核销")
    ;

    private final Integer code;
    private final String desc;
}
