package so.dian.huashan.check.emuns.settle;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 16:43
 * @description: 出资方类型
 */
@AllArgsConstructor
@Getter
public enum SettleSubjectType {
    XIAODIAN((byte) 1, "小电"),
    AGENT((byte) 2, "代理商/二级代理商"),
    JV_COMPANY((byte) 5, "合资公司"),
    ;
    private final Byte code;
    private final String desc;

    public static SettleSubjectType from(Byte code) {
        return Arrays.stream(values()).filter(type -> type.code.equals(code)).findFirst().orElse(null);
    }

    public static String getDesc(Integer code) {
        if (code == null) {
            return null;
        }
        Byte byteCode = (byte)code.intValue();
        for (SettleSubjectType type : SettleSubjectType.values()) {
            if (type.code.equals(byteCode)) {
                return type.desc;
            }
        }
        return null;
    }
}
