package so.dian.huashan.check.emuns.settle;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum SettleOrderStatus {

    PAY(4, "支付"),
    CARD_PAY(2, "次卡支付"),
    CARD_REFUND(3, "次卡退款"),
    REFUND(5, "退款"),
    ;
    private final Integer code;
    private final String desc;

    public static SettleOrderStatus from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }
}
