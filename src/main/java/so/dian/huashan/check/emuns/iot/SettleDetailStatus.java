package so.dian.huashan.check.emuns.iot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum SettleDetailStatus {

    INIT(0, "初始化"),
    PROCESSED(1, "已处理"),
    PROCESSING(2, "重新处理"),
    ;
    private final Integer code;
    private final String desc;

    public static SettleDetailStatus from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }
}
