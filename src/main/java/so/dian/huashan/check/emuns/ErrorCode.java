package so.dian.huashan.check.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.ErrorCodeInterface;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/03/29 14:02
 * @description:
 */
@AllArgsConstructor
@Getter
public enum ErrorCode implements ErrorCodeInterface<ErrorCode> {

    CHANNEL_DIFF_IDS_EMPYT("100100", "渠道差异ID集合为空"),
    ;
    private final String code;
    private final String desc;
}
