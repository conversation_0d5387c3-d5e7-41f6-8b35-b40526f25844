package so.dian.huashan.check.emuns;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/03/29 13:46
 * @description:
 */
@AllArgsConstructor
@Getter
public enum ChannelDiffOperationType {
    NORMAL((byte)0, "正常"),
    MANUAL_IGNORE((byte)1, "手动忽略"),
    ;

    private final Byte code;
    private final String desc;

    public static ChannelDiffOperationType from(Byte code) {
        return Arrays.stream(values())
                .filter(status -> status.getCode().equals(code)).findFirst()
                .orElse(null);
    }
}
