package so.dian.huashan.check.emuns.iot;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * @author: xingba
 * @create: 2024/09/09 18:32
 * @description:
 */
@AllArgsConstructor
@Getter
public enum SettleDetailScene {

    //1-三方不存在，2-业务方不存在，3-金额不想等，4-退票
    NOT_BIZ(0, "业务方不存在"),
    NOT_THIRD(1, "渠道不存在"),
    NOT_SCENE(2, "字段差异"),
    ;
    private final Integer code;
    private final String desc;

    public static SettleDetailScene from(Integer code) {
        return Arrays.stream(values())
                .filter(status -> status.code.equals(code))
                .findFirst().orElse(null);
    }
    public static String getDesc(Integer code) {
        if (Objects.isNull(code)){
            return null;
        }
        for (SettleDetailScene payBillReason : SettleDetailScene.values()) {
            if (payBillReason.getCode().equals(code)) {
                return payBillReason.getDesc();
            }
        }
        return null;
    }

}
