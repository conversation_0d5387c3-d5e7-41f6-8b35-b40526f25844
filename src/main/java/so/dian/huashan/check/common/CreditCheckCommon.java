package so.dian.huashan.check.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.check.emuns.*;
import so.dian.huashan.check.manage.dto.CreditCheckDTO;
import so.dian.huashan.check.mapper.entity.CreditCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.CreditCheckDiffDO;
import so.dian.huashan.common.enums.CreditBizTypeEnum;
import so.dian.huashan.common.mapper.supplychain.*;
import so.dian.huashan.common.mapper.supplychain.dos.*;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
@Slf4j
@Component
public class CreditCheckCommon {
    @Autowired
    private ShipmentOrderMapper shipmentOrderMapper;
    @Autowired
    private ShipmentSubOrderMapper shipmentSubOrderMapper;
    @Autowired
    private TransferOrderMapper transferOrderMapper;

    @Autowired
    private CreditTradeMapper creditTradeMapper;
    @Autowired
    private ShipmentTradeMapper shipmentTradeMapper;
    @Autowired
    private CreditOrderMapper creditOrderMapper;
    @Autowired
    private TaskMasterMapper taskMasterMapper;

    @Autowired
    private TradeOrderProxySaleInfoMapper tradeOrderProxySaleInfoMapper;

    public CreditCheckDTO shipmentTradeCheckBySubOrder(List<Long> shipmentSubOrderIds, Integer checkType,String batchNo) {
        if (CollectionUtil.isEmpty(shipmentSubOrderIds)|| StringUtils.isBlank(batchNo)) {
            return null;
        }
        List<ShipmentSubOrderDO> shipmentSubOrderDOList = shipmentSubOrderMapper.selectByIds(shipmentSubOrderIds);
        if (CollectionUtils.isEmpty(shipmentSubOrderDOList)) {
            log.warn("供应链发货子单数据不存在/任务编排编号不存在 ids:{},batchNo:{}",shipmentSubOrderIds,batchNo);
            return null;
        }
        Set<String> shipmentSubOrderNos = shipmentSubOrderDOList.stream().map(ShipmentSubOrderDO::getShipmentOrderNo).collect(Collectors.toSet());
        List<ShipmentOrderDO> shipmentOrderDOList = shipmentOrderMapper.selectByShipmentOrderNos(new ArrayList<>(shipmentSubOrderNos));
        if (CollectionUtils.isEmpty(shipmentOrderDOList)) {
            log.warn("shipmentTradeCheckBySubOrder get shipmentSubOrderNos:{}",shipmentSubOrderNos);
            return null;
        }
        Map<String, Integer> shipmentOrderMap = shipmentOrderDOList.stream().collect(Collectors.toMap(ShipmentOrderDO::getShipmentOrderNo, ShipmentOrderDO::getStatus));
        return shipmentOrderImplementationDetail(shipmentOrderMap,shipmentSubOrderDOList,checkType,batchNo);
    }
    public CreditCheckDTO shipmentOrderCheck(List<Long> ids, Integer checkType,String batchNo) {
        if (CollectionUtil.isEmpty(ids)|| StringUtils.isBlank(batchNo)) {
            return null;
        }
        List<ShipmentOrderDO> shipmentOrderDOList = shipmentOrderMapper.selectByIds(ids);
        if (CollectionUtil.isEmpty(shipmentOrderDOList)) {
            log.warn("供应链发货单数据不存在/任务编排编号不存在 ids:{},batchNo:{}",ids,batchNo);
            return null;
        }
        // 获取发货子单
        List<String> shipmentOrderNos = new ArrayList<>();
        // 获取发货单状态
        Map<String, Integer> shipmentOrderMap = new HashMap<>();
        for (ShipmentOrderDO shipmentOrderDO : shipmentOrderDOList) {
            shipmentOrderNos.add(shipmentOrderDO.getShipmentOrderNo());
            shipmentOrderMap.put(shipmentOrderDO.getShipmentOrderNo(), shipmentOrderDO.getStatus());
        }
        // 获取发货子单列表
        return shipmentOrderImplementationDetail(shipmentOrderMap,shipmentSubOrderMapper.selectByShipmentOrderNos(shipmentOrderNos),checkType,batchNo);
    }

    private CreditCheckDTO shipmentOrderImplementationDetail(Map<String, Integer> shipmentOrderMap,List<ShipmentSubOrderDO> shipmentSubOrderDOList, Integer checkType,String batchNo) {
        if (CollectionUtils.isEmpty(shipmentSubOrderDOList)||Objects.isNull(shipmentOrderMap)){
            log.warn("供应链发货子单数据不存在/任务编排编号不存在 batchNo:{}",batchNo);
            return null;
        }
        List<String> shipmentSubOrderNos = shipmentSubOrderDOList.stream().map(ShipmentSubOrderDO::getShipmentSubOrderNo).collect(Collectors.toList());
        Map<String, CreditOrderDO> creditOrderMap = buildCreditOrderMap(shipmentSubOrderNos);
        List<CreditCheckConsistencyDO> creditCheckConsistencyDOList = new ArrayList<>();
        List<CreditCheckDiffDO> creditCheckDiffDOList = new ArrayList<>();
        List<Long> diffIds = new ArrayList<>();

        for (ShipmentSubOrderDO shipmentSubOrderDO : shipmentSubOrderDOList) {
            // 创建时间在2025年1月1日之前的不参与校验
            if (shipmentSubOrderDO.getCreateTime().getTime() < 1735660800000L) {
                continue;
            }
            // 授信
            CreditOrderDO creditOrderDO = creditOrderMap.get(shipmentSubOrderDO.getShipmentSubOrderNo());
            if (Objects.isNull(creditOrderDO)) {
                creditCheckDiffDOList.add(initCreditCheckDiffDO(shipmentSubOrderDO.getId(), CreditBizTypeEnum.ORDER.getCode(), DiffReasonEnum.CREDIT_LOSS.getCode(),batchNo));
                continue;
            }
            Integer status = shipmentOrderMap.get(shipmentSubOrderDO.getShipmentOrderNo());
            if (Objects.isNull(status)) {
                continue;
            }
            List<Integer> shipStatusList = Arrays.asList(ShipmentOrderStatusEnum.PARTIAL_SHIPPED.getCode(), ShipmentOrderStatusEnum.COMPLETE_SHIPPED.getCode());
            if (!((CreditOrderStatusEnum.CANCELLED.getCode().equals(creditOrderDO.getOrderStatus()) && ShipmentOrderStatusEnum.CANCEL_SHIPPED.getCode().equals(status))
                    || (CreditOrderStatusEnum.PERIOD.getCode().equals(creditOrderDO.getOrderStatus()) && shipStatusList.contains(status)))) {
                creditCheckDiffDOList.add(initCreditCheckDiffDO(shipmentSubOrderDO.getId(), CreditBizTypeEnum.ORDER.getCode(), DiffReasonEnum.INCONSISTENT_STATUS.getCode(),batchNo));
                continue;
            }
            // 逻辑删除异常
            diffIds.add(shipmentSubOrderDO.getId());
            creditCheckConsistencyDOList.add(initCreditCheckConsistencyDO(shipmentSubOrderDO.getId(), CreditBizTypeEnum.ORDER.getCode(), checkType,batchNo));
        }
        return CreditCheckDTO.builder()
                .creditCheckConsistencyDOList(creditCheckConsistencyDOList)
                .deleteDiffIds(diffIds)
                .bizType(CreditBizTypeEnum.ORDER.getCode())
                .creditCheckDiffDOList(creditCheckDiffDOList)
                .build();
    }

    public CreditCheckDTO transferOrderCheck(List<Long> ids, Integer checkType,String batchNo) {
        if (CollUtil.isEmpty(ids)|| StringUtils.isBlank(batchNo)) {
            log.warn("供应链转让单数据不存在/任务编排编号不存在 ids:{},batchNo:{}",ids,batchNo);
            return null;
        }
        List<TransferOrderDO> transferOrderDOList = transferOrderMapper.selectByIds(ids);
        if (CollUtil.isEmpty(transferOrderDOList)) {
            return null;
        }
        List<String> orderNoList = transferOrderDOList.stream().map(TransferOrderDO::getOrderNo).collect(Collectors.toList());
        Map<String, String> map = builCreditTradeBizNoMap(orderNoList);
        List<CreditCheckConsistencyDO> creditCheckConsistencyDOList = new ArrayList<>();
        List<CreditCheckDiffDO> creditCheckDiffDOList = new ArrayList<>();
        List<Long> diffIds = new ArrayList<>();
        for (TransferOrderDO transferOrderDO : transferOrderDOList) {
            if (Objects.isNull(map.get(transferOrderDO.getOrderNo()))) {
                // 获取对账差异
                creditCheckDiffDOList.add(initCreditCheckDiffDO(transferOrderDO.getId(), CreditBizTypeEnum.TRANSFER_ORDER.getCode(), DiffReasonEnum.CREDIT_LOSS.getCode(),batchNo));
                continue;
            }
            diffIds.add(transferOrderDO.getId());
            // 一致性凭证
            creditCheckConsistencyDOList.add(initCreditCheckConsistencyDO(transferOrderDO.getId(), CreditBizTypeEnum.TRANSFER_ORDER.getCode(), checkType,batchNo));
        }
        return CreditCheckDTO.builder()
                .creditCheckConsistencyDOList(creditCheckConsistencyDOList)
                .deleteDiffIds(diffIds)
                .bizType(CreditBizTypeEnum.TRANSFER_ORDER.getCode())
                .creditCheckDiffDOList(creditCheckDiffDOList)
                .build();
    }

    public CreditCheckDTO shipmentTradeCheck(List<Long> ids, Integer checkType,String batchNo) {
        // 获取数据
        List<ShipmentTradeDO> shipmentTradeDOList = shipmentTradeMapper.selectByIds(ids);
        if (CollUtil.isEmpty(shipmentTradeDOList)|| StringUtils.isBlank(batchNo)) {
            log.warn("供应链交易数据不存在/任务编排编号不存在 ids:{},batchNo:{}",ids,batchNo);
            return null;
        }
        List<String> orderNos = shipmentTradeDOList.stream().filter(s -> Objects.nonNull(s.getOrderNo())).map(k -> String.valueOf(k.getOrderNo())).collect(Collectors.toList());
        // 授信交易单号
        Map<String, CreditTradeDO> creditTradeMap = getCreditTradeMap(orderNos);

        List<CreditCheckConsistencyDO> creditCheckConsistencyDOList = new ArrayList<>();
        List<CreditCheckDiffDO> creditCheckDiffDOList = new ArrayList<>();
        List<Long> diffIds = new ArrayList<>();
        for (ShipmentTradeDO shipmentTradeDO : shipmentTradeDOList) {
            CreditTradeDO creditTradeDO = creditTradeMap.get(String.valueOf(shipmentTradeDO.getOrderNo()));
            if (Objects.isNull(creditTradeDO)) {
                TradeOrderProxySaleInfoDO tradeOrderProxySaleInfoDO = tradeOrderProxySaleInfoMapper.selectByTradeNo(shipmentTradeDO.getOrderNo());
                if(Objects.isNull(tradeOrderProxySaleInfoDO)){
                    creditCheckDiffDOList.add(initCreditCheckDiffDO(shipmentTradeDO.getId(), CreditBizTypeEnum.TRADE.getCode(), DiffReasonEnum.CREDIT_LOSS.getCode(),batchNo));
                    continue;
                }
                if(Objects.nonNull(tradeOrderProxySaleInfoDO) && tradeOrderProxySaleInfoDO.getWaitPayAmount() > 0 && tradeOrderProxySaleInfoDO.getPayType() == 1){
                    creditCheckDiffDOList.add(initCreditCheckDiffDO(shipmentTradeDO.getId(), CreditBizTypeEnum.TRADE.getCode(), DiffReasonEnum.CREDIT_LOSS.getCode(),batchNo));
                    continue;
                }
                continue;
            }
            Long refundTag = 32L;
            if ((CreditTradeStatusEnum.REFUND.getCode().equals(creditTradeDO.getTradeStatus()) && (shipmentTradeDO.getTag() & refundTag) == 0) || !CreditTradeStatusEnum.REFUND.getCode().equals(creditTradeDO.getTradeStatus()) && (shipmentTradeDO.getTag() & refundTag) > 0) {
                creditCheckDiffDOList.add(initCreditCheckDiffDO(shipmentTradeDO.getId(), CreditBizTypeEnum.TRADE.getCode(), DiffReasonEnum.INCONSISTENT_STATUS.getCode(),batchNo));
                continue;
            }
            diffIds.add(shipmentTradeDO.getId());
            creditCheckConsistencyDOList.add(initCreditCheckConsistencyDO(shipmentTradeDO.getId(), CreditBizTypeEnum.TRADE.getCode(), checkType,batchNo));
        }
        return CreditCheckDTO.builder().creditCheckDiffDOList(creditCheckDiffDOList)
                .bizType(CreditBizTypeEnum.TRADE.getCode())
                .deleteDiffIds(diffIds)
                .creditCheckConsistencyDOList(creditCheckConsistencyDOList).build();
    }

    /**
     * 获取授信交易列表
     */
    private Map<String, CreditTradeDO> getCreditTradeMap(List<String> orderNos) {
        List<CreditTradeDO> creditTradeDOList = creditTradeMapper.selectByBizNosAndBizType(1, orderNos);
        if (CollectionUtil.isEmpty(creditTradeDOList)) {
            return Collections.emptyMap();
        }
        return creditTradeDOList.stream().collect(Collectors.toMap(CreditTradeDO::getBizNo, t -> t));
    }
    /**
     * 获取授信map
     */
    private Map<String, String> builCreditTradeBizNoMap(List<String> orderNoList) {
        if (CollUtil.isEmpty(orderNoList)) {
            return new HashMap<>();
        }
        List<CreditTradeDO> creditTradeDOList = creditTradeMapper.selectByBizNosAndBizType(2, orderNoList);

        Map<String, String> params = new HashMap<>();
        if (!CollUtil.isEmpty(creditTradeDOList)) {
            for (CreditTradeDO creditTradeDO : creditTradeDOList) {
                params.put(creditTradeDO.getBizNo(), creditTradeDO.getBizNo());
            }
        }
        return params;
    }
    /**
     * 获取map
     */
    private Map<String, CreditOrderDO> buildCreditOrderMap(List<String> shipmentSubOrderNos) {
        if (CollectionUtil.isEmpty(shipmentSubOrderNos)) {
            return Collections.emptyMap();
        }
        List<CreditOrderDO> creditOrderDOList = creditOrderMapper.selectByShipmentSubOrderNos(shipmentSubOrderNos);
        if (CollectionUtil.isEmpty(creditOrderDOList)) {
            return Collections.emptyMap();
        }
        return creditOrderDOList.stream().collect(Collectors.toMap(CreditOrderDO::getShipmentSubOrderNo, creditOrderDO -> creditOrderDO));
    }

    public CreditCheckDiffDO initCreditCheckDiffDO(Long bizId, Integer bizType, Integer reason,String batchNo) {
        CreditCheckDiffDO creditCheckDiffDO = new CreditCheckDiffDO();
        creditCheckDiffDO.init();
        creditCheckDiffDO.setBatchNo(batchNo);
        creditCheckDiffDO.setBizId(bizId);
        creditCheckDiffDO.setReason(reason);
        creditCheckDiffDO.setBizType(bizType);
        creditCheckDiffDO.setStatus(DiffStatusEnum.NOT_ACCOUNTS.getCode());
        return creditCheckDiffDO;
    }

    public CreditCheckConsistencyDO initCreditCheckConsistencyDO(Long bizId, Integer bizType, Integer checkType,String batchNo) {
        CreditCheckConsistencyDO creditCheckConsistencyDO = new CreditCheckConsistencyDO();
        creditCheckConsistencyDO.init();
        creditCheckConsistencyDO.setBatchNo(batchNo);
        creditCheckConsistencyDO.setBizId(bizId);
        creditCheckConsistencyDO.setBizType(bizType);
        creditCheckConsistencyDO.setCheckType(checkType);
        return creditCheckConsistencyDO;
    }


    public String getBatchNo(Long taskMasterId){
        if (Objects.isNull(taskMasterId)){
            return null;
        }
        TaskMasterDO taskMasterDO = taskMasterMapper.selectByPrimaryKey(taskMasterId);
        if (Objects.isNull(taskMasterDO)){
            return null;
        }
        return taskMasterDO.getBatchNo();
    }


}
