package so.dian.huashan.check.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GetCheckDiffResponse {

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 支付通道
     */
    @ApiModelProperty("支付通道")
    private Integer payChannel;

    /**
     * 支付通道名称
     */
    @ApiModelProperty("支付通道名称")
    private String payChannelName;

    /**
     * 交易时间（打款时间）
     */
    @ApiModelProperty("交易时间（打款时间）")
    private String tradeTime;
    /**
     * 支付单流水号
     */
    @ApiModelProperty("支付单流水号")
    private String tradeNo;
    /**
     * 支付通道交易流水号
     */
    @ApiModelProperty("支付通道交易流水号")
    private String channelTradeNo;
    /**
     * 付款主体
     */
    @ApiModelProperty("付款主体")
    private Integer paySubjectType;
    @ApiModelProperty("付款主体名称")
    private String paySubjectTypeName;


    /**
     *  付款公司
     */
    @ApiModelProperty("付款公司")
    private String paySubjectName;

    /**
     * 付款成功金额
     */
    @ApiModelProperty("付款成功金额")
    private Long paySuccessAmount;

    /**
     *  支付通道成功金额
     */
    @ApiModelProperty("支付通道成功金额")
    private Long payChannelSuccessAmount;
    /**
     * 支付差异金额
     */
    @ApiModelProperty("支付差异金额")
    private Long payDiffAmount;

    /**
     * 差异类型
     */
    @ApiModelProperty("差异类型")
    private Integer reason;

    /**
     * 差异类型名称 1-三方不存在，2-业务方不存在，3-金额不想等，4-招商退汇
     */
    @ApiModelProperty("差异类型名称 1-三方不存在，2-业务方不存在，3-金额不想等，4-招商退汇")
    private String reasonName;

    /**
     * 状态（对账结果）
     */
    @ApiModelProperty("状态（对账结果）0-未平账，1-手动平账，2-不参与平账（对账结果）")
    private Integer status;
    @ApiModelProperty("备注")
    private String remark;
}
