package so.dian.huashan.check.controller.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.huashan.model.dto.PageDTO;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class GetCheckDiffPageReq extends PageDTO {

    /**
     *  付款公司
     */
    @ApiModelProperty("付款公司Id")
    private String paySubjectId;
    /**
     * 支付通道
     */
    @ApiModelProperty("支付通道")
    private Integer payChannel;


    /**
     * 支付单流水号
     */
    @ApiModelProperty("支付单流水号")
    private String tradeNo;

    /**
     * 支付通道交易流水号
     */
    @ApiModelProperty("支付通道交易流水号")
    private String channelTradeNo;

    /**
     * 付款主体
     */
    @ApiModelProperty("付款主体")
    private Integer paySubjectType;

    /**
     * 交易开始时间（打款开始时间）
     */
    @ApiModelProperty("交易开始时间（打款开始时间）")
    private Date tradeStartTime;
    /**
     * 交易结束时间（打款结束时间）
     */
    @ApiModelProperty("交易结束时间（打款结束时间）")
    private Date tradeEndTime;

    /**
     *  状态  0-未平账，1-手动平账，2-不参与平账（对账结果）
     */
    @ApiModelProperty("状态  0-未平账，1-手动平账，2-不参与平账（对账结果）")
    private Integer status;
}
