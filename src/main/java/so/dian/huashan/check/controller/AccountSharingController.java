package so.dian.huashan.check.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;

import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.collection.job.FundBalanceJob;
import so.dian.huashan.collection.job.FundsBillCollectionJob;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.common.enums.ThirdpartyPayType;


@RestController
public class AccountSharingController {
    @Autowired
    private ThirdpartyPullService thirdpartyPullService;
    @Autowired
    private FundsBillCollectionJob fundsBillCollectionJob;
    @Autowired
    private FundBalanceJob fundBalanceJob;
    @GetMapping("/account/share/test")
    public BizResult<Boolean> accountShareTest(String param) {
        thirdpartyPullService.pull(ThirdpartyPayType.CHANNEL_SHARE_ACCOUNT, param);
        return BizResult.create(true);
    }
    @GetMapping("/fund/test")
    public BizResult<Boolean> fundTest(String param) {
        fundsBillCollectionJob.execute(param);
        return BizResult.create(true);
    }

    @GetMapping("/fund/balance/test")
    public BizResult<Boolean> fundBalanceTest(String param) {
        fundBalanceJob.execute(param);
        return BizResult.create(true);
    }
}
