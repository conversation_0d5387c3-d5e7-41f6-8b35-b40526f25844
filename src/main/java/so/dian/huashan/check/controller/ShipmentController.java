package so.dian.huashan.check.controller;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huashan.check.job.credit.shipment.ShipmentDiffCheckJob;
import so.dian.huashan.check.job.credit.shipment.ShipmentOrderCheckJob;
import so.dian.huashan.check.job.credit.shipment.ShipmentTradeCheckJob;
import so.dian.huashan.check.job.credit.shipment.processor.CreditInstallReconciliationProcessor;
import so.dian.huashan.check.job.credit.transfer.TransferOrderJob;
import so.dian.huashan.check.job.iot.processor.SplitBillCheckDiffCompleteProcessor;
import so.dian.huashan.check.service.credit.shipment.ShipmentTradeService;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;

@Slf4j
@RestController
public class ShipmentController {

    @Autowired
    private ShipmentTradeCheckJob shipmentTradeCheckJob;
    @Autowired
    private ShipmentOrderCheckJob shipmentOrderCheckJob;
    @Autowired
    private TransferOrderJob transferOrderJob;
    @Autowired
    private ShipmentDiffCheckJob shipmentDiffCheckJob;
    @Autowired
    private CreditInstallReconciliationProcessor creditInstallReconciliationProcessor;

    @GetMapping("/test/tradeInstall")
    public void shipTradeInstall(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("22222");
        taskBaseParam.setTaskCode("423232");
        shipmentTradeCheckJob.shipmentTradeInstall(taskBaseParam);
    }

    @GetMapping("/test/tradeExecute")
    public void shipTradeExecute(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222221");
        taskBaseParam.setTaskCode("4232321");
        shipmentTradeCheckJob.shipmentTradeExecute(taskBaseParam);
    }

    @GetMapping("/test/orderInstall")
    public void shipOrderInstall(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222222");
        taskBaseParam.setTaskCode("4232322");
        shipmentOrderCheckJob.shipmentOrderInstall(taskBaseParam);
    }

    @GetMapping("/test/orderExecute")
    public void shipOrderExecute(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222223");
        taskBaseParam.setTaskCode("4232323");
        shipmentOrderCheckJob.shipmentOrderExecute(taskBaseParam);
    }

    @GetMapping("/test/transferInstall")
    public void shipTransferInstall(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222224");
        taskBaseParam.setTaskCode("4232324");
        transferOrderJob.transferOrderInstall(taskBaseParam);
    }

    @GetMapping("/test/transferExecute")
    public void shipTransferExecute(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222225");
        taskBaseParam.setTaskCode("4232325");
        transferOrderJob.shipmentTradeExecute(taskBaseParam);
    }

    @GetMapping("/test/shipmentDiffInstall")
    public void shipmentDiffInstall(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222226");
        taskBaseParam.setTaskCode("4232326");
        shipmentDiffCheckJob.shipmentDiffInstall(taskBaseParam);
    }

    @GetMapping("/test/shipmentDiffExecute")
    public void shipmentDiffExecute(){
        TaskBaseParam taskBaseParam = new TaskBaseParam();
        taskBaseParam.setBatchNo("222226");
        taskBaseParam.setTaskCode("4232326");
        shipmentDiffCheckJob.shipmentDiffExecute(taskBaseParam);
    }
    @GetMapping("/test/alarm")
    public void test(){
        ShardCompleteMessage shardCompleteMessage = ShardCompleteMessage.parse("{\"jobName\":\"settleIncrementCheckJob\",\"taskMasterId\":1,\"allShardCount\":2,\"successShardCount\":2,\"failShardCount\":0}");

        creditInstallReconciliationProcessor.process(shardCompleteMessage);
    }
}
