package so.dian.huashan.check.controller;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.check.service.ChannelAccountService;
import so.dian.huashan.task.enums.InvokeTypeEnum;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version: v 0.1 TestController.java, 2020-08-11 下午3:46 Exp $
 */
@Slf4j
@RequestMapping("/check")
@RestController
public class CheckController {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ThreadPoolTaskExecutor commonExecutor;

    @Autowired
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private ChannelAccountService channelAccountService;

    @GetMapping("/preload")
    public String preload(HttpServletResponse response) throws IOException {
        return "OK";
    }

    @GetMapping("/insert")
    public String insert(){
        RAtomicLong times = redissonClient.getAtomicLong("huashan_times_2");
        for(int i=1;i<10;i++){
            commonExecutor.submit(() -> {
                try {
                    times.getAndIncrement();
                    log.info("times={}", times.get());
                } catch (Exception e) {
                    log.error(">>>> 异步执对账逻辑", e);
                }
            });
        }
        while (true){
            if(times.get() == 9L){
                break;
            }
            try {
                TimeUnit.SECONDS.sleep(60);
            } catch (InterruptedException e) {
                log.error("休眠异常");
            }
        }
        log.info("times1111={}", times.get());
        return "OK";
    }

    @GetMapping("manual/retry/job")
    public BizResult<String> manualRetryJob(@RequestParam("taskMasterId") Long taskMasterId) {
        TaskMasterDO taskMasterDO = taskMasterMapper.selectByPrimaryKey(taskMasterId);
        if (Objects.nonNull(taskMasterDO))
            channelAccountService.channel(taskMasterDO, InvokeTypeEnum.from(taskMasterDO.getInvokeType()));

        return BizResult.create("成功");
    }
}