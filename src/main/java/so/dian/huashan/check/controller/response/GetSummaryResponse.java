package so.dian.huashan.check.controller.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class GetSummaryResponse {
    /**
     *  付款主体
     */
    @ApiModelProperty("付款主体")
    private Integer paySubjectType;
    @ApiModelProperty("付款主体名称")
    private String paySubjectTypeName;

    /**
     *  付款公司
     */
    @ApiModelProperty("付款公司")
    private String paySubjectName;

    @ApiModelProperty("付款公司Id")
    private Long paySubjectId;

    /**
     * 支付通道
     */
    @ApiModelProperty("支付通道")
    private Integer payChannel;
    /**
     * 支付通道名称
     */
    @ApiModelProperty("支付通道名称")
    private String payChannelName;

    /**
     * 核对日
     */
    @ApiModelProperty("核对日")
    private String checkDate;

    /**
     *  差异金额
     */
    @ApiModelProperty("差异金额")
    private Long diffAmount;

    /**
     * 差异笔数
     */
    @ApiModelProperty("差异笔数")
    private Integer diffNum;

}
