package so.dian.huashan.check.controller.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.huashan.model.dto.PageDTO;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class GetSummaryPageReq extends PageDTO {

    /**
     *  付款主体
     */
    @ApiModelProperty("付款主体")
    private Integer paySubjectType;

    /**
     *  付款公司
     */
    @ApiModelProperty("付款公司id")
    private Long paySubjectId;

    /**
     * 支付通道
     */
    @ApiModelProperty("支付通道")
    private Integer payChannel;

    /**
     * 核对开始日期
     */
    @ApiModelProperty("核对开始日期")
    private Date checkStartDate;
    /**
     * 核对结束日期
     */
    @ApiModelProperty("核对结束日期")
    private Date checkEndDate;
}
