package so.dian.huashan.check.controller;


import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.entity.PageData;
import so.dian.huashan.check.controller.req.CallBackXiYouKeUrlReq;
import so.dian.huashan.check.controller.req.GetCheckDiffPageReq;
import so.dian.huashan.check.controller.req.GetSummaryPageReq;
import so.dian.huashan.check.controller.req.PayBillModifyReq;
import so.dian.huashan.check.controller.response.GetCheckDiffResponse;
import so.dian.huashan.check.controller.response.GetSummaryResponse;
import so.dian.huashan.check.service.pay.PayBillService;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.model.dto.ManualReconciliationDTO;
import so.dian.huashan.model.dto.PullJobDTO;

import java.util.List;

@Slf4j
@RestController
public class PayBillController {

    @Autowired
    private PayBillService payBillService;
    @Autowired
    private ThirdpartyPullService thirdpartyPullService;

    /**
     * 付款对账汇总（新）
     */
    @PostMapping("/crm/payBill/getSummary")
    public BizResult<PageData<GetSummaryResponse>> getSummary(@RequestBody GetSummaryPageReq getSummaryPageReq) {
        return BizResult.create(payBillService.getSummary(getSummaryPageReq));
    }

    /**
     * 付款对账明细（新）
     */
    @PostMapping("/crm/payBill/getCheckDiff")
    public BizResult<PageData<GetCheckDiffResponse>> getCheckDiff(@RequestBody GetCheckDiffPageReq getCheckDiffPageReq) {
        return BizResult.create(payBillService.getCheckDiff(getCheckDiffPageReq));
    }
    /**
     * 手动平账
     */
    @PostMapping("/crm/payBill/modify")
    public BizResult<Boolean> modify(@RequestBody PayBillModifyReq req) {
        payBillService.manualReconciliation(ManualReconciliationDTO.builder()
                .id(req.getId())
                .remark(req.getRemark())
                .status(req.getStatus())
                .build());
        return BizResult.create(true);
    }

    @PostMapping("/xiyouke/callback/downloadUrl")
    public BizResult<Boolean> callBackDownloadUrl(@RequestBody CallBackXiYouKeUrlReq req){
        log.info("callBackDownloadUrl get req:{}", JSON.toJSON(req));
        thirdpartyPullService.pullXiYouKeJob(PullJobDTO.builder()
                        .url(req.getBody().getPaymentChangeUrl())
                        .payType(ThirdpartyPayType.XIYOUKE)
                        .beginDate(req.getBody().getStartTime())
                        .partnerOrderId(req.getBody().getPartnerOrderId())
                .build());
        return BizResult.create(true);
    }

}
