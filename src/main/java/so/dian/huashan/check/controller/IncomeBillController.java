package so.dian.huashan.check.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.check.controller.req.IncomeDiffBillReq;
import so.dian.huashan.check.service.income.IncomeBillService;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2024/03/29 13:41
 * @description:
 */
@RestController
public class IncomeBillController {

    @Autowired
    private IncomeBillService incomeBillService;

    @PostMapping("incomeBill/channelDiff/modify/type")
    public BizResult<Boolean> batchModifyType(@RequestBody IncomeDiffBillReq billReq) {
        return BizResult.create(incomeBillService.batchModifyType(billReq));
    }
}
