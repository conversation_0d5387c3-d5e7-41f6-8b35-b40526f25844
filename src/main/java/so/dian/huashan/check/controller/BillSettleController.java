package so.dian.huashan.check.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.check.service.settle.BillSettleCheckService;
import so.dian.huashan.check.service.settle.ControlOrderCheckService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/20 10:14
 * @description: 清结算业务前端接口
 */
@RestController
public class BillSettleController {

    @Autowired
    private BillSettleCheckService settleCheckService;

    @Resource
    private ControlOrderCheckService controlOrderCheckService;

    /**
     * 手动平账
     * @param diffIds 差异表ID集合
     */
    @PostMapping("billSettle/manual/consistency")
    public BizResult<Boolean> manualConsistency(@RequestBody List<Long> diffIds) {
        settleCheckService.manualConsistency(diffIds);
        return BizResult.create(Boolean.TRUE);
    }

    /**
     * 抽单池手动平账
     * @param diffIds 差异表ID集合
     */
    @PostMapping("billSettle/control/manual/consistency")
    public BizResult<Boolean> controlManualConsistency(@RequestBody List<Long> diffIds) {
        controlOrderCheckService.manualConsistency(diffIds);
        return BizResult.create(Boolean.TRUE);
    }


}
