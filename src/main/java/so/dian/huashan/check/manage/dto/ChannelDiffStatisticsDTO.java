package so.dian.huashan.check.manage.dto;

import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/04/01 09:56
 * @description:
 */
@Data
public class ChannelDiffStatisticsDTO {

    private Integer diffCount;

    private Long amount;

    private String tradeType;

    public static ChannelDiffStatisticsDTO zeroValue() {
        ChannelDiffStatisticsDTO statisticsDTO = new ChannelDiffStatisticsDTO();
        statisticsDTO.setDiffCount(0);
        statisticsDTO.setAmount(0L);
        return statisticsDTO;
    }

    public static ChannelDiffStatisticsDTO zeroValue(String tradeType) {
        ChannelDiffStatisticsDTO statisticsDTO = new ChannelDiffStatisticsDTO();
        statisticsDTO.setDiffCount(0);
        statisticsDTO.setAmount(0L);
        statisticsDTO.setTradeType(tradeType);
        return statisticsDTO;
    }
}
