package so.dian.huashan.check.manage.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.huashan.check.mapper.entity.CreditCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.CreditCheckDiffDO;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreditCheckDTO {

    /**
     * 凭证表
     */
    private List<CreditCheckConsistencyDO> creditCheckConsistencyDOList;

    private Integer bizType;

    /**
     * 删除差异表
     */
    private List<Long> deleteDiffIds;

    /**
     * 差异表
     */
    private List<CreditCheckDiffDO> creditCheckDiffDOList;
}
