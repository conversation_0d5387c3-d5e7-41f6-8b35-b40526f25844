package so.dian.huashan.check.manage.convert;

import so.dian.huashan.check.manage.bos.IncomeOriginalInfoBO;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/12 16:08
 * @description:
 */
public class IncomeOriginalInfoConvert {

    public IncomeOriginalInfoBO toBO(IncomeOriginalInfoDO infoDO) {

        IncomeOriginalInfoBO originalInfoBO = new IncomeOriginalInfoBO();
        originalInfoBO.setId(infoDO.getId());
        originalInfoBO.setIdempotentNo(infoDO.getIdempotentNo());
        originalInfoBO.setVoucherId(infoDO.getVoucherId());
        originalInfoBO.setDataSource(infoDO.getDataSource());
        originalInfoBO.setTradeType(infoDO.getTradeType());
        originalInfoBO.setBizType(infoDO.getBizType());
        originalInfoBO.setPayWay(infoDO.getPayWay());
        originalInfoBO.setPayNo(infoDO.getPayNo());
        originalInfoBO.setTradeAmount(infoDO.getTradeAmount());
        originalInfoBO.setTradeTime(infoDO.getTradeTime());
        originalInfoBO.setHappenDate(infoDO.getHappenDate());
        originalInfoBO.setStatus(infoDO.getStatus());
        originalInfoBO.setTradeNo(infoDO.getTradeNo());
        originalInfoBO.setDianPayNo(infoDO.getDianPayNo());
        originalInfoBO.setChannelTradeNo(infoDO.getChannelTradeNo());

        return originalInfoBO;
    }
}
