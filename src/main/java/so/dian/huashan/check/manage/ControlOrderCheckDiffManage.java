package so.dian.huashan.check.manage;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.check.mapper.ControlOrderCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO;

import java.util.List;
import java.util.Objects;

@Component
public class ControlOrderCheckDiffManage {

    @Autowired
    private ControlOrderCheckDiffMapper checkDiffMapper;

    @Transactional
    public void batchAddOrModify(List<ControlOrderCheckDiffDO> checkDiffs) {
        if (CollUtil.isEmpty(checkDiffs))
            return;

        List<ControlOrderCheckDiffDO> toAdds = Lists.newArrayList();
        List<Long> toModifys = Lists.newArrayList();
        for (ControlOrderCheckDiffDO checkDiff : checkDiffs) {
            if (Objects.nonNull(checkDiff.getId())) {
                toModifys.add(checkDiff.getId());
            }else {
                toAdds.add(checkDiff);
            }
        }

        if (CollUtil.isNotEmpty(toAdds))
            checkDiffMapper.batchInsert(toAdds);

        if (CollUtil.isNotEmpty(toModifys))
            checkDiffMapper.batchLogicDeleteByIds(toModifys, null);
    }
}
