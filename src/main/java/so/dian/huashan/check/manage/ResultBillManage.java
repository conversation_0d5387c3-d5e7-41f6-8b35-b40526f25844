package so.dian.huashan.check.manage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.check.mapper.ResultBillMapper;
import so.dian.huashan.check.mapper.entity.ResultBillDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static so.dian.huashan.common.constant.CommonConstants.CURRENCY_RMB;
import static so.dian.huashan.common.constant.CommonConstants.XIAO_DIAN_CODE;
import static so.dian.huashan.common.constant.CommonConstants.XIAO_DIAN_NAME;
import static so.dian.huashan.common.enums.SubjectType.OUR_COMPANY;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class ResultBillManage {

    @Resource
    private ResultBillMapper resultBillMapper;
    public void insert(ChannelCheckContext billDO){
        ResultBillDO resultBillDO = resultBillMapper.selectByKey(billDO.getTradeType(), billDO.getTradeNo(), billDO.getAmount(), billDO.getPayWay());
        if(resultBillDO != null){
            resultBillDO.setBillDate(billDO.getBillDate());
            resultBillDO.setCheckDate(billDO.getCheckDate());
            resultBillDO.setBillId(billDO.getBillId());
            resultBillDO.modify();
            resultBillMapper.updateByPrimaryKeySelective(resultBillDO);
        }else{
            resultBillDO = new ResultBillDO();
            resultBillDO.setBillId(billDO.getBillId());
            resultBillDO.setBizType(billDO.getBizType());
            resultBillDO.setTradeNo(billDO.getTradeNo());
            resultBillDO.setTradeType(billDO.getTradeType());
            resultBillDO.setBillDate(billDO.getBillDate());
            resultBillDO.setAmount(billDO.getAmount());
            resultBillDO.setPayWay(billDO.getPayWay());
            resultBillDO.setCheckDate(billDO.getCheckDate());
            resultBillDO.setCheckResult(billDO.getCheckResult());
            resultBillDO.init();
            resultBillMapper.insertSelective(resultBillDO);
        }

    }

    public void batchAddOrModify(List<ChannelCheckContext> resultList) {
        if(CollectionUtil.isEmpty(resultList))
            return;

        List<ResultBillDO> resultBillList = resultList.stream().map(o -> {
            ResultBillDO resultBillDO = new ResultBillDO();
            resultBillDO.setBillId(o.getBillId());
            resultBillDO.setDianPayNo(o.getDianPayNo());
            resultBillDO.setChannelTradeNo(o.getChannelTradeNo());
            resultBillDO.setBizType(o.getBizType());
            resultBillDO.setTradeNo(o.getTradeNo());
            resultBillDO.setTradeType(o.getTradeType());
            resultBillDO.setBillDate(o.getBillDate());
            resultBillDO.setAmount(o.getAmount());
            resultBillDO.setPayWay(o.getPayWay());
            resultBillDO.setCheckDate(Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN)));
            resultBillDO.setCheckResult(o.getCheckResult());
            resultBillDO.setMchOrderNo(o.getBizNo1());
            resultBillDO.setMchId(o.getMchId());
            resultBillDO.setCurrency(CURRENCY_RMB);
            resultBillDO.setSubjectCode(XIAO_DIAN_CODE);
            resultBillDO.setSubjectName(XIAO_DIAN_NAME);
            resultBillDO.setSubjectType(OUR_COMPANY.getCode());
            resultBillDO.setIdempotentNo(StrUtil.join(StrUtil.COLON, resultBillDO.getTradeNo(), resultBillDO.getTradeType(), resultBillDO.getDianPayNo()));
            resultBillDO.init();
            return resultBillDO;
        }).collect(Collectors.toList());
        resultBillMapper.insertOrUpdateBatch(resultBillList);
    }
}
