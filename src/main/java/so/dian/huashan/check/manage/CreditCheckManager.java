package so.dian.huashan.check.manage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.check.manage.dto.CreditCheckDTO;
import so.dian.huashan.check.mapper.CreditCheckConsistencyMapper;
import so.dian.huashan.check.mapper.CreditCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.CreditCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.CreditCheckDiffDO;

import java.util.List;
import java.util.Objects;

@Component
public class CreditCheckManager {
    @Autowired
    private CreditCheckConsistencyMapper creditCheckConsistencyMapper;
    @Autowired
    private CreditCheckDiffMapper creditCheckDiffMapper;

    /**
     * 批量添加
     */
    @Transactional
    public void batchSaveAndDelete(List<CreditCheckDTO> creditCheckDTOS) {
        if (CollectionUtils.isEmpty(creditCheckDTOS)) {
            return;
        }
        for (CreditCheckDTO creditCheckDTO : creditCheckDTOS) {
            if (!CollectionUtils.isEmpty(creditCheckDTO.getDeleteDiffIds()) && Objects.nonNull(creditCheckDTO.getBizType())) {
                creditCheckDiffMapper.deleteByBizIdAndBizType(creditCheckDTO.getDeleteDiffIds(), creditCheckDTO.getBizType());
            }
            if (!CollectionUtils.isEmpty(creditCheckDTO.getCreditCheckConsistencyDOList())) {
                creditCheckConsistencyMapper.insertBatch(creditCheckDTO.getCreditCheckConsistencyDOList());
            }
            if (!CollectionUtils.isEmpty(creditCheckDTO.getCreditCheckDiffDOList())) {
                creditCheckDiffMapper.insertBatch(creditCheckDTO.getCreditCheckDiffDOList());
            }
        }
    }

    @Transactional
    public void diffBatchSaveAndDelete(List<CreditCheckDTO> creditCheckDTOS,List<Long> updateSettleStatusDiffIds) {
        if (!CollectionUtils.isEmpty(updateSettleStatusDiffIds)){
            creditCheckDiffMapper.updateStatusByIds(updateSettleStatusDiffIds);
        }
        // 批量添加
        batchSaveAndDelete(creditCheckDTOS);
    }
}
