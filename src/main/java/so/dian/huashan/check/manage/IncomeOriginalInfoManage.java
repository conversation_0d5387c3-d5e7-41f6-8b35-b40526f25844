package so.dian.huashan.check.manage;

import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.manage.bos.IncomeOriginalInfoBO;
import so.dian.huashan.check.manage.convert.IncomeOriginalInfoConvert;
import so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/10/12 16:07
 * @description:
 */
@Component
public class IncomeOriginalInfoManage {

    @Autowired
    private IncomeOriginalInfoMapper incomeOriginalInfoMapper;

    List<IncomeOriginalInfoBO> listByPayNo(List<String> payNos) {
        if (CollUtil.isEmpty(payNos))
            return Collections.emptyList();

        List<IncomeOriginalInfoDO> originalInfoDOS = incomeOriginalInfoMapper.selectBatchByPayNos(payNos);
        IncomeOriginalInfoConvert convert = new IncomeOriginalInfoConvert();
        return originalInfoDOS.stream().map(convert::toBO).collect(Collectors.toList());
    }
}
