package so.dian.huashan.check.manage.bos;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.Date;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/10/12 16:07
 * @description:
 */
@Data
public class IncomeOriginalInfoBO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 幂等键
     */
    private String idempotentNo;

    /**
     * 凭证id
     */
    private Long voucherId;

    /**
     * (1、payment,2、hera_trans_order,3、refund_order_payment_channel_map_relation,4、offline_pay)
     */
    private Integer dataSource;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 支付方式（1-支付宝，3-微信，4-苏宁，5-QQ，6-银联，7-会员，8-优惠券，9-积分，10-钱包或押金，11-宝付，12-微美）
     */
    private Integer payWay;

    /**
     * 渠道支付单号
     */
    private String payNo;

    /**
     * 交易金额，单位：分
     */
    private Integer tradeAmount;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 清洗组装日期
     */
    private Integer happenDate;

    /**
     * 状态（0-初始化，1-处理完成）
     */
    private Integer status;

    /**
     * 交易单号，存储的是交易系统trade_order的id字段的值
     */
    private String tradeNo;

    /**
     * 小电支付系统流水号，分别对应payment#trade_no和refund_order#refund_order_no
     */
    private String dianPayNo;

    /**
     * 第三方账单流水号
     */
    private String channelTradeNo;

    public String getCheckKey() {
        return StrUtil.join(StrUtil.COLON, payNo, tradeType, dianPayNo);
    }
}
