package so.dian.huashan.check.manage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.emuns.ChannelDiffOperationType;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.check.manage.dto.ChannelDiffStatisticsDTO;
import so.dian.huashan.check.mapper.ResultChannelBillMapper;
import so.dian.huashan.check.mapper.entity.ResultChannelBillDO;
import so.dian.huashan.common.enums.TradeType;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static so.dian.huashan.common.constant.CommonConstants.CURRENCY_RMB;
import static so.dian.huashan.common.constant.CommonConstants.XIAO_DIAN_CODE;
import static so.dian.huashan.common.constant.CommonConstants.XIAO_DIAN_NAME;
import static so.dian.huashan.common.enums.SubjectType.OUR_COMPANY;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class ResultChannelBillManage {

    @Resource
    private ResultChannelBillMapper resultChannelBillMapper;

    public void insert(ChannelCheckContext billDO){
        ResultChannelBillDO resultChannelBillDO = resultChannelBillMapper.selectByKey(billDO.getTradeType(), billDO.getTradeNo(), billDO.getAmount(), billDO.getPayWay());
        if(resultChannelBillDO != null){
            resultChannelBillDO.setBillDate(billDO.getBillDate());
            resultChannelBillDO.setCheckDate(billDO.getCheckDate());
            resultChannelBillDO.setBillId(billDO.getBillId());
            resultChannelBillDO.modify();
            resultChannelBillMapper.updateByPrimaryKeySelective(resultChannelBillDO);
        }else{
            resultChannelBillDO = new ResultChannelBillDO();
            resultChannelBillDO.setBillId(billDO.getBillId());
            resultChannelBillDO.setBizType(billDO.getBizType());
            resultChannelBillDO.setTradeNo(billDO.getTradeNo());
            resultChannelBillDO.setTradeType(billDO.getTradeType());
            resultChannelBillDO.setBillDate(billDO.getBillDate());
            resultChannelBillDO.setAmount(billDO.getAmount());
            resultChannelBillDO.setPayWay(billDO.getPayWay());
            resultChannelBillDO.setCheckDate(billDO.getCheckDate());
            resultChannelBillDO.setCheckResult(billDO.getCheckResult());
            resultChannelBillDO.init();
            resultChannelBillMapper.insertSelective(resultChannelBillDO);
        }

    }

    public void batchAddOrModify(List<ChannelCheckContext> resultChannelList) {

        if(CollectionUtil.isEmpty(resultChannelList)){
            return;
        }

        List<ResultChannelBillDO> resultChannelBillList = resultChannelList.stream().map(o -> {
            ResultChannelBillDO resultChannelBillDO = new ResultChannelBillDO();
            resultChannelBillDO.setBillId(o.getBillId());
            resultChannelBillDO.setDianPayNo(o.getDianPayNo());
            resultChannelBillDO.setChannelTradeNo(o.getChannelTradeNo());
            resultChannelBillDO.setBizType(o.getBizType());
            resultChannelBillDO.setTradeNo(o.getTradeNo());
            resultChannelBillDO.setTradeType(o.getTradeType());
            resultChannelBillDO.setBillDate(o.getBillDate());
            resultChannelBillDO.setAmount(o.getAmount());
            resultChannelBillDO.setPayWay(o.getPayWay());
            resultChannelBillDO.setCheckDate(Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN)));
            resultChannelBillDO.setCheckResult(o.getCheckResult());
            resultChannelBillDO.setMchOrderNo(o.getBizNo1());
            resultChannelBillDO.setMchId(o.getMchId());
            resultChannelBillDO.setCurrency(CURRENCY_RMB);
            resultChannelBillDO.setSubjectCode(XIAO_DIAN_CODE);
            resultChannelBillDO.setSubjectName(XIAO_DIAN_NAME);
            resultChannelBillDO.setSubjectType(OUR_COMPANY.getCode());
            resultChannelBillDO.setOperationType(ChannelDiffOperationType.NORMAL.getCode());
            resultChannelBillDO.setIdempotentNo(StrUtil.join(StrUtil.COLON, resultChannelBillDO.getTradeNo(), resultChannelBillDO.getTradeType(), resultChannelBillDO.getDianPayNo()));
            resultChannelBillDO.init();

            // 跑差异对账时，会存在历史数据业务类型为8的枚举值，需要标记删除
            if (Objects.nonNull(o.getBizType()) && o.getBizType().equals(8)) {
                resultChannelBillDO.setDeleted(1);
            }
            return resultChannelBillDO;
        }).collect(Collectors.toList());
        resultChannelBillMapper.insertOrUpdateBatch(resultChannelBillList);
    }

    public Boolean batchModifyType(List<Long> ids, ChannelDiffOperationType operationType) {
        if (CollUtil.isEmpty(ids) || Objects.isNull(operationType))
            return Boolean.FALSE;

        return resultChannelBillMapper.batchUpdateStatus(ids, operationType.getCode()) > 0;
    }

    public ChannelDiffStatisticsDTO diffStatistics(Long startCreate, Long endCreate) {
        if (Objects.isNull(startCreate) || Objects.isNull(endCreate))
            return ChannelDiffStatisticsDTO.zeroValue();

        return resultChannelBillMapper.diffStatistics(startCreate, endCreate);
    }

    public ChannelDiffStatisticsDTO squareAccountStatistics(Long startUpdate, Long endUpdate) {
        if (Objects.isNull(startUpdate) || Objects.isNull(endUpdate))
            return ChannelDiffStatisticsDTO.zeroValue();

        return resultChannelBillMapper.squareAccountStatistics(startUpdate, endUpdate);
    }

    public List<ChannelDiffStatisticsDTO> totalDiffStatistics(Date startBillDate, Date endBillDate) {
        if (Objects.isNull(startBillDate) || Objects.isNull(endBillDate))
            return Lists.newArrayList(ChannelDiffStatisticsDTO.zeroValue(TradeType.IN.getKey()),
                    ChannelDiffStatisticsDTO.zeroValue(TradeType.OUT.getKey()));

        return resultChannelBillMapper.totalDiffStatistics(startBillDate, endBillDate);
    }
}
