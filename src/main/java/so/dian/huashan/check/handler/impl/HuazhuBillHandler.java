package so.dian.huashan.check.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.handler.AbstractCheckHandler;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.collection.mapper.HuazhuIncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.HuazhuResultBillMapper;
import so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoExtDO;
import so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.common.mapper.lhc.OrderPartnerMappingMapper;
import so.dian.huashan.common.mapper.lhc.entity.OrderPartnerMappingDO;
import so.dian.huashan.common.service.DeviceService;
import so.dian.huashan.common.service.OrderBoxService;
import so.dian.huashan.common.service.ShopService;
import so.dian.huashan.common.service.entity.DeviceInfoBO;
import so.dian.huashan.common.service.entity.OrdersBoxBO;
import so.dian.huashan.common.service.entity.ShopCompanyBO;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.transaction.ManualTransaction;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static so.dian.huashan.common.constant.CommonConstants.CURRENCY_RMB;
import static so.dian.huashan.common.constant.CommonConstants.HUAZHU_BILL;

/**
 * @author: miaoshuai
 * @create: 2023/10/20 16:45
 * @description:
 */
@Slf4j
@Component(HUAZHU_BILL)
public class HuazhuBillHandler extends AbstractCheckHandler {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TaskSubMapper taskSubMapper;

    @Autowired
    private HuazhuIncomeOriginalInfoMapper huazhuIncomeOriginalInfoMapper;

    @Autowired
    private OrderPartnerMappingMapper orderPartnerMappingMapper;

    @Autowired
    private HuazhuResultBillMapper huazhuResultBillMapper;

    @Resource
    private ShopService shopService;

    @Resource
    private OrderBoxService orderBoxService;

    @Resource
    private DeviceService deviceService;

    @Override
    public void process(AbstractCheckContext abstractCheckContext) {

        log.info("华住账单生成开始执行，子任务ID:{}", abstractCheckContext.getTaskSubId());
        // 对子任务加锁
        RLock lock = redissonClient.getFairLock(CommonConstants.TASK_SUB + ":" + abstractCheckContext.getTaskSubId());
        try {
            if (lock.tryLock(30L, TimeUnit.SECONDS)){
                // 第一步查询出子对账任务，更新为对账中
                TaskSubDO taskSubDO = taskSubMapper.selectByPrimaryKey(abstractCheckContext.getTaskSubId());
                if(!taskSubDO.getStatus().equals(TaskSubStatusEnum.INIT.code()) && !taskSubDO.getStatus().equals(TaskSubStatusEnum.FAIL.code())){
                    return;
                }
                taskSubDO.setStatus(TaskSubStatusEnum.PROGRESS.code());
                taskSubDO.modify();
                taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
                executeBiz(abstractCheckContext);
                taskSubDO.setStatus(TaskSubStatusEnum.SUCCESS.code());
                taskSubDO.modify();
                taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
            }
        } catch (Exception e) {
            log.error("华住账单生成异常，HuazhuBillHandler.taskSubId:{}", abstractCheckContext.getTaskSubId(), e);
            TaskSubDO taskSubDO = new TaskSubDO();
            taskSubDO.setId(abstractCheckContext.getTaskSubId());
            taskSubDO.setStatus(TaskSubStatusEnum.FAIL.code());
            taskSubDO.modify();
            taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
            throw new RuntimeException("华住账单生成异常");
        }finally {
            lock.unlock();
        }
        log.info("华住账单生成执行结束，子任务ID:{}", abstractCheckContext.getTaskSubId());
    }

    public void executeBiz(AbstractCheckContext abstractChectContext){
        List<Long> ids = JSONObject.parseArray(abstractChectContext.getVoucher(),Long.class);
        List<HuazhuIncomeOriginalInfoExtDO> originalInfoExtDOS = huazhuIncomeOriginalInfoMapper.selectOriginalByIds(ids);
        if (CollUtil.isEmpty(originalInfoExtDOS)) {
            log.info("华住账单生成场景，查询不到华住账单底表数据, 子任务ID：{}", abstractChectContext.getTaskSubId());
            return;
        }

        List<String> orderNos = originalInfoExtDOS.stream().map(HuazhuIncomeOriginalInfoExtDO::getOrderNo).collect(Collectors.toList());
        List<OrdersBoxBO> ordersBoxBOS = orderBoxService.list(orderNos);
        Map<String, OrdersBoxBO> orderNoAdOrder = ordersBoxBOS.stream().collect(Collectors.toMap(OrdersBoxBO::getOrderNo, Function.identity()));

        List<OrderPartnerMappingDO> orderPartnerMappingDOS = orderPartnerMappingMapper.selectByOrderNos(orderNos);
        Map<String, OrderPartnerMappingDO> orderNoAndMapping = orderPartnerMappingDOS.stream().collect(Collectors.toMap(OrderPartnerMappingDO::getOrderNo, Function.identity()));

        List<String> loadDeviceNos = ordersBoxBOS.stream().map(OrdersBoxBO::getLoanBoxNo).collect(Collectors.toList());
        List<DeviceInfoBO> deviceInfoBOS = deviceService.list(loadDeviceNos);
        Map<String, DeviceInfoBO> deviceNoAndInfoMap = deviceInfoBOS.stream().collect(Collectors.toMap(DeviceInfoBO::getDeviceNo, Function.identity()));

        List<Long> loadShopIds = ordersBoxBOS.stream().map(OrdersBoxBO::getLoanShopId).collect(Collectors.toList());
        List<ShopCompanyBO> shopCompanies = shopService.list(loadShopIds);
        Map<Long, ShopCompanyBO> shopIdAndCompanyMap = shopCompanies.stream().collect(Collectors.toMap(ShopCompanyBO::getShopId, Function.identity()));

        List<HuazhuResultBillDO> huazhuResultBillDOS = Lists.newArrayList();
        List<Long> modifyIds = Lists.newArrayList();
        for (HuazhuIncomeOriginalInfoExtDO originalInfoExtDO : originalInfoExtDOS) {
            try {
                modifyIds.add(originalInfoExtDO.getId());
                HuazhuResultBillDO resultBillDO = new HuazhuResultBillDO();
                resultBillDO.setIncomeOriginalId(originalInfoExtDO.getIncomeOriginalId());
                resultBillDO.setBillDate(DateUtil.parseDateYyyyMMdd2Int(originalInfoExtDO.getTradeTime()));
                resultBillDO.setTradeNo(orderNoAndMapping.getOrDefault(originalInfoExtDO.getOrderNo(), new OrderPartnerMappingDO()).getPartnerOrder());
                resultBillDO.setDianTradeNo(originalInfoExtDO.getDianPayNo());
                resultBillDO.setBizOrderNo(originalInfoExtDO.getOrderNo());
                resultBillDO.setPayNo(originalInfoExtDO.getPayNo());
                resultBillDO.setPayType(originalInfoExtDO.getPayType());
                resultBillDO.setAmount(originalInfoExtDO.getTradeAmount().longValue());
                resultBillDO.setCurrency(CURRENCY_RMB);
                resultBillDO.setTradeTime(originalInfoExtDO.getTradeTime());
                resultBillDO.setTradeType(originalInfoExtDO.getTradeType());

                // 订单
                OrdersBoxBO ordersBoxBO = orderNoAdOrder.getOrDefault(originalInfoExtDO.getOrderNo(), OrdersBoxBO.builder().build());
                resultBillDO.setOrderAmount(ordersBoxBO.getOrderAmount());
                resultBillDO.setDeviceNo(ordersBoxBO.getPowerBankNo());
                resultBillDO.setOrderStopChargTime(ordersBoxBO.getReturnTime());
                resultBillDO.setIsCappedPrice(BooleanUtil.toByte(ordersBoxBO.isCappedPrice()));
                resultBillDO.setCappedPrice(ordersBoxBO.cappedPrice());

                // 产品信息
                DeviceInfoBO deviceInfoBO = deviceNoAndInfoMap.get(ordersBoxBO.getLoanBoxNo());
                resultBillDO.setSpuCode(Optional.ofNullable(deviceInfoBO).map(DeviceInfoBO::getSpuCode).orElse(null));
                resultBillDO.setSpuName(Optional.ofNullable(deviceInfoBO).map(DeviceInfoBO::getSpuName).orElse(null));
                resultBillDO.setProductCategory(Optional.ofNullable(deviceInfoBO).map(DeviceInfoBO::productCategoryId).orElse(null));
                resultBillDO.setProductCategoryDesc(Optional.ofNullable(deviceInfoBO).map(DeviceInfoBO::productCategoryDesc).orElse(null));

                // 借出门店所属公司
                ShopCompanyBO shopCompany = shopIdAndCompanyMap.getOrDefault(ordersBoxBO.getLoanShopId(), ShopCompanyBO.builder().build());
                resultBillDO.setLoanShopCompany(shopCompany.getCompanyName());
                resultBillDO.setLoanShopChannel(shopCompany.agentTypeName());

                resultBillDO.init();
                huazhuResultBillDOS.add(resultBillDO);
            }catch (Exception e) {
                log.error("华住账单处理失败，华住底表数据ID:{}", originalInfoExtDO.getId());
                throw e;
            }
        }

        ManualTransaction manualTransaction = ManualTransaction.builder().build();
        manualTransaction.invoke(t -> {
            huazhuIncomeOriginalInfoMapper.batchUpdateStatus(modifyIds, (byte)1);
            huazhuResultBillMapper.batchInsert(huazhuResultBillDOS);
        });
    }
}
