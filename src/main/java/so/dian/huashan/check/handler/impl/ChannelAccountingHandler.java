package so.dian.huashan.check.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.check.handler.AbstractCheckHandler;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.collection.mapper.ThirdPartyBillMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.common.enums.BillResource;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static so.dian.huashan.common.constant.ChannelConstant.ALIPAY_CONSUME_DONATE;

/**
 * TaskSubHandler
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 10:00
 */
@Slf4j
@Component(value = CommonConstants.CHANNEL_ACCOUNT)
public class ChannelAccountingHandler extends AbstractCheckHandler{

    @Resource
    private ThirdPartyBillMapper thirdPartyBillMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TaskSubMapper taskSubMapper;

    @Autowired
    private ChannelAccountingHandler self;

    @Override
    public void process(AbstractCheckContext abstractCheckContext) {
        log.info("渠道对账开始执行，子任务ID:{}", abstractCheckContext.getTaskSubId());
        // 对子任务加锁
        RLock lock = redissonClient.getFairLock(CommonConstants.TASK_SUB + ":" + abstractCheckContext.getTaskSubId());
        try {
            if (lock.tryLock(30L, TimeUnit.SECONDS)){
                // 第一步查询出子对账任务，更新为对账中
                TaskSubDO taskSubDO = taskSubMapper.selectByPrimaryKey(abstractCheckContext.getTaskSubId());
                if(!taskSubDO.getStatus().equals(TaskSubStatusEnum.INIT.code()) && !taskSubDO.getStatus().equals(TaskSubStatusEnum.FAIL.code())){
                    return;
                }
                taskSubDO.setStatus(TaskSubStatusEnum.PROGRESS.code());
                taskSubDO.modify();
                taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
                self.executeBiz(abstractCheckContext);
                taskSubDO.setStatus(TaskSubStatusEnum.SUCCESS.code());
                taskSubDO.modify();
                taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
            }
        } catch (Exception e) {
            log.error("创建渠道对账异常-ChannelAccountingHandler = {}", JSON.toJSONString(abstractCheckContext),e);
            TaskSubDO taskSubDO = new TaskSubDO();
            taskSubDO.setId(abstractCheckContext.getTaskSubId());
            taskSubDO.setStatus(TaskSubStatusEnum.FAIL.code());
            taskSubDO.modify();
            taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
            throw new RuntimeException("创建渠道对账异常");
        }finally {
            lock.unlock();
        }
        log.info("渠道对账执行结束，子任务ID:{}", abstractCheckContext.getTaskSubId());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void executeBiz(AbstractCheckContext abstractChectContext){
        List<Long> ids = JSONObject.parseArray(abstractChectContext.getVoucher(),Long.class);
        List<ThirdPartyBillDO> thirdPartyBillDOS = thirdPartyBillMapper.selectByIds(ids);
        if (CollectionUtil.isEmpty(thirdPartyBillDOS)) {
            log.error("batchCheck selectByIds return ChannelAccountingHandler is empty");
            return;
        }
        List<ChannelCheckContext> channelList = thirdPartyBillDOS.stream()
                .filter(bill -> !(BillResource.Alipay.getKey().equals(bill.getBillSource()) && StrUtil.isNotBlank(bill.getBizNo1()) && bill.getBizNo1().endsWith(ALIPAY_CONSUME_DONATE)))
                .map(o -> {
                    ChannelCheckContext channelCheckContext = new ChannelCheckContext();
                    channelCheckContext.setBillId(o.getId());
                    channelCheckContext.setCheckDate(o.getCheckDate());
                    channelCheckContext.setAmount(o.getTradeAmount());
                    channelCheckContext.setCheckResult("");
                    channelCheckContext.setBizNo1(o.getBizNo1());
                    channelCheckContext.setMchId(o.getMchId());
                    channelCheckContext.setBillDate(o.getBillTime());
                    channelCheckContext.setBizType(o.getBizType());
                    channelCheckContext.setPayWay(o.getBillSource());
                    channelCheckContext.setTradeNo(o.getPayNo());
                    channelCheckContext.setTradeType(o.getTradeType());
                    return channelCheckContext;
                }).collect(Collectors.toList());
        /**
         * 查询对应的数据，根据ids
         * 根据支付单号查询出对应的源数据
         * 将两份数据进行对比，成功的落对账结果表，失败的落对账差异表
         */
        if (CollUtil.isEmpty(channelList))
            return;
        batchCheck(channelList);
    }
}
