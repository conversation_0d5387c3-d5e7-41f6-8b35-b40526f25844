package so.dian.huashan.check.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.check.handler.AbstractCheckHandler;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.check.mapper.ResultChannelBillMapper;
import so.dian.huashan.check.mapper.entity.ResultChannelBillDO;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * TaskSubHandler
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 10:00
 */
@Slf4j
@Component(value = CommonConstants.CHANNEL_DIFFERENCE)
public class ChannelDifferenceHandler extends AbstractCheckHandler {

    @Resource
    private ResultChannelBillMapper resultChannelBillMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TaskSubMapper taskSubMapper;

    @Autowired
    private ChannelDifferenceHandler self;

    @Override
    public void process(AbstractCheckContext abstractChectContext) {
        log.info("渠道差异对账开始执行，子任务ID:{}", abstractChectContext.getTaskSubId());
        // 对子任务加锁
        RLock lock = redissonClient.getFairLock(CommonConstants.TASK_SUB + ":" + abstractChectContext.getTaskSubId());
        try {
            if (lock.tryLock(30L, TimeUnit.SECONDS)){
                // 第一步查询出子对账任务，更新为对账中
                TaskSubDO taskSubDO = taskSubMapper.selectByPrimaryKey(abstractChectContext.getTaskSubId());
                if(!taskSubDO.getStatus().equals(TaskSubStatusEnum.INIT.code()) && !taskSubDO.getStatus().equals(TaskSubStatusEnum.FAIL.code())){
                    return;
                }
                taskSubDO.setStatus(TaskSubStatusEnum.PROGRESS.code());
                taskSubDO.modify();
                taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
                self.executeBiz(abstractChectContext);
                taskSubDO.setStatus(TaskSubStatusEnum.SUCCESS.code());
                taskSubDO.modify();
                taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
            }
        } catch (Exception e) {
            log.error("创建渠道对账异常-ChannelAccountingHandler = {}", JSON.toJSONString(abstractChectContext),e);
            TaskSubDO taskSubDO = new TaskSubDO();
            taskSubDO.setId(abstractChectContext.getTaskSubId());
            taskSubDO.setStatus(TaskSubStatusEnum.FAIL.code());
            taskSubDO.modify();
            taskSubMapper.updateByPrimaryKeySelective(taskSubDO);
            throw new RuntimeException("创建渠道对账异常");
        }finally {
            lock.unlock();
        }
        log.info("渠道差异对账执行结束，子任务ID:{}", abstractChectContext.getTaskSubId());
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void executeBiz(AbstractCheckContext abstractChectContext){
        List<Long> ids = JSONObject.parseArray(abstractChectContext.getVoucher(),Long.class);
        List<ResultChannelBillDO> thirdPartyBillDOS = resultChannelBillMapper.selectByIds(ids);
        if (CollectionUtil.isEmpty(thirdPartyBillDOS)) {
            log.error("batchCheck selectByIds return ChannelDifferenceHandler is empty");
            return;
        }
        List<ChannelCheckContext> channelList = thirdPartyBillDOS.stream().map(o -> {
            ChannelCheckContext channelCheckContext = new ChannelCheckContext();
            channelCheckContext.setResultChannelBillId(o.getId());
            channelCheckContext.setBillId(o.getBillId());
            channelCheckContext.setCheckDate(o.getCheckDate());
            channelCheckContext.setAmount(o.getAmount());
            channelCheckContext.setCheckResult("");
            channelCheckContext.setBizNo1(o.getMchOrderNo());
            channelCheckContext.setMchId(o.getMchId());
            channelCheckContext.setBillDate(o.getBillDate());
            channelCheckContext.setBizType(o.getBizType());
            channelCheckContext.setPayWay(o.getPayWay());
            channelCheckContext.setTradeNo(o.getTradeNo());
            channelCheckContext.setTradeType(o.getTradeType());
            return channelCheckContext;
        }).collect(Collectors.toList());
        /**
         * 查询对应的数据，根据ids
         * 根据支付单号查询出对应的源数据
         * 将两份数据进行对比，成功的落对账结果表，失败的落对账差异表
         */
        Set<Long> idList = batchCheck(channelList);

        if(!CollectionUtil.isEmpty(idList)){
            resultChannelBillMapper.updateByIdList(idList);
        }
    }
}
