package so.dian.huashan.check.handler.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.check.manage.ResultBillManage;
import so.dian.huashan.check.manage.ResultChannelBillManage;
import so.dian.huashan.check.manage.bos.IncomeOriginalInfoBO;
import so.dian.huashan.common.enums.BillResource;
import so.dian.huashan.common.mapper.lhc.PaymentMapper;
import so.dian.huashan.common.mapper.lhc.TransOrderMapper;
import so.dian.huashan.common.mapper.lhc.entity.PaymentDO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/10/12 15:54
 * @description:
 */
@Slf4j
@Component
public class DefaultChannelCheckStrategy {

    @Resource
    private PaymentMapper paymentMapper;

    @Resource
    private TransOrderMapper transOrderMapper;

    @Resource
    private ResultBillManage resultBillManage;

    @Resource
    private ResultChannelBillManage resultChannelBillManage;

    public Set<Long> channelCheck(List<ChannelCheckContext> contexts, List<IncomeOriginalInfoBO> incomeOriginalInfos) {
        Map<String, List<IncomeOriginalInfoBO>> bizBillDOGroupMap = new HashMap<>();
        if(CollectionUtil.isNotEmpty(incomeOriginalInfos)) {
            bizBillDOGroupMap = incomeOriginalInfos.stream().collect(Collectors.groupingBy(bizBillDO -> {
                String payNo = bizBillDO.getPayNo();
                String tradeType = bizBillDO.getTradeType();
                Integer payChannel = bizBillDO.getPayWay();
                return StringUtils.joinWith(StrUtil.COLON, payNo, tradeType, payChannel);
            }));
        }
        return doCheckList(contexts, bizBillDOGroupMap);
    }

    private Set<Long> doCheckList(List<ChannelCheckContext> list, Map<String, List<IncomeOriginalInfoBO>> bizBillDOGroupMap) {
        Set<Long> idList  = new HashSet<>();
        List<ChannelCheckContext> resultList = new ArrayList<>();
        List<ChannelCheckContext> resultChannelList = new ArrayList<>();
        for (ChannelCheckContext channelBill : list) {
            String key = getKey(channelBill);
            Long id = check(resultList, resultChannelList, channelBill, bizBillDOGroupMap.get(key));
            if(Objects.nonNull(id)){
                idList.add(id);
            }
        }

        resultBillManage.batchAddOrModify(resultList);
        resultChannelBillManage.batchAddOrModify(resultChannelList);
        return idList;
    }

    private Long check(List<ChannelCheckContext> resultList, List<ChannelCheckContext> resultChannelList, ChannelCheckContext channelBill, List<IncomeOriginalInfoBO> incomeOriginalBillDOS){
        if(CollUtil.isNotEmpty(incomeOriginalBillDOS)){
            //平账标志
            boolean flag = false;
            Integer tradeAmount = 0;
            for (IncomeOriginalInfoBO incomeOriginalBillDO : incomeOriginalBillDOS) {
                channelBill.setDianPayNo(incomeOriginalBillDO.getDianPayNo());
                channelBill.setChannelTradeNo(incomeOriginalBillDO.getChannelTradeNo());
                if(Objects.equals(incomeOriginalBillDO.getTradeAmount(),channelBill.getAmount())){
                    flag = true;
                    break;
                }
                tradeAmount = incomeOriginalBillDO.getTradeAmount() + tradeAmount;
                if(Objects.equals(tradeAmount,channelBill.getAmount())){
                    flag = true;
                    break;
                }
            }
            if(!flag){
                // 入渠道差异对账表
                channelBill.setCheckResult("渠道对账差异落库");
                resultChannelList.add(channelBill);
            }else{
                // 入渠道对账结果表
                channelBill.setCheckResult("渠道对账正确落库");
                resultList.add(channelBill);
                return channelBill.getResultChannelBillId();
            }
        }else {
            /**
             * 支付宝测试环境和产线环境用的是同一个账号
             * 需要排除掉测试环境的数据
             */
            channelBill.setDianPayNo(Optional.ofNullable(CollUtil.getFirst(incomeOriginalBillDOS)).map(IncomeOriginalInfoBO::getDianPayNo).orElse(null));
            channelBill.setChannelTradeNo(Optional.ofNullable(CollUtil.getFirst(incomeOriginalBillDOS)).map(IncomeOriginalInfoBO::getChannelTradeNo).orElse(null));
            if(Objects.equals(BillResource.Alipay.getKey(),channelBill.getPayWay())){
                List<PaymentDO> paymentDOs = paymentMapper.selectAlipayByPayNo(channelBill.getTradeNo());
                if(CollectionUtil.isNotEmpty(paymentDOs)){
                    log.warn("该笔渠道订单是测试环境支付数据,不进行对账 支付单号 = {}",channelBill.getTradeNo());
                    String remark = "该笔渠道订单是测试环境支付或退款数据";
                    // 直接落对账结果表
                    channelBill.setCheckResult(remark);
                    resultList.add(channelBill);
                    return channelBill.getResultChannelBillId();
                }
                String payNo = transOrderMapper.getByOutPayNo(channelBill.getTradeNo());
                if(Objects.nonNull(payNo)){
                    log.warn("该笔渠道订单是测试环境企业打款数据,不进行对账 支付单号 = {}",channelBill.getTradeNo());
                    String remark = "该笔渠道订单是测试环境企业打款数据";
                    // 直接落对账结果表
                    channelBill.setCheckResult(remark);
                    resultList.add(channelBill);
                    return channelBill.getResultChannelBillId();
                }
            }
            resultChannelList.add(channelBill);
        }
        return null;
    }

    private String getKey(ChannelCheckContext channelBill){
        if(Objects.isNull(channelBill)){
            return null;
        }
        String payNo =  channelBill.getTradeNo();
        String tradeType = channelBill.getTradeType();
        Integer payChannel = channelBill.getPayWay();
        return StringUtils.joinWith(StrUtil.COLON, payNo, tradeType, payChannel);
    }
}
