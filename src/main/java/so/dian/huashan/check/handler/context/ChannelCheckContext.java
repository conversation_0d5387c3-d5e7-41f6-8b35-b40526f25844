package so.dian.huashan.check.handler.context;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.Date;

/**
 * ChannelCheckContext
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/24 16:13
 */
@Data
public class ChannelCheckContext {
    /**
     * 渠道数据id
     */
    private Long billId;

    private Long resultChannelBillId;

    /**
     * 核对日
     */
    private Integer checkDate;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 交易时间
     */
    private Date billDate;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 交易金额，单位：分
     */
    private Integer amount;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 支付方式（1-支付宝，3-微信，4-苏宁，5-QQ，6-银联，7-会员，8-优惠券，9-积分，10-钱包或押金，11-宝付，12-微美）
     */
    private Integer payWay;

    /**
     * 核对结果描述
     */
    private String checkResult;

    private String bizNo1;

    private String mchId;

    /**
     * 小电支付系统流水号，分别对应payment#trade_no和refund_order#refund_order_no
     */
    private String dianPayNo;

    /**
     * 第三方账单流水号
     */
    private String channelTradeNo;
    
    public String getCheckKey() {
        return StrUtil.join(StrUtil.COLON, tradeNo, tradeType, bizNo1);
    }
}
