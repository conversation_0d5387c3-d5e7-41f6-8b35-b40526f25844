package so.dian.huashan.check.handler.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.check.manage.ResultBillManage;
import so.dian.huashan.check.manage.bos.IncomeOriginalInfoBO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/10/12 16:00
 * @description:
 */
@Slf4j
@Component
public class ChannelRefundCheckStrategy {

    @Resource
    private ResultBillManage resultBillManage;

    @Resource
    private DefaultChannelCheckStrategy defaultChannelCheckStrategy;

    public Set<Long> refundCheck(List<ChannelCheckContext> contexts, List<IncomeOriginalInfoBO> incomeOriginalInfos) {

        Map<String, List<IncomeOriginalInfoBO>> keyAndOriMap = incomeOriginalInfos.stream().collect(Collectors.groupingBy(IncomeOriginalInfoBO::getCheckKey));
        List<ChannelCheckContext> resultList = Lists.newArrayList();
        List<ChannelCheckContext> resultChannelList = Lists.newArrayList();
        for (ChannelCheckContext context : contexts) {
            List<IncomeOriginalInfoBO> originalInfoBOS = keyAndOriMap.get(context.getCheckKey());
            IncomeOriginalInfoBO originalInfoBO = CollUtil.getFirst(originalInfoBOS);
            context.setDianPayNo(Optional.ofNullable(originalInfoBO).map(IncomeOriginalInfoBO::getDianPayNo).orElse(null));
            context.setChannelTradeNo(Optional.ofNullable(originalInfoBO).map(IncomeOriginalInfoBO::getChannelTradeNo).orElse(null));
            if (ObjectUtil.isNotNull(originalInfoBO) && NumberUtil.equals(context.getAmount(), originalInfoBO.getTradeAmount())) {
                context.setCheckResult("渠道对账正确落库");
                resultList.add(context);
            }else {
                context.setCheckResult("渠道对账差异落库");
                resultChannelList.add(context);
            }
        }

        resultBillManage.batchAddOrModify(resultList);

        Set<Long> ids = resultList.stream().map(ChannelCheckContext::getResultChannelBillId).filter(Objects::nonNull).collect(Collectors.toSet());

        // 新的退款对账场景下，用新的逻辑对完账以后还有差异，就用老逻辑再对一遍，11月份以后此处逻辑可以替换成直接保存差异数据
        if (CollUtil.isNotEmpty(resultChannelList)) {
            log.info("渠道对账，退款场景下开始使用老逻辑进行对账");
            Set<Long> resultIds = defaultChannelCheckStrategy.channelCheck(resultChannelList, incomeOriginalInfos);
            if (CollUtil.isNotEmpty(resultIds))
                ids.addAll(resultIds);
        }
        return ids;
    }
}
