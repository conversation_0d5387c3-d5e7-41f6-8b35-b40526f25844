package so.dian.huashan.check.handler;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.check.handler.context.ChannelCheckContext;
import so.dian.huashan.check.handler.strategy.ChannelRefundCheckStrategy;
import so.dian.huashan.check.handler.strategy.DefaultChannelCheckStrategy;
import so.dian.huashan.check.manage.bos.IncomeOriginalInfoBO;
import so.dian.huashan.check.manage.convert.IncomeOriginalInfoConvert;
import so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * AbstractCheckHandler
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public abstract class AbstractCheckHandler {

   @Resource
   private IncomeOriginalInfoMapper incomeOriginalInfoMapper;

   @Resource
   private DefaultChannelCheckStrategy defaultChannelCheckStrategy;

   @Resource
   private ChannelRefundCheckStrategy channelRefundCheckStrategy;

   public abstract void process(AbstractCheckContext abstractCheckContext);

   public Set<Long> batchCheck(List<ChannelCheckContext> contexts){
      List<String> payNos = contexts.stream().map(ChannelCheckContext::getTradeNo).collect(Collectors.toList());
      List<IncomeOriginalInfoDO> bizBillDOS = incomeOriginalInfoMapper.selectBatchByPayNos(payNos);

      List<IncomeOriginalInfoBO> bizInBills = Lists.newArrayList();
      List<IncomeOriginalInfoBO> bizOutBills = Lists.newArrayList();
      IncomeOriginalInfoConvert convert = new IncomeOriginalInfoConvert();
      for (IncomeOriginalInfoDO bizBillDO : bizBillDOS) {
         IncomeOriginalInfoBO bizBillBO = convert.toBO(bizBillDO);
         if ("in".equals(bizBillBO.getTradeType())) {
            bizInBills.add(bizBillBO);
         }else {
            bizOutBills.add(bizBillBO);
         }
      }

      List<ChannelCheckContext> channelInBill = Lists.newArrayList();
      List<ChannelCheckContext> channelOutBill = Lists.newArrayList();
      for (ChannelCheckContext context : contexts) {
         if ("in".equals(context.getTradeType())) {
            channelInBill.add(context);
         }else {
            channelOutBill.add(context);
         }
      }

      Set<Long> needRemoveDiffBillIds = Sets.newHashSet();
      Set<Long> channelCheck = defaultChannelCheckStrategy.channelCheck(channelInBill, bizInBills);
      Set<Long> refundCheck = channelRefundCheckStrategy.refundCheck(channelOutBill, bizOutBills);

      if (CollUtil.isNotEmpty(channelCheck))
         needRemoveDiffBillIds.addAll(channelCheck);
      if (CollUtil.isNotEmpty(refundCheck))
         needRemoveDiffBillIds.addAll(refundCheck);
      return needRemoveDiffBillIds;
   }


}
