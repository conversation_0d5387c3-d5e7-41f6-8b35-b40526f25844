package so.dian.huashan.check.job.settle;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.service.settle.BillSettleCheckService;

import java.util.concurrent.atomic.AtomicBoolean;

import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/12/20 14:24
 * @description:
 */
@Slf4j
@Component
public class SettleDeletedDataJob {

    @Autowired
    private BillSettleCheckService settleCheckService;

    private final AtomicBoolean isRun = new AtomicBoolean(Boolean.FALSE);

    @XxlJob("settleDeletedDataJob")
    public ReturnT<String> execute(String param) {
        boolean compareAndSet = isRun.compareAndSet(Boolean.FALSE, Boolean.TRUE);
        try {
            if (compareAndSet) {
                settleCheckService.dataDelete();
            }
        }catch (Exception e) {
            log.warn(BILLING_SETTLE, "清结算数据删除执行异常", e);
            throw e;
        }finally {
            if (compareAndSet) {
                isRun.compareAndSet(Boolean.TRUE, Boolean.FALSE);
            }
        }

        return ReturnT.SUCCESS;
    }
}
