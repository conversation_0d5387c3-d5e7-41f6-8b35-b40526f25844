package so.dian.huashan.check.job.credit.shipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huangshan.core.executor.TaskExecuteHelper;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.core.executor.val.TaskOutResult;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.check.service.credit.shipment.ShipmentOrderService;
import so.dian.huashan.framework.task.ShardTaskExecuteService;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskInstallService;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

@Slf4j
@Component
public class ShipmentOrderCheckJob {
    public final static String CHECK_DATE_TIME = "checkDateTime";

    public final static String CREDIT_JOB_NAME = "shipmentOrderCheckJob";
    @Autowired
    private ShipmentOrderService shipmentOrderService;

    private ShardTaskRetryService taskRetryService;
    @Resource
    private TaskExecuteHelper taskExecuteHelper;
    @PostConstruct
    public void init() {
        ShardTaskProperties properties = new ShardTaskProperties(CREDIT_JOB_NAME);
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }
    @HSTaskScheduled(taskActuator = "shipmentOrderInstallJob")
    public void shipmentOrderInstall(TaskBaseParam param) {
        log.info("shipmentOrderInstallJob get param:{}",param);
        try {
            InstallJobParam jobParam = new InstallJobParam(param.getBatchNo(), param.getTaskCode());
            Map<String, Object> paramMap = JSON.parseObject(param.getUserParam(), new TypeReference<Map<String, Object>>() {
            });
            if (CollUtil.isNotEmpty(paramMap))
                jobParam.setExtParam(paramMap);
            shipmentOrderService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称::{}", CREDIT_JOB_NAME, e);
            throw e;
        }
    }
    @HSTaskScheduled(taskActuator = "shipmentOrderExecuteJob")
    public void shipmentOrderExecute(TaskBaseParam param) {
        log.info("shipmentOrderExecuteJob get param:{}",param);
        List<TaskOutResult> preTaskResults = taskExecuteHelper.listPreTaskResults(param.getBatchNo(), param.getTaskCode());
        TaskOutResult outResult = CollUtil.getFirst(preTaskResults);
        if (Objects.isNull(outResult) || StrUtil.isBlank(outResult.getOutParam()))
            throw BizException.create(SHARD_TASK_FAIL);

        ExecuteJobParam jobParam = new ExecuteJobParam(param.getBatchNo(), param.getTaskCode());
        ShardTaskResult shardTaskResult = JSON.parseObject(outResult.getOutParam(), ShardTaskResult.class);
        jobParam.setTaskMasterId(shardTaskResult.getTaskMasterId());
        shipmentOrderService.execute(jobParam);
    }


    /**
     * 增量对账分片重试任务
     */
    @XxlJob("shipmentOrderRetryJob")
    public ReturnT<String> retryExecute(String param) {
        log.info("shipmentOrderRetryJob get param:{}",param);
        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }
        taskRetryService.retry(jobParam, shipmentOrderService::check);
        return ReturnT.SUCCESS;
    }
}
