package so.dian.huashan.check.job.credit.shipment;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huangshan.infrastructure.framework.HSTaskGroup;

@Slf4j
@Component
public class ShipmentCheckJob {

    @XxlJob("shipmentCheckJob")
    @HSTaskGroup(code = "shipmentCheckJob")
    public ReturnT<String> startIncome(String param) {
        return ReturnT.SUCCESS;
    }
}
