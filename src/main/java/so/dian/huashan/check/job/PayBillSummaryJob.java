package so.dian.huashan.check.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.check.emuns.payBill.PayBillDiffStatus;
import so.dian.huashan.model.dto.CheckOffSumAndCountDTO;
import so.dian.huashan.model.param.CheckDiffParam;
import so.dian.huashan.check.job.dto.PayBillCheckDiffGroupDTO;
import so.dian.huashan.check.job.param.PayBillCheckDiffGroupParam;
import so.dian.huashan.check.mapper.PayBillCheckDiffMapper;
import so.dian.huashan.check.mapper.PayBillSummaryMapper;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.check.mapper.entity.PayBillSummaryDO;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.model.param.CheckOffSumAndCountParam;
import so.dian.huashan.model.param.SummaryParam;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class PayBillSummaryJob {

    @Resource
    private PayBillCheckDiffMapper payBillCheckDiffMapper;

    @Resource
    private PayBillSummaryMapper payBillSummaryMapper;

    @XxlJob("PayBillSummaryJob")
    public ReturnT<String> execute(String param) {
        log.info("PayBillSummaryJob start param:{}", param);
        summary(null, null);
        return ReturnT.SUCCESS;
    }

    public void summary(Long startTime, Long endTime) {
        if (Objects.isNull(startTime)) {
            startTime =  DateUtil.getStartDate(new Date()).getTime();
        }
        if (Objects.isNull(endTime)) {
            endTime = DateUtil.getEndDate(new Date()).getTime();
        }
        PayBillCheckDiffGroupParam payBillCheckDiffGroupParam = getPayBillCheckDiffGroupParam(startTime, endTime);
        List<PayBillCheckDiffGroupDTO> groupDTOS = payBillCheckDiffMapper.selectCheckDiffGroupByParam(payBillCheckDiffGroupParam);
        if (CollectionUtils.isEmpty(groupDTOS)) {
            return;
        }

        log.info("PayBillSummaryJob get groupDTOList.size:{}", groupDTOS.size());
        for (PayBillCheckDiffGroupDTO groupDTO : groupDTOS) {

            if (Objects.isNull(groupDTO) || Objects.isNull(groupDTO.getPaySubjectId()) || Objects.isNull(groupDTO.getSource()) || Objects.isNull(groupDTO.getTradeTime())) {
                log.error("来源或者交易时间或者主体id为空 groupDTO:{}", groupDTO);
                continue;
            }
            // 获取差值
            CheckOffSumAndCountDTO checkOffSumAndCountDTO = payBillCheckDiffMapper.checkOffDiffAmountAndCount(CheckOffSumAndCountParam.builder().paySubjectId(groupDTO.getPaySubjectId()).source(groupDTO.getSource()).tradeStartTime(DateUtil.getStartDate(groupDTO.getTradeTime())).tradeEndTime(DateUtil.getEndDate(groupDTO.getTradeTime())).build());
            if (Objects.isNull(checkOffSumAndCountDTO)||Objects.isNull(checkOffSumAndCountDTO.getDiffAmount())) {
                List<PayBillSummaryDO> payBillSummaryDOList = payBillSummaryMapper.selectByParam(SummaryParam.builder().paySubjectId(groupDTO.getPaySubjectId()).source(groupDTO.getSource()).naturalDate(DateUtil.parseDateYyyyMMdd2IntThrowException(groupDTO.getTradeTime())).build());
                if (CollectionUtils.isEmpty(payBillSummaryDOList)){
                    continue;
                }
                PayBillSummaryDO payBillSummaryDO = payBillSummaryDOList.get(0);
                payBillSummaryDO.setPayDiffAmount(0L);
                payBillSummaryDO.setPayDiffCount(0);
                payBillSummaryDO.setGmtUpdate(System.currentTimeMillis());
                payBillSummaryMapper.updateByPrimaryKey(payBillSummaryDO);
                continue;
            }

            // 获取汇总
            List<PayBillSummaryDO> payBillSummaryDOList = payBillSummaryMapper.selectByParam(SummaryParam.builder().paySubjectId(groupDTO.getPaySubjectId()).source(groupDTO.getSource()).naturalDate(DateUtil.parseDateYyyyMMdd2IntThrowException(groupDTO.getTradeTime())).build());
            if (CollectionUtils.isEmpty(payBillSummaryDOList)) {
                PayBillCheckDiffDO payBillCheckDiffDO = payBillCheckDiffMapper.getOne(CheckDiffParam.builder().paySubjectId(groupDTO.getPaySubjectId()).build());
                if (Objects.isNull(payBillCheckDiffDO)) {
                    log.error("主体不能为空");
                    continue;
                }
                PayBillSummaryDO payBillSummaryDO = new PayBillSummaryDO();
                payBillSummaryDO.setPaySubjectId(groupDTO.getPaySubjectId());
                payBillSummaryDO.setPaySubjectType(payBillCheckDiffDO.getPaySubjectType());
                payBillSummaryDO.setNaturalDate(DateUtil.parseDateYyyyMMdd2Int(groupDTO.getTradeTime()));
                payBillSummaryDO.setSource(groupDTO.getSource());
                payBillSummaryDO.setGmtCreate(System.currentTimeMillis());
                payBillSummaryDO.setGmtUpdate(System.currentTimeMillis());
                payBillSummaryDO.setPayDiffAmount(checkOffSumAndCountDTO.getDiffAmount());
                payBillSummaryDO.setPayDiffCount(checkOffSumAndCountDTO.getCount());
                payBillSummaryMapper.insertSelective(payBillSummaryDO);
                continue;
            }

            PayBillSummaryDO payBillSummaryDO = payBillSummaryDOList.get(0);
            payBillSummaryDO.setPayDiffAmount(checkOffSumAndCountDTO.getDiffAmount());
            payBillSummaryDO.setPayDiffCount(checkOffSumAndCountDTO.getCount());
            payBillSummaryDO.setGmtUpdate(System.currentTimeMillis());
            payBillSummaryMapper.updateByPrimaryKey(payBillSummaryDO);
        }
    }

    private PayBillCheckDiffGroupParam getPayBillCheckDiffGroupParam(Long startTime, Long endTime) {
        PayBillCheckDiffGroupParam payBillCheckDiffGroupParam = new PayBillCheckDiffGroupParam();
        payBillCheckDiffGroupParam.setStatus(PayBillDiffStatus.NOT_ACCOUNTS.getCode());
        payBillCheckDiffGroupParam.setUpdateStartTime(startTime);
        payBillCheckDiffGroupParam.setUpdateEndTime(endTime);
        return payBillCheckDiffGroupParam;
    }
}
