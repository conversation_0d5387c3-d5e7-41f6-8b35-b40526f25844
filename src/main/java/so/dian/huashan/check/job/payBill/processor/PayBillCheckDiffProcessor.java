package so.dian.huashan.check.job.payBill.processor;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.job.income.ChannelIncomeCheckJob;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.processor.ShardInstallProcessor;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;
import static so.dian.huashan.check.job.payBill.PayBillCheckDiffJob.PAY_BILL_DIFF_JOB_NAME;
import static so.dian.huashan.check.job.payBill.PayBillCheckDiffJob.CHECK_DATE_TIME;

/**
 * @author: miaoshuai
 * @create: 2024/01/04 09:55
 * @description:
 */
@Slf4j
@Component
public class PayBillCheckDiffProcessor extends ShardInstallProcessor {

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private TaskRegistryMapper taskRegistryMapper;

    @Override
    public String jobName() {
        return PAY_BILL_DIFF_JOB_NAME;
    }

    @Override
    public boolean preProcess(InstallJobParam param) {
        String checkDate = param.getExtParam(CHECK_DATE_TIME);
        if (Objects.isNull(checkDate)) {
            log.warn(CHECK_DATE_TIME, "增量对账付款单前置校验，checkDate 不能为空");
            return Boolean.FALSE;
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        DateTime date = LocalDateUtils.offsetDay(checkDateTime, 1);
        String checkDateStr = LocalDateUtils.format(date, PURE_DATE_PATTERN);
        // 校验payBillThirdJob是否执行完成
        TaskRegistryDO taskRegistryDO = taskRegistryMapper.selectByName("payBillThirdJob");
        List<TaskMasterDO> taskMasterDOList = taskMasterMapper.selectByTaskRegistryIdInvokeDate(Integer.valueOf(checkDateStr), taskRegistryDO.getId());
        if(CollectionUtils.isEmpty(taskMasterDOList)){
            log.warn(CHECK_DATE_TIME, "增量对账付款单前置校验，前置任务payBillThirdJob没有执行完成");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
