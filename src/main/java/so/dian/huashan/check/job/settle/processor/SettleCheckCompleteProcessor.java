package so.dian.huashan.check.job.settle.processor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.check.job.settle.SettleCheckJob;
import so.dian.huashan.check.mapper.BillSettleCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/12/19 16:00
 * @description:
 */
@Slf4j
@Component
public class SettleCheckCompleteProcessor extends BaseCheckCompleteProcessor {

    private final static String JOB_NAME_CN = "订单计费明细对账";

    @Resource
    private LushanFacade lushanFacade;

    @Autowired
    private BillSettleCheckDiffMapper settleCheckDiffMapper;

    @Value("${huashan.biz.settle.dingding-code}")
    private String dingdingCode;

    @Autowired
    private TaskMasterMapper taskMasterMapper;

    @Override
    public String jobName() {
        return SettleCheckJob.SETTLE_CHECK_JOB_NAME;
    }

    @Override
    public void process(ShardCompleteMessage completeMessage) {

        TaskMasterDO taskMasterDO = taskMasterMapper.selectByPrimaryKey(completeMessage.getTaskMasterId());
        int countDiff = settleCheckDiffMapper.countDiff(taskMasterDO.getStartTime().getTime(), taskMasterDO.getFinishTime().getTime());
        if (countDiff <= 0) {
            log.info(BILLING_SETTLE, "清结算本次对账结束，没有差异，主任务ID:{}", completeMessage.getTaskMasterId());
            return;
        }

        List<BillSettleCheckDiffDO> checkDiffDOS = settleCheckDiffMapper.selectRecently(PageRequest.of(1, 11), taskMasterDO.getStartTime().getTime(), taskMasterDO.getFinishTime().getTime());
        List<String> orderNos = checkDiffDOS.stream().map(BillSettleCheckDiffDO::getOrderNo).collect(Collectors.toList());
        sendNotify(JOB_NAME_CN, completeMessage, orderNos, countDiff);
    }

    @Override
    protected String getDingdingCode() {
        return dingdingCode;
    }
}
