package so.dian.huashan.check.job.payBill;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.service.payBill.PayBillCheckService;
import so.dian.huashan.framework.task.ShardTaskExecuteService;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskInstallService;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.ShardRetryJobParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_MS_PATTERN;
import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:32
 * @description: 渠道收入账任务
 */
@Slf4j
@Component
public class PayBillCheckJob {

    public final static String CHECK_DATE_TIME = "checkDateTime";

    public final static String PAY_BILL_JOB_NAME = "payBillJob";

    private ShardTaskInstallService installTaskService;

    private ShardTaskExecuteService executeTaskService;

    private ShardTaskRetryService taskRetryService;

    @Resource
    private PayBillCheckService payBillCheckService;

    @PostConstruct
    public void init() {
        ShardTaskProperties properties = new ShardTaskProperties(PAY_BILL_JOB_NAME);
        installTaskService = ShardTaskFactory.createInstallService(properties);
        executeTaskService = ShardTaskFactory.createExecuteService(properties);
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }

    /**
     * 增量对账分片生成任务
     */
    @XxlJob("payBillInstallJob")
    public ReturnT<String> payBillInstall(String param) {
        InstallJobParam jobParam = JSON.parseObject(param, InstallJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new InstallJobParam();
            jobParam.putExtParam(CHECK_DATE_TIME, LocalDateUtils.format(LocalDateUtils.yesterday(),NORM_DATETIME_PATTERN));
        }

        try {
            Long taskMasterId = installTaskService.installShardTask(jobParam, payBillCheckService::listIds);
            return Objects.nonNull(taskMasterId) ? ReturnT.SUCCESS : ReturnT.FAIL;
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称:{}", PAY_BILL_JOB_NAME, e);
            throw e;
        }

    }

    /**
     * 增量对账分片执行任务
     */
    @XxlJob("payBillExecuteJob")
    public ReturnT<String> payBillExecute(String param) {

        ExecuteJobParam jobParam = JSON.parseObject(param, ExecuteJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ExecuteJobParam();
        }

        try {
            executeTaskService.executeShardTask(jobParam, payBillCheckService::check);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "分片执行异常, 任务名称:{}", PAY_BILL_JOB_NAME, e);
            throw e;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 增量对账分片重试任务
     */
    @XxlJob("payBillRetryJob")
    public ReturnT<String> retryPayBillExecute(String param) {

        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }
        taskRetryService.retry(jobParam, payBillCheckService::check);
        return ReturnT.SUCCESS;
    }
}
