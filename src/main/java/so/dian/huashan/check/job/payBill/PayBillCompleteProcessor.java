package so.dian.huashan.check.job.payBill;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.enums.MoneyUnit;
import so.dian.huashan.check.emuns.payBill.PayBillReason;
import so.dian.huashan.check.mapper.PayBillCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.processor.ShardCompleteProcessor;
import so.dian.huashan.task.mapper.TaskMasterMapper;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_PAY;

/**
 * @author: miaoshuai
 * @create: 2023/12/19 16:00
 * @description:
 */
@Slf4j
@Component
public class PayBillCompleteProcessor implements ShardCompleteProcessor {

    @Resource
    private LushanFacade lushanFacade;

    @Resource
    private PayBillCheckDiffMapper payBillCheckDiffMapper;

    @Value("${huashan.biz.dingding.code}")
    private String dingdingCode;

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Value("${pay.bill.domain}")
    private String domain;

    private static final String DETAIL_URL = "/fis/finance/reconciliation/paymentDetailNew";


    @Override
    public String jobName() {
        return PayBillCheckDiffJob.PAY_BILL_DIFF_JOB_NAME;
    }

    @Override
    public void process(ShardCompleteMessage completeMessage) {
        log.info("PayBillCompleteProcessor get completeMessage:{}", JSONObject.toJSONString(completeMessage));
        List<PayBillCheckDiffDO> list = payBillCheckDiffMapper.countDiff();
        if (list.size() <= 0) {
            log.info(BILLING_PAY, "付款对账本次对账结束，没有差异，主任务ID:{}", completeMessage.getTaskMasterId());
            String content = "  \n  &nbsp; 无差异";
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("内容", content);
            lushanFacade.reportJob(jsonObject, completeMessage.getJobName(), dingdingCode);
            return;
        }

        // 中喜：付款单有支付通道无

        StringBuilder content = new StringBuilder("  \n  &nbsp;<font color = red>付款单有支付通道无：</font>");

        List<PayBillCheckDiffDO> notXYKPayBillList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_THIRD.getCode()) && o.getSource().equals(0)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notXYKPayBillList)){
            content.append("  \n  &nbsp;僖游客" + toAppend(notXYKPayBillList.stream().map(PayBillCheckDiffDO::getTradeNo).collect(Collectors.toList())));
        }

        List<PayBillCheckDiffDO> notGMPayBillList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_THIRD.getCode()) && o.getSource().equals(1)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notGMPayBillList)){
            content.append("  \n  &nbsp;工猫" + toAppend(notGMPayBillList.stream().map(PayBillCheckDiffDO::getTradeNo).collect(Collectors.toList())));
        }

        List<PayBillCheckDiffDO> notZSPayBillList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_THIRD.getCode()) && o.getSource().equals(2)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notZSPayBillList)){
            content.append("  \n  &nbsp;招商" + toAppend(notZSPayBillList.stream().map(PayBillCheckDiffDO::getTradeNo).collect(Collectors.toList())));
        }

        content.append("  \n  &nbsp;<font color = red>支付通道有付款单无：</font>");

        List<PayBillCheckDiffDO> notXYKPayBillThirdList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_BIZ.getCode()) && o.getSource().equals(0)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notXYKPayBillThirdList)){
            content.append("  \n  &nbsp;僖游客" + toAppend(notXYKPayBillThirdList.stream().map(PayBillCheckDiffDO::getChannelTradeNo).collect(Collectors.toList())));
        }

        List<PayBillCheckDiffDO> notGMPayBillThirdList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_BIZ.getCode()) && o.getSource().equals(1)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notGMPayBillThirdList)){
            content.append("  \n  &nbsp;工猫" + toAppend(notGMPayBillThirdList.stream().map(PayBillCheckDiffDO::getChannelTradeNo).collect(Collectors.toList())));
        }

        List<PayBillCheckDiffDO> notZSPayBillThirdList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_BIZ.getCode()) && o.getSource().equals(2)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notZSPayBillThirdList)){
            content.append("  \n  &nbsp;招商" + toAppend(notZSPayBillThirdList.stream().map(PayBillCheckDiffDO::getChannelTradeNo).collect(Collectors.toList())));
        }

        content.append("  \n  &nbsp;<font color = red>退票：</font>");
        List<PayBillCheckDiffDO> notZSPayBillThirdTPList = list.stream().filter(o -> o.getReason().equals(PayBillReason.REFUND_TICKET.getCode()) && o.getSource().equals(2)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notZSPayBillThirdTPList)){
            content.append("  \n  &nbsp;招商" + toAppendCode(notZSPayBillThirdTPList));
        }
        content.append("  \n  &nbsp;<font color = red>金额不一致：</font>");

        List<PayBillCheckDiffDO> notXYKList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_AMOUNT.getCode()) && o.getSource().equals(0)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notXYKList)){
            content.append("  \n  &nbsp;僖游客" + toAppendCode(notXYKList));
        }

        List<PayBillCheckDiffDO> notGMList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_AMOUNT.getCode()) && o.getSource().equals(1)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notGMList)){
            content.append("  \n  &nbsp;工猫" + toAppendCode(notGMList));
        }

        List<PayBillCheckDiffDO> notZSList = list.stream().filter(o -> o.getReason().equals(PayBillReason.NOT_AMOUNT.getCode()) && o.getSource().equals(2)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notZSList)){
            content.append("  \n  &nbsp;招商" + toAppendCode(notZSList));
        }

        content.append("  \n  &nbsp;<font color = red>差异条数和差异金额：</font>");
        content.append("  \n  &nbsp;" + list.size() + "||" + MoneyUnit.toYuan(list.stream().mapToLong(o-> Math.abs(o.getTradeAmount() - o.getThirdTradeAmount())).sum(),MoneyUnit.YUAN) + "元");
        content.append("点击[查看明细]({})");
        String fullUrl = domain + DETAIL_URL;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("内容", StrUtil.format(content.toString(), fullUrl));
        lushanFacade.reportJob(jsonObject,"每日通道付款核对结果通知", dingdingCode);
        log.info(BILLING_PAY, "发送付款任务执行完成结果，{}", jsonObject.toJSONString(content));
    }

    private String toAppendCode(List<PayBillCheckDiffDO> list){
        int i = 0;
        String json = "";
        for(PayBillCheckDiffDO payBillCheckDiffDO : list){
            json = json + "||" + payBillCheckDiffDO.getTradeNo() + "||" + payBillCheckDiffDO.getChannelTradeNo();
            i++;
            if(i>10){
                break;
            }
        }
        return json;
    }

    private String toAppend(List<String> list){
        int i = 0;
        String json = "";
        for(String num : list){
            json = json + "||" + num;
            i++;
            if(i>10){
                break;
            }
        }
        return json;
    }
}
