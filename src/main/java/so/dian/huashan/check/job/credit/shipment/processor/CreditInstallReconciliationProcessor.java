package so.dian.huashan.check.job.credit.shipment.processor;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.job.credit.shipment.ShipmentDiffCheckJob;
import so.dian.huashan.check.mapper.CreditCheckDiffMapper;
import so.dian.huashan.common.enums.CreditBizTypeEnum;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.processor.ShardCompleteProcessor;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Component
public class CreditInstallReconciliationProcessor implements ShardCompleteProcessor {
    @Autowired
    private LushanFacade lushanFacade;
    @Autowired
    private CreditCheckDiffMapper creditCheckDiffMapper;
    @Value("${huashan.biz.credit.code}")
    private String funcCode;

    @Override
    public void process(ShardCompleteMessage completeMessage) {
        try {
            log.info("creditInstallReconciliationJob get completeMessage:{}", completeMessage);
            Date current = DateUtil.getNow();
            Long end = DateUtil.endOfMonth(current).getTime();
            Long startBill = DateUtil.beginOfMonth(current).getTime();
            log.info("creditInstallReconciliationJob get startBill:{},end:{}", startBill, end);
            Long tradeCount = creditCheckDiffMapper.totalDiffStatistics(startBill, end, CreditBizTypeEnum.TRADE.getCode());
            Long orderCount = creditCheckDiffMapper.totalDiffStatistics(startBill, end, CreditBizTypeEnum.ORDER.getCode());
            Long transferCount = creditCheckDiffMapper.totalDiffStatistics(startBill, end, CreditBizTypeEnum.TRANSFER_ORDER.getCode());
            if (Objects.isNull(tradeCount)) {
                tradeCount = 0L;
            }
            if (Objects.isNull(orderCount)) {
                orderCount = 0L;
            }
            if (Objects.isNull(transferCount)) {
                transferCount = 0L;
            }
            StringBuilder content = new StringBuilder("  \n  &nbsp;1. 账单日：");
            content.append(LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN));
            content.append("  \n  &nbsp;2. 本月对账合计差异总条数：").append(tradeCount + orderCount + transferCount);
            content.append("  \n  &nbsp;3. 本月交易对账差异总条数：").append(tradeCount);
            content.append("  \n  &nbsp;4. 本月订单对账差异总条数：").append(orderCount);
            content.append("  \n  &nbsp;5. 本月设备转让单对账差异总条数：").append(transferCount);
            if (StrUtil.isBlank(content)) {
                log.warn("内容不存在");
                return;
            }
            JSONObject jsonObject = new JSONObject(true);
            jsonObject.put("标题", "授信分期单据对账结果通知");
            jsonObject.put("内容", content);
            log.info("授信分期单据对账，内容:{}", jsonObject.toJSONString());

            lushanFacade.reportJob(jsonObject, "授信分期单据对账结果通知", funcCode);
            log.info("授信分期单据对账结果通知成功");
        } catch (Exception e) {
            log.error("授信分期单据对账失败 completeMessage:{}", completeMessage, e);
        }
    }


    @Override
    public String jobName() {
        return ShipmentDiffCheckJob.CREDIT_JOB_NAME;
    }
}
