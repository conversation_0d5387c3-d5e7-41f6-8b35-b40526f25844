package so.dian.huashan.check.job.income.processor;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.manage.ResultChannelBillManage;
import so.dian.huashan.check.manage.dto.ChannelDiffStatisticsDTO;
import so.dian.huashan.common.enums.TradeType;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.common.val.AmountVal;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.processor.ShardCompleteProcessor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static so.dian.huashan.check.job.income.ChannelIncomeDiffCheckJob.INCOME_CHECK_DIFF_JOB_NAME;

/**
 * @author: miaoshuai
 * @create: 2024/03/29 15:34
 * @description:
 */
@Slf4j
@Component
public class ChannelDiffCompleteProcessor implements ShardCompleteProcessor {

    private static final String CHANNEL_DIFF_FUNC_CODE = "huashan.biz.channel.func-code";

    @Autowired
    private ResultChannelBillManage resultChannelBillManage;

    @Autowired
    private LushanFacade lushanFacade;

    @Autowired
    private ApplicationContext context;

    @Override
    public void process(ShardCompleteMessage completeMessage) {
        Date current = DateUtil.getNow();
        DateTime start = DateUtil.beginOfDay(current);
        DateTime end = DateUtil.endOfDay(current);

        ChannelDiffStatisticsDTO diffStatistics = resultChannelBillManage.diffStatistics(start.getTime(), end.getTime());
        ChannelDiffStatisticsDTO squareAccountStatistics = resultChannelBillManage.squareAccountStatistics(start.getTime(), end.getTime());

        DateTime startBill = DateUtil.beginOfMonth(current);
        List<ChannelDiffStatisticsDTO> totalDiffStatistics = resultChannelBillManage.totalDiffStatistics(startBill, end);

        int diffInCount = 0;
        BigDecimal diffInAmount = BigDecimal.ZERO;

        int diffOutCount = 0;
        BigDecimal diffOutAmount = BigDecimal.ZERO;

        int totalDiffCount = 0;
        long totalDiffAmount = 0L;
        for (ChannelDiffStatisticsDTO totalDiffStatistic : totalDiffStatistics) {
            if (TradeType.IN.getKey().equals(totalDiffStatistic.getTradeType())) {
                totalDiffCount = totalDiffCount + totalDiffStatistic.getDiffCount();
                totalDiffAmount = totalDiffAmount + totalDiffStatistic.getAmount();

                diffInCount = totalDiffStatistic.getDiffCount();
                diffInAmount = new AmountVal(totalDiffStatistic.getAmount()).formatYuan();
            }else if (TradeType.OUT.getKey().equals(totalDiffStatistic.getTradeType())) {
                totalDiffCount = totalDiffCount + totalDiffStatistic.getDiffCount();
                totalDiffAmount = totalDiffAmount + totalDiffStatistic.getAmount();

                diffOutCount = totalDiffStatistic.getDiffCount();
                diffOutAmount = new AmountVal(totalDiffStatistic.getAmount()).formatYuan();
            }
        }

        StringBuilder content = new StringBuilder("  \n  &nbsp;1. 账单日：");
        content.append(LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN));
        content.append("  \n  &nbsp;2. 今日渠道侧新增差异条数：").append(diffStatistics.getDiffCount() - squareAccountStatistics.getDiffCount());
        content.append("  \n  &nbsp;3. 今日渠道侧新增差异金额：").append(new AmountVal(diffStatistics.getAmount() - squareAccountStatistics.getAmount()).formatYuan()).append("元");
        content.append("  \n  &nbsp;4. 本月渠道侧差异总条数：").append(StrUtil.format("{}条; 其中收入差异: {}条, 支出差异: {}条", totalDiffCount, diffInCount, diffOutCount));
        content.append("  \n  &nbsp;5. 本月渠道侧差异总金额：").append(StrUtil.format("{}元; 其中收入差异: {}元,支出差异: {}元", new AmountVal(totalDiffAmount).formatYuan(), diffInAmount, diffOutAmount));

        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("标题", "每日收入账核对结果通知");
        jsonObject.put("内容", content.toString());

        log.info("渠道对账结果通知，内容:{}", jsonObject.toJSONString());

        String funcCode = context.getEnvironment().getProperty(CHANNEL_DIFF_FUNC_CODE);
        log.info("从系统变量中获取渠道差异通知funcCde:{}", funcCode);
        if (StrUtil.isBlank(funcCode)) {
            lushanFacade.reportJob(jsonObject, "每日收入账核对结果通知");
        }else {
            lushanFacade.reportJob(jsonObject, "每日收入账核对结果通知", funcCode);
        }
    }

    @Override
    public String jobName() {
        return INCOME_CHECK_DIFF_JOB_NAME;
    }
}
