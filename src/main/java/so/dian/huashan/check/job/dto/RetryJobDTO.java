package so.dian.huashan.check.job.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.util.Date;
import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/24 10:12
 * @description:
 */
@Data
public class RetryJobDTO {

    private Integer checkDate;

    private Long endTime;

    private Long taskMasterId;

    public Long getEndTime() {
        return Optional.ofNullable(endTime).orElse(DateUtil.offsetHour(new Date(),-2).getTime());
    }
}
