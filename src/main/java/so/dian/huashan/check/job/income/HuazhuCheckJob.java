package so.dian.huashan.check.job.income;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huangshan.core.executor.TaskExecuteHelper;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.core.executor.val.TaskOutResult;
import so.dian.huangshan.infrastructure.framework.HSTaskGroup;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.check.service.income.HuazhuCheckService;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.ShardRetryJobParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.task.entity.ShardTaskResult;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: miaoshuai
 * @create: 2023/12/26 16:47
 * @description:
 */
@Slf4j
@Component
public class HuazhuCheckJob {

    public static final String HUAZHU_CHECK_JOB = "huazhuCheckJob";

    private ShardTaskRetryService taskRetryService;

    @Autowired
    private HuazhuCheckService huazhuCheckService;

    @Autowired
    private TaskExecuteHelper taskExecuteHelper;

    @PostConstruct
    public void init() {
        ShardTaskProperties properties = huazhuCheckService.shardTaskProperties();
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }

    @XxlJob(HUAZHU_CHECK_JOB)
    @HSTaskGroup(code = HUAZHU_CHECK_JOB)
    public ReturnT<String> startHz(String param) {
        return ReturnT.SUCCESS;
    }

    @HSTaskScheduled(taskActuator = "huazhuCheckInstallJob")
    public void install(TaskBaseParam param) {

        try {
            InstallJobParam jobParam = new InstallJobParam(param.getBatchNo(), param.getTaskCode());
            jobParam.putExtParam(ChannelIncomeCheckJob.CHECK_DATE, LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
            if (StrUtil.isNotBlank(param.getUserParam()))
                jobParam.putExtParam(ChannelIncomeCheckJob.CHECK_DATE, param.getUserParam());

            huazhuCheckService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称:{}", HUAZHU_CHECK_JOB, e);
            throw e;
        }
    }

    @HSTaskScheduled(taskActuator = "huazhuCheckExecuteJob")
    public void execute(TaskBaseParam param) {

        try {

            List<TaskOutResult> preTaskResults = taskExecuteHelper.listPreTaskResults(param.getBatchNo(), param.getTaskCode());
            TaskOutResult outResult = CollUtil.getFirst(preTaskResults);
            if (Objects.isNull(outResult) || StrUtil.isBlank(outResult.getOutParam()))
                throw BizException.create(SHARD_TASK_FAIL);

            ExecuteJobParam jobParam = new ExecuteJobParam(param.getBatchNo(), param.getTaskCode());
            ShardTaskResult shardTaskResult = JSON.parseObject(outResult.getOutParam(), ShardTaskResult.class);
            jobParam.setTaskMasterId(shardTaskResult.getTaskMasterId());

            huazhuCheckService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称:{}", HUAZHU_CHECK_JOB, e);
            throw e;
        }
    }

    @XxlJob("huazhuCheckRetryExecuteJob")
    public ReturnT<String> retryExecute(String param) {

        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }
        taskRetryService.retry(jobParam, huazhuCheckService::check);
        return ReturnT.SUCCESS;
    }
}
