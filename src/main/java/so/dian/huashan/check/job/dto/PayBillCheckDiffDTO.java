package so.dian.huashan.check.job.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PayBillCheckDiffDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 付款单账单ID
     */
    private Long payBillId;

    /**
     * 三方付款单支付账单ID
     */
    private Long payBillThirdId;

    /**
     * 付款主体id
     */
    private Long paySubjectId;

    /**
     * 付款主体类型
     */
    private Integer paySubjectType;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 账单来源（0-中喜，1-工猫，2-招商cbs）
     */
    private Integer source;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 第三方账单流水号
     */
    private String channelTradeNo;

    /**
     * 交易金额，单位：分
     */
    private Long tradeAmount;

    /**
     * 三方交易金额，单位：分
     */
    private Long thirdTradeAmount;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 未平账原因（1-三方不存在，2-业务方不存在，3-金额不想等，4-招商退汇）
     */
    private Integer reason;

    /**
     * 状态（0-未平账，1-手动平账，2-不参与平账）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 差异金额
     */
    private BigDecimal diffAmount;
}
