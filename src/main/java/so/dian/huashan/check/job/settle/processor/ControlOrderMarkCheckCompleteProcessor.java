package so.dian.huashan.check.job.settle.processor;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.job.settle.ControlOrderMarkCheckJob;
import so.dian.huashan.check.mapper.ControlOrderCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

import java.util.List;
import java.util.stream.Collectors;

import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

@Slf4j
@Component
public class ControlOrderMarkCheckCompleteProcessor extends BaseCheckCompleteProcessor {

    private final static String CONTROL_CHECK_DINGCODE = "huashan.biz.settle.control-dingding-code";

    private final static String JOB_NAME_CN = "订单成本控制对账";

    @Autowired
    private ControlOrderCheckDiffMapper checkDiffMapper;

    @Autowired
    private TaskMasterMapper taskMasterMapper;

    @Autowired
    private Environment env;

    @Override
    public void process(ShardCompleteMessage completeMessage) {
        TaskMasterDO taskMasterDO = taskMasterMapper.selectByPrimaryKey(completeMessage.getTaskMasterId());

        Page<ControlOrderCheckDiffDO> page = PageHelper.startPage(1, 11).doSelectPage(() -> checkDiffMapper.selectIncrementDiff(taskMasterDO.getStartTime().getTime(), taskMasterDO.getFinishTime().getTime()));
        if (CollUtil.isEmpty(page.getResult())) {
            log.info(BILLING_SETTLE, "清结算抽单池标识本次对账结束，没有差异，主任务ID:{}", completeMessage.getTaskMasterId());
            return;
        }

        List<String> orderNos = page.getResult().stream().map(ControlOrderCheckDiffDO::getOrderNo).collect(Collectors.toList());
        sendNotify(JOB_NAME_CN, completeMessage, orderNos, (int) page.getTotal());
    }

    @Override
    public String jobName() {
        return ControlOrderMarkCheckJob.SETTLE_CONTROL_CHECK_JOB_NAME;
    }

    @Override
    protected String getDingdingCode() {
        return env.getProperty(CONTROL_CHECK_DINGCODE);
    }
}
