package so.dian.huashan.check.job.income.processor;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.job.income.ChannelIncomeDiffCheckJob;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.processor.ShardInstallProcessor;

import static so.dian.huashan.check.job.income.ChannelIncomeDiffCheckJob.INCOME_CHECK_DIFF_JOB_NAME;
import static so.dian.huashan.common.exception.LogMarkerFactory.CHANNEL_DIFF_CHECK_MARKER;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2024/01/04 09:55
 * @description:
 */
@Slf4j
@Component
public class ChannelIncomeDiffCheckProcessor extends ShardInstallProcessor {

    @Override
    public String jobName() {
        return INCOME_CHECK_DIFF_JOB_NAME;
    }

    @Override
    public boolean preProcess(InstallJobParam param) {
        String checkDate = param.getExtParam(ChannelIncomeDiffCheckJob.CHECK_DATE);
        if (StrUtil.isBlank(checkDate)) {
            log.warn(CHANNEL_DIFF_CHECK_MARKER, "渠道差异对账前置校验，checkDate 不能为空");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
