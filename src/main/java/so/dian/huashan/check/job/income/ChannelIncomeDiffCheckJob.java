package so.dian.huashan.check.job.income;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huangshan.core.executor.TaskExecuteHelper;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.core.executor.val.TaskOutResult;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.check.service.income.ChannelIncomeDiffCheckService;
import so.dian.huashan.common.val.IncomeCheckParam;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.ShardRetryJobParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.task.entity.ShardTaskResult;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:32
 * @description: 渠道收入账任务
 */
@Slf4j
@Component
public class ChannelIncomeDiffCheckJob {

    public final static String CHECK_DATE = "checkDate";

    public final static String INCOME_CHECK_DIFF_JOB_NAME = "incomeDiffCheckJob";

    private ShardTaskRetryService taskRetryService;

    @Autowired
    private ChannelIncomeDiffCheckService incomeDiffCheckService;

    @Autowired
    private TaskExecuteHelper taskExecuteHelper;

    @PostConstruct
    public void init() {
        ShardTaskProperties properties = incomeDiffCheckService.shardTaskProperties();
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }

    /**
     * 增量对账分片生成任务
     */
    @HSTaskScheduled(taskActuator = "incomeDiffCheckInstallJob")
    public void install(TaskBaseParam param) {

        try {
            IncomeCheckParam checkParam = IncomeCheckParam.from(param);
            InstallJobParam jobParam = new InstallJobParam(param.getBatchNo(), param.getTaskCode());
            jobParam.putExtParam(CHECK_DATE, checkParam.getCheckDate());
            if (StrUtil.isBlank(checkParam.getCheckDate()))
                jobParam.putExtParam(CHECK_DATE, LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN));

            incomeDiffCheckService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称：{}", INCOME_CHECK_DIFF_JOB_NAME, e);
            throw e;
        }
    }

    /**
     * 增量对账分片执行任务
     */
    @HSTaskScheduled(taskActuator = "incomeDiffCheckExecuteJob")
    public void execute(TaskBaseParam param) {

        try {
            List<TaskOutResult> preTaskResults = taskExecuteHelper.listPreTaskResults(param.getBatchNo(), param.getTaskCode());
            TaskOutResult outResult = CollUtil.getFirst(preTaskResults);
            if (Objects.isNull(outResult) || StrUtil.isBlank(outResult.getOutParam()))
                throw BizException.create(SHARD_TASK_FAIL);

            ExecuteJobParam jobParam = new ExecuteJobParam(param.getBatchNo(), param.getTaskCode());
            ShardTaskResult shardTaskResult = JSON.parseObject(outResult.getOutParam(), ShardTaskResult.class);
            jobParam.setTaskMasterId(shardTaskResult.getTaskMasterId());
            incomeDiffCheckService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "分片执行异常, 任务名称:{}", INCOME_CHECK_DIFF_JOB_NAME, e);
            throw e;
        }
    }

    /**
     * 增量对账分片重试任务
     */
    @XxlJob("incomeDiffCheckRetryJob")
    public ReturnT<String> retryExecute(String param) {

        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }
        taskRetryService.retry(jobParam, incomeDiffCheckService::check);
        return ReturnT.SUCCESS;
    }
}
