package so.dian.huashan.check.job.iot.processor;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.mapper.SplitBillCheckDiffMapper;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.processor.ShardCompleteProcessor;

import javax.annotation.Resource;
import java.util.Date;

import static so.dian.huashan.check.job.iot.SettleWxSplitCheckDiffJob.SETTLE_WX_SPLIT_DIFF_JOB_NAME;

/**
 * @author: xingba
 * @create: 2024/09/10 15:34
 * @description:
 */
@Slf4j
@Component
public class SplitBillCheckDiffCompleteProcessor implements ShardCompleteProcessor {

    private static final String CHANNEL_DIFF_FUNC_CODE = "huashan.biz.channel.func-code";

    @Resource
    private SplitBillCheckDiffMapper splitBillCheckDiffMapper;

    @Autowired
    private LushanFacade lushanFacade;

    @Autowired
    private ApplicationContext context;

    @Override
    public void process(ShardCompleteMessage completeMessage) {
        Date current = DateUtil.getNow();
        Long end = DateUtil.endOfMonth(current).getTime();
        Long startBill = DateUtil.beginOfMonth(current).getTime();
        Long count= splitBillCheckDiffMapper.totalDiffStatistics(startBill, end);

        StringBuilder content = new StringBuilder("  \n  &nbsp;1. 账单日：");
        content.append(LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN));
        content.append("  \n  &nbsp;2. 本月iot分账差异总条数：").append(StrUtil.format("{}条;", count));

        JSONObject jsonObject = new JSONObject(true);
        jsonObject.put("标题", "每月iot分账核对结果通知");
        jsonObject.put("内容", content.toString());

        log.info("iot分账对账结果通知，内容:{}", jsonObject.toJSONString());

        String funcCode = context.getEnvironment().getProperty(CHANNEL_DIFF_FUNC_CODE);
        log.info("从系统变量中获取iot分账差异通知funcCde:{}", funcCode);
        if (StrUtil.isBlank(funcCode)) {
            lushanFacade.reportJob(jsonObject, "每月iot分账核对结果通知");
        }else {
            lushanFacade.reportJob(jsonObject, "每月iot分账核对结果通知", funcCode);
        }
    }

    @Override
    public String jobName() {
        return SETTLE_WX_SPLIT_DIFF_JOB_NAME;
    }
}
