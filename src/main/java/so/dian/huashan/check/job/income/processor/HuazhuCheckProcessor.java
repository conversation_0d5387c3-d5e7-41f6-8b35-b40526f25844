package so.dian.huashan.check.job.income.processor;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.job.income.ChannelIncomeCheckJob;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.processor.ShardInstallProcessor;

import static so.dian.huashan.check.job.income.HuazhuCheckJob.HUAZHU_CHECK_JOB;
import static so.dian.huashan.common.exception.LogMarkerFactory.HUAZHU_CHECK_MARKER;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2024/01/03 18:28
 * @description:
 */
@Slf4j
@Component
public class HuazhuCheckProcessor extends ShardInstallProcessor {

    @Override
    public String jobName() {
        return HUAZHU_CHECK_JOB;
    }

    @Override
    public boolean preProcess(InstallJobParam param) {
        String checkDate = param.getExtParam(ChannelIncomeCheckJob.CHECK_DATE);
        if (StrUtil.isBlank(checkDate)) {
            log.warn(HUAZHU_CHECK_MARKER, "华住对账前置校验，checkDate 不能为空");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }
}
