package so.dian.huashan.check.job.settle.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.framework.task.processor.ShardCompleteProcessor;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

@Slf4j
public abstract class BaseCheckCompleteProcessor implements ShardCompleteProcessor {

    @Resource
    private LushanFacade lushanFacade;

    protected void sendNotify(String jobName, ShardCompleteMessage completeMessage, List<String> orderNos, Integer countDiff) {
        if (Objects.isNull(completeMessage) || CollUtil.isEmpty(orderNos))
            return;

        StringBuilder orderNoSb = new StringBuilder();
        int showCount = 0;
        for (String orderNo : orderNos) {
            showCount ++;
            if (showCount > 10) {
                orderNoSb.append("...");
                orderNoSb.append(StrUtil.LF);
                break;
            }
            orderNoSb.append("&nbsp;&nbsp;");
            orderNoSb.append(orderNo);
            orderNoSb.append(StrUtil.LF);
        }
        orderNoSb.deleteCharAt(orderNoSb.length() - 1);
        String content = "  \n  &nbsp;1. 总分片数：" + completeMessage.getAllShardCount() +
                "  \n  &nbsp;2. 执行成功片数：" + completeMessage.getSuccessShardCount() +
                "  \n  &nbsp;3. 执行失败片数：" + completeMessage.getFailShardCount() +
                "  \n  &nbsp;4. 增量差异数：" + countDiff +
                "  \n  &nbsp;5. 最近增量差异订单号：" + "\n" + orderNoSb;

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("内容", content);

        String dingdingCode = getDingdingCode();
        if (StrUtil.isBlank(dingdingCode)) {
            lushanFacade.reportJob(jsonObject, Optional.ofNullable(jobName).orElse(completeMessage.getJobName()));
        }else {
            lushanFacade.reportJob(jsonObject, Optional.ofNullable(jobName).orElse(completeMessage.getJobName()), dingdingCode);
        }
        log.info(BILLING_SETTLE, "发送任务执行完成结果，{}", jsonObject.toJSONString());
    }

    protected abstract String getDingdingCode();
}
