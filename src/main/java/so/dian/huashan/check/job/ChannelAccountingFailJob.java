package so.dian.huashan.check.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.service.ChannelAccountService;
import so.dian.huashan.common.util.LocalPatternUtils;
import so.dian.huashan.task.enums.InvokeTypeEnum;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * ChannelAccountingJob 渠道对账补偿任务
 *
 * <AUTHOR>
 * @desc
 * @date 2022/8/26 10:09
 */
@Slf4j
@Component
public class ChannelAccountingFailJob {

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private ChannelAccountService channelAccountService;
//
//    @Resource
//    private LushanFacade lushanFacade;

    @Resource
    private TaskSubMapper taskSubMapper;

    @XxlJob("ChannelAccountingFailJob")
    public ReturnT<String> execute(String param) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("start","渠道失败对账任务补偿任务开始");
//        lushanFacade.reportJob(jsonObject,"ChannelAccountingFailJob");

        String checkDate = param;
        if (StringUtils.isBlank(param)) {
            checkDate = LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN);
        } else if (!LocalPatternUtils.dateMatches(param)) {
            log.info("ChannelAccountingFailJob 日期格式不符合,str:" + param);
            /**
             * 检查是否日期类型
             */
            return new ReturnT<>(ReturnT.FAIL_CODE, "日期格式不符合");
        }
        log.info("ChannelAccountingFailJob 日期检查结束,str:{},checkDate:{}",param,checkDate);
        Integer date = Integer.valueOf(checkDate);
        // 查询主任务，判断状态是否为成功，如果成功
        List<Long> ids = taskSubMapper.selectByFailStatus(DateUtil.offsetHour(new Date(),-2).getTime());
        if(CollectionUtil.isEmpty(ids)){
            return ReturnT.SUCCESS;
        }
        List<TaskMasterDO> taskMasterDOS = taskMasterMapper.selectByIds(ids,InvokeTypeEnum.CHANNEL_BURST.code());
        if(CollectionUtil.isEmpty(taskMasterDOS)){
            return ReturnT.SUCCESS;
        }

        for(TaskMasterDO taskMasterDO : taskMasterDOS){
            log.info(">>> 对账重试，开始执行，主任务ID:{}", taskMasterDO.getId());
            try {
                channelAccountService.channel(taskMasterDO,InvokeTypeEnum.from(taskMasterDO.getInvokeType()));
            } catch (Exception e) {
                log.error(">>>> 渠道失败对账异常 | taskMasterDO:{},ChannelAccountingFailJob = {}",JSONObject.toJSONString(taskMasterDO),e);
            }
        }

//        jsonObject.put("end","渠道失败对账任务结束");
//        lushanFacade.reportJob(jsonObject,"ChannelAccountingFailJob");
        return ReturnT.SUCCESS;
    }

}
