package so.dian.huashan.check.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.manager.ThirdCallLogManager;
import so.dian.huashan.collection.mapper.dos.ThirdCallLogDO;
import so.dian.huashan.collection.mapper.param.ThirdCallLogParam;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.utils.DateUtil;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ThirdCallLogStatisticsJob {
    @Autowired
    private LushanFacade lushanFacade;
    @Autowired
    private ThirdCallLogManager thirdCallLogManager;
    @Value("${huashan.biz.log.code}")
    private String funcCode;

    @XxlJob("ThirdCallLogStatisticsJob")
    public ReturnT<String> execute(String param) {
        try {
            Date beginDate = LocalDateUtils.offsetDay(LocalDateUtils.beginOfDay(DateTime.now()), -1);;
            if (StringUtils.isNotBlank(param)) {
                beginDate = LocalDateUtils.parse(param,"yyyy-MM-dd");
            }
            Integer callDate = DateUtil.parseDateYyyyMMdd2IntThrowException(beginDate);
            List<ThirdCallLogDO> thirdCallLogDOList = thirdCallLogManager.selectByParam(ThirdCallLogParam.builder().callDate(callDate).build());
            if (CollectionUtils.isEmpty(thirdCallLogDOList)) {
                return ReturnT.SUCCESS;
            }
            String content = getContent(thirdCallLogDOList, callDate);
            if (StrUtil.isBlank(content)) {
                log.warn("内容不存在");
                return ReturnT.SUCCESS;
            }
            JSONObject jsonObject = new JSONObject(true);
            jsonObject.put("标题", "每日三方对账接口调用结果通知");
            jsonObject.put("内容", content);
            log.info("三方对账结果通知，内容:{}", jsonObject.toJSONString());

            lushanFacade.reportJob(jsonObject, "ThirdCallLogStatisticsJob", funcCode);
            log.info("发送三方对账结果通知成功");
        } catch (Exception e) {
            log.error("第三方调用统计失败 param:{}", param, e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 获取通知内容
     */
    private String getContent(List<ThirdCallLogDO> thirdCallLogDOList, Integer callDate) {
        if (CollectionUtils.isEmpty(thirdCallLogDOList)) {
            return null;
        }
        log.info("获取内容thirdCallLogDOList.size:{},callDate:{}",thirdCallLogDOList.size(),callDate);
        // 成功数量
        Integer successCount = 0;
        // 不包括账单不存在、第三方返回失败
        Integer failCountExcludeBillNotExist = 0;
        // 账单不存在数量
        Integer billNotExistCount = 0;
        for (ThirdCallLogDO thirdCallLogDO : thirdCallLogDOList) {
            if (Objects.isNull(thirdCallLogDO.getHttpStatusCode())) {
                continue;
            }
            if (200 == thirdCallLogDO.getHttpStatusCode()) {
                successCount++;
                continue;
            }
            if (Objects.nonNull(thirdCallLogDO.getThirdErrorCode())) {
                if ("NO_STATEMENT_EXIST".equals(thirdCallLogDO.getThirdErrorCode())) {
                    billNotExistCount++;
                    continue;
                }
                failCountExcludeBillNotExist++;
            }
        }
        StringBuilder content = new StringBuilder("  \n  &nbsp;1. 三方对账接口调用日：");
        content.append(callDate);
        content.append("  \n  &nbsp;2. 总调用量：").append(thirdCallLogDOList.size());
        content.append("  \n  &nbsp;3. 成功量：").append(successCount);
        content.append("  \n  &nbsp;4. 失败量(不包括对账不存在)：").append(failCountExcludeBillNotExist);
        content.append("  \n  &nbsp;5. 对账不存在量：").append(billNotExistCount);
        log.info("第三方调用统计结果 callDate:{},successCount:{},failCountExcludeBillNotExist:{},billNotExistCount:{}", callDate, successCount, failCountExcludeBillNotExist, billNotExistCount);
        return content.toString();

    }
}
