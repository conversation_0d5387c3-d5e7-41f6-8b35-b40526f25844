package so.dian.huashan.check.job.credit.shipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huangshan.core.executor.TaskExecuteHelper;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.core.executor.val.TaskOutResult;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.check.service.credit.shipment.ShipmentTradeService;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

@Slf4j
@Component
public class ShipmentTradeCheckJob {
    public final static String CHECK_DATE_TIME = "checkDateTime";

    public final static String CREDIT_JOB_NAME = "shipmentTradeCheckJob";
    @Autowired
    private ShipmentTradeService shipmentTradeService;

    private ShardTaskRetryService taskRetryService;
    @Resource
    private TaskExecuteHelper taskExecuteHelper;
    @PostConstruct
    public void init() {
        ShardTaskProperties properties = new ShardTaskProperties(CREDIT_JOB_NAME);
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }
    @HSTaskScheduled(taskActuator = "shipmentTradeInstallJob")
    public void shipmentTradeInstall(TaskBaseParam param) {
        log.info("shipmentTradeInstall get param:{}",param);
        try {
            InstallJobParam jobParam = new InstallJobParam(param.getBatchNo(), param.getTaskCode());
            Map<String, Object> paramMap = JSON.parseObject(param.getUserParam(), new TypeReference<Map<String, Object>>() {
            });
            if (CollUtil.isNotEmpty(paramMap))
                jobParam.setExtParam(paramMap);
            shipmentTradeService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称::{}", CREDIT_JOB_NAME, e);
            throw e;
        }
    }

    @HSTaskScheduled(taskActuator = "shipmentTradeExecuteJob")
    public void shipmentTradeExecute(TaskBaseParam param) {
        log.info("shipmentTradeExecute get param:{}",param);
        List<TaskOutResult> preTaskResults = taskExecuteHelper.listPreTaskResults(param.getBatchNo(), param.getTaskCode());
        TaskOutResult outResult = CollUtil.getFirst(preTaskResults);
        if (Objects.isNull(outResult) || StrUtil.isBlank(outResult.getOutParam()))
            throw BizException.create(SHARD_TASK_FAIL);

        ExecuteJobParam jobParam = new ExecuteJobParam(param.getBatchNo(), param.getTaskCode());
        ShardTaskResult shardTaskResult = JSON.parseObject(outResult.getOutParam(), ShardTaskResult.class);
        jobParam.setTaskMasterId(shardTaskResult.getTaskMasterId());
        shipmentTradeService.execute(jobParam);
    }

    /**
     * 增量对账分片重试任务
     */
    @XxlJob("shipmentTradeRetryJob")
    public ReturnT<String> retryExecute(String param) {
        log.info("shipmentTradeRetryJob get param:{}",param);
        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }
        taskRetryService.retry(jobParam, shipmentTradeService::check);
        return ReturnT.SUCCESS;
    }
}
