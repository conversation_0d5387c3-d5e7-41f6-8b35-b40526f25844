package so.dian.huashan.check.job.iot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huangshan.core.executor.TaskExecuteHelper;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.core.executor.val.TaskOutResult;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.check.service.iot.WxsplitCheckDiffService;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: xingba
 * @create: 2024/09/09 16:32
 * @description: iot业务微信差异分账
 */
@Slf4j
@Component
public class SettleWxSplitCheckDiffJob {

    public final static String CHECK_DATE_TIME = "billDate";

    public final static String SETTLE_WX_SPLIT_DIFF_JOB_NAME = "settleWxSplitCheckDiffJob";

    private ShardTaskRetryService taskRetryService;

    @Resource
    private WxsplitCheckDiffService wxsplitCheckDiffService;

    @Resource
    private TaskExecuteHelper taskExecuteHelper;

    @PostConstruct
    public void init() {
        ShardTaskProperties properties = wxsplitCheckDiffService.shardTaskProperties();
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }

    /**
     * 增量对账分片生成任务
     */
    @HSTaskScheduled(taskActuator = "settleWxSplitCheckDiffInstallJob")
    public void install(TaskBaseParam param) {
        try {
            InstallJobParam jobParam = new InstallJobParam(param.getBatchNo(), param.getTaskCode());
            Map<String, Object> paramMap = JSON.parseObject(param.getUserParam(), new TypeReference<Map<String, Object>>() {
            });
            if (CollUtil.isNotEmpty(paramMap))
                jobParam.setExtParam(paramMap);
            wxsplitCheckDiffService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称::{}", SETTLE_WX_SPLIT_DIFF_JOB_NAME, e);
            throw e;
        }

    }

    /**
     * 增量对账分片执行任务
     */
//    @XxlJob("settleWxSplitCheckDiffExecuteJob")
    @HSTaskScheduled(taskActuator = "settleWxSplitCheckDiffExecuteJob")
    public void execute(TaskBaseParam param) {

        try {
            List<TaskOutResult> preTaskResults = taskExecuteHelper.listPreTaskResults(param.getBatchNo(), param.getTaskCode());
            TaskOutResult outResult = CollUtil.getFirst(preTaskResults);
            if (Objects.isNull(outResult) || StrUtil.isBlank(outResult.getOutParam()))
                throw BizException.create(SHARD_TASK_FAIL);

            ExecuteJobParam jobParam = new ExecuteJobParam(param.getBatchNo(), param.getTaskCode());
            ShardTaskResult shardTaskResult = JSON.parseObject(outResult.getOutParam(), ShardTaskResult.class);
            jobParam.setTaskMasterId(shardTaskResult.getTaskMasterId());

            wxsplitCheckDiffService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常, 任务名称:{}", SETTLE_WX_SPLIT_DIFF_JOB_NAME, e);
            throw e;
        }
    }

    /**
     * 增量对账分片重试任务
     */
    @XxlJob("settleWxSplitCheckDiffRetryJob")
    public ReturnT<String> retrySettleWxSplitCheckDiffExecute(String param) {

        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }
        taskRetryService.retry(jobParam, wxsplitCheckDiffService::check);
        return ReturnT.SUCCESS;
    }
}
