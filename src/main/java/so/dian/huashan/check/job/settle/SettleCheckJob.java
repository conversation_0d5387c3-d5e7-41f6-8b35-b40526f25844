package so.dian.huashan.check.job.settle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huangshan.core.executor.TaskExecuteHelper;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.core.executor.val.TaskOutResult;
import so.dian.huangshan.infrastructure.framework.HSTaskGroup;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.check.service.settle.BillSettleCheckService;
import so.dian.huashan.framework.task.ShardTaskFactory;
import so.dian.huashan.framework.task.ShardTaskRetryService;
import so.dian.huashan.framework.task.entity.ExecuteJobParam;
import so.dian.huashan.framework.task.entity.InstallJobParam;
import so.dian.huashan.framework.task.entity.ShardRetryJobParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.task.entity.ShardTaskResult;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static so.dian.huashan.common.enums.BizErrorCode.SHARD_TASK_FAIL;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: miaoshuai
 * @create: 2023/12/18 10:54
 * @description: 清结算对账任务
 */
@Slf4j
@Component
public class SettleCheckJob {

    public final static String SETTLE_CHECK_JOB_NAME = "settleIncrementCheckJob";

    private ShardTaskRetryService taskRetryService;

    @Autowired
    private BillSettleCheckService billSettleCheckService;

    @Autowired
    private TaskExecuteHelper taskExecuteHelper;

    @PostConstruct
    public void init() {
        ShardTaskProperties properties = billSettleCheckService.shardTaskProperties();
        taskRetryService = ShardTaskFactory.createRetryService(properties);
    }

    @XxlJob(SETTLE_CHECK_JOB_NAME)
    @HSTaskGroup(code = SETTLE_CHECK_JOB_NAME)
    public ReturnT<String> startCheck(String param){
        return ReturnT.SUCCESS;
    }

    /**
     * 增量对账分片生成任务
     */
    @HSTaskScheduled(taskActuator = "settleIncrementCheckInstallJob")
    public void install(TaskBaseParam param) {

        try {
            InstallJobParam jobParam = new InstallJobParam(param.getBatchNo(), param.getTaskCode());
            Map<String, Object> paramMap = JSON.parseObject(param.getUserParam(), new TypeReference<Map<String, Object>>() {
            });

            if (CollUtil.isNotEmpty(paramMap))
                jobParam.setExtParam(paramMap);

            billSettleCheckService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "安装主体变更分片任务异常", e);
            throw e;
        }
    }

    /**
     * 增量对账分片执行任务
     */
    @HSTaskScheduled(taskActuator = "settleIncrementCheckExecuteJob")
    public void execute(TaskBaseParam param) {

        try {
            List<TaskOutResult> preTaskResults = taskExecuteHelper.listPreTaskResults(param.getBatchNo(), param.getTaskCode());
            TaskOutResult outResult = CollUtil.getFirst(preTaskResults);
            if (Objects.isNull(outResult) || StrUtil.isBlank(outResult.getOutParam()))
                throw BizException.create(SHARD_TASK_FAIL);

            ExecuteJobParam jobParam = new ExecuteJobParam(param.getBatchNo(), param.getTaskCode());
            ShardTaskResult shardTaskResult = JSON.parseObject(outResult.getOutParam(), ShardTaskResult.class);
            jobParam.setTaskMasterId(shardTaskResult.getTaskMasterId());

            billSettleCheckService.execute(jobParam);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "分片执行异常, 任务名称:{}", SETTLE_CHECK_JOB_NAME, e);
            throw e;
        }
    }

    /**
     * 差异对账执行任务
     */
    @XxlJob("settleDiffCheckExecuteJob")
    public ReturnT<String> diffExecute(String param) {

        try {
            billSettleCheckService.diffCheck();
        }catch (Exception e) {
            log.warn(SYSTEM_ERROR, "清结算差异对账任务执行异常", e);
            throw e;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 清结算对账重试任务，不建议启动，出现对账失败的分片时可以通过手动触发来执行
     */
    @XxlJob("settleRetryCheckJob")
    public ReturnT<String> retryExecute(String param) {

        ShardRetryJobParam jobParam = JSON.parseObject(param, ShardRetryJobParam.class);
        if (Objects.isNull(jobParam)) {
            jobParam = new ShardRetryJobParam();
        }

        try {
            taskRetryService.retry(jobParam, billSettleCheckService::incrementCheck);
        }catch (Exception e) {
            log.warn(SYSTEM_ERROR, "清结算重试任务执行异常", e);
            throw e;
        }

        return ReturnT.SUCCESS;
    }
}
