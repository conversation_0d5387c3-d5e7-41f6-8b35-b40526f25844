package so.dian.huashan.check.service.iot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.emuns.iot.SettleDetailScene;
import so.dian.huashan.check.emuns.iot.WxSplitDiffStatus;
import so.dian.huashan.check.job.dto.DiffReasonDTO;
import so.dian.huashan.check.job.dto.SettleWxDiffReasonDTO;
import so.dian.huashan.check.job.iot.SettleDetailCheckJob;
import so.dian.huashan.check.job.iot.SettleWxSplitCheckDiffJob;
import so.dian.huashan.check.mapper.SplitBillCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO;
import so.dian.huashan.collection.mapper.SettleDetailOriginalMapper;
import so.dian.huashan.collection.mapper.WxSplitBillDetailMapper;
import so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static so.dian.huashan.check.job.iot.SettleWxSplitCheckDiffJob.SETTLE_WX_SPLIT_DIFF_JOB_NAME;

/**
 * @author: xingba
 * @create: 2024/09/10 16:37
 * @description:
 */
@Service
@Slf4j
public class WxsplitCheckDiffService extends ShardParamRecordService{

    private final static String MAX_ID = "max.id";

    private final static Integer ONE = 1;


    @Resource
    private SplitBillCheckDiffMapper splitBillCheckDiffMapper;

    @Resource
    private WxSplitBillDetailMapper wxSplitBillDetailMapper;

    @Resource
    private SettleDetailOriginalMapper settleDetailOriginalMapper;

    @Resource
    private SettleWxBaseService settleWxBaseService;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDate = shardParam.getJobParam().getExtParam(SettleWxSplitCheckDiffJob.CHECK_DATE_TIME);
        if (StringUtils.isEmpty(checkDate)) {
            shardParam.putExtParam(SettleWxSplitCheckDiffJob.CHECK_DATE_TIME, LocalDateUtils.format(LocalDateUtils.yesterday(),NORM_DATETIME_PATTERN));
            checkDate = LocalDateUtils.format(LocalDateUtils.date(),NORM_DATETIME_PATTERN);
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Long startTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(checkDateTime,-1)).getTime();

        Long endTime = DateUtil.endOfMonth(checkDateTime).getTime();

        Page<Long> page = PageHelper.startPage(ONE, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        splitBillCheckDiffMapper.selectByCheckDateTime(startTime, endTime, shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<SplitBillCheckDiffDO> splitBillCheckDiffDOS = splitBillCheckDiffMapper.selectByIds(ids);
        if (CollUtil.isEmpty(splitBillCheckDiffDOS))
            return;

        // 渠道不存在
        List<SplitBillCheckDiffDO> notWxSplitBillList = splitBillCheckDiffDOS.stream().filter(o -> SettleDetailScene.NOT_THIRD.getCode().equals(o.getDiffScene())).collect(Collectors.toList());
        // 业务不存在
        List<SplitBillCheckDiffDO> notSettleDetailList = splitBillCheckDiffDOS.stream().filter(o -> SettleDetailScene.NOT_BIZ.getCode().equals(o.getDiffScene())).collect(Collectors.toList());
        // 字段差异
        List<SplitBillCheckDiffDO> notSenceList = splitBillCheckDiffDOS.stream().filter(o -> SettleDetailScene.NOT_SCENE.getCode().equals(o.getDiffScene())).collect(Collectors.toList());

        List<SplitBillCheckDiffDO> addDiffList = new ArrayList<>();
        List<SplitBillCheckConsistencyDO> addConsistencyList = new ArrayList<>();
        List<Long> idList = new ArrayList<>();
        List<Long> wxSplitIds = new ArrayList<>();
        List<Long> settleIds = new ArrayList<>();

        // 业务对账逻辑
        // 渠道不存在
        checkWxSplitNot(notWxSplitBillList, addDiffList, idList, wxSplitIds, addConsistencyList,settleIds);

        // 业务不存在
        checkBizNot(notSettleDetailList, addDiffList, idList, wxSplitIds, addConsistencyList,settleIds);

        // 都存在，字段差异
        checkFieldNot(notSenceList, addDiffList, idList, wxSplitIds, addConsistencyList,settleIds);

        // 批量操作数据，事物控制
        settleWxBaseService.batchDiffSave(addDiffList, addConsistencyList, idList, wxSplitIds,settleIds);
    }

    private void checkFieldNot(List<SplitBillCheckDiffDO> notSenceList, List<SplitBillCheckDiffDO> addDiffList,
                               List<Long> idList, List<Long> wxSplitIds, List<SplitBillCheckConsistencyDO> addConsistencyList,List<Long> settleIds){
        if (CollectionUtils.isNotEmpty(notSenceList)) {
            Map<String, WxSplitBillDetailDO> mapNotWxSplitBill = new HashMap();
            Map<String, SettleDetailOriginalDO> mapNotSettleDetail = new HashMap();
            List<String> list = notSenceList.stream().map(SplitBillCheckDiffDO::getSettleDetailNo).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(list)) {
                buildMap(list, mapNotWxSplitBill, mapNotSettleDetail);
                // 2）对数据进行for循环
                for (SplitBillCheckDiffDO splitBillCheckDiffDO : notSenceList) {
                    SettleDetailOriginalDO settleDetailOriginalDO = mapNotSettleDetail.get(splitBillCheckDiffDO.getSettleDetailNo());
                    WxSplitBillDetailDO wxSplitBillDetailDO = mapNotWxSplitBill.get(splitBillCheckDiffDO.getSettleDetailNo());
                    // 字段差异
                    checkFieldDiff(settleDetailOriginalDO, wxSplitBillDetailDO, splitBillCheckDiffDO, addDiffList, idList, wxSplitIds, addConsistencyList,settleIds);
                }
            }
        }
    }
    /**
     * 业务不存在
     * @param notSettleDetailList
     * @param addDiffList
     * @param idList
     * @param wxSplitIds
     * @param addConsistencyList
     */
    private void checkBizNot(List<SplitBillCheckDiffDO> notSettleDetailList, List<SplitBillCheckDiffDO> addDiffList,
                                 List<Long> idList, List<Long> wxSplitIds, List<SplitBillCheckConsistencyDO> addConsistencyList,List<Long> settleIds) {
        if (CollectionUtils.isNotEmpty(notSettleDetailList)) {
            // 1）根据结算单号查出这批付款
            List<String> listSettleDetail = notSettleDetailList.stream().map(SplitBillCheckDiffDO::getSettleDetailNo).collect(Collectors.toList());
            Map<String, WxSplitBillDetailDO> mapNotWxSplitBill = new HashMap();
            Map<String, SettleDetailOriginalDO> mapNotSettleDetail = new HashMap();
            if (CollectionUtils.isNotEmpty(listSettleDetail)) {
                buildMap(listSettleDetail, mapNotWxSplitBill, mapNotSettleDetail);
                // 2）对数据进行for循环
                for (SplitBillCheckDiffDO splitBillCheckDiffDO : notSettleDetailList) {
                    SettleDetailOriginalDO settleDetailOriginalDO = mapNotSettleDetail.get(splitBillCheckDiffDO.getSettleDetailNo());
                    if (Objects.isNull(settleDetailOriginalDO)) {
                        splitBillCheckDiffDO.setGmtUpdate(System.currentTimeMillis());
                        splitBillCheckDiffDO.setCheckCount(splitBillCheckDiffDO.getCheckCount() + ONE);
                        addDiffList.add(splitBillCheckDiffDO);
                        continue;
                    }
                    WxSplitBillDetailDO wxSplitBillDetailDO = mapNotWxSplitBill.get(splitBillCheckDiffDO.getSettleDetailNo());
                    // 字段差异
                    checkFieldDiff(settleDetailOriginalDO, wxSplitBillDetailDO, splitBillCheckDiffDO, addDiffList, idList, wxSplitIds, addConsistencyList,settleIds);
                }
            }
        }
    }

    /**
     * 渠道不存在
     * @param notWxSplitBillList
     * @param addDiffList
     * @param idList
     * @param wxSplitIds
     * @param addConsistencyList
     */
    private void checkWxSplitNot(List<SplitBillCheckDiffDO> notWxSplitBillList, List<SplitBillCheckDiffDO> addDiffList,
                             List<Long> idList, List<Long> wxSplitIds, List<SplitBillCheckConsistencyDO> addConsistencyList,List<Long> settleIds) {
        if (CollectionUtils.isNotEmpty(notWxSplitBillList)) {
            List<String> list = notWxSplitBillList.stream().map(SplitBillCheckDiffDO::getSettleDetailNo).collect(Collectors.toList());
            Map<String, WxSplitBillDetailDO> mapNotWxSplitBill = new HashMap();
            Map<String, SettleDetailOriginalDO> mapNotSettleDetail = new HashMap();
            if (CollectionUtils.isNotEmpty(list)) {
                buildMap(list, mapNotWxSplitBill, mapNotSettleDetail);
                // 2）对数据进行for循环
                for (SplitBillCheckDiffDO splitBillCheckDiffDO : notWxSplitBillList) {
                    WxSplitBillDetailDO wxSplitBillDetailDO = mapNotWxSplitBill.get(splitBillCheckDiffDO.getSettleDetailNo());
                    if (Objects.isNull(wxSplitBillDetailDO)) {
                        splitBillCheckDiffDO.setGmtUpdate(System.currentTimeMillis());
                        splitBillCheckDiffDO.setCheckCount(splitBillCheckDiffDO.getCheckCount() + ONE);
                        addDiffList.add(splitBillCheckDiffDO);
                        continue;
                    }
                    SettleDetailOriginalDO settleDetailOriginalDO = mapNotSettleDetail.get(splitBillCheckDiffDO.getSettleDetailNo());
                    // 字段差异
                    checkFieldDiff(settleDetailOriginalDO, wxSplitBillDetailDO, splitBillCheckDiffDO, addDiffList, idList, wxSplitIds, addConsistencyList,settleIds);
                }
            }
        }
    }

    private void buildMap(List<String> list, Map<String, WxSplitBillDetailDO> mapNotWxSplitBill, Map<String, SettleDetailOriginalDO> mapNotSettleDetail) {
        List<WxSplitBillDetailDO> wxSplitBillDetailDOS = wxSplitBillDetailMapper.selectBySettleDetailNo(list);
        if (CollectionUtils.isNotEmpty(wxSplitBillDetailDOS)) {
            mapNotWxSplitBill.putAll(wxSplitBillDetailDOS.stream().collect(Collectors.toMap(WxSplitBillDetailDO::getSettleDetailNo, Function.identity())));
        }
        List<SettleDetailOriginalDO> settleDetailOriginalDOS = settleDetailOriginalMapper.selectBySettleDetailNos(list);
        if (CollectionUtils.isNotEmpty(settleDetailOriginalDOS)) {
            mapNotSettleDetail.putAll(settleDetailOriginalDOS.stream().collect(Collectors.toMap(SettleDetailOriginalDO::getSettleDetailNo, Function.identity())));
        }
    }

    private void checkFieldDiff(SettleDetailOriginalDO settleDetailOriginalDO, WxSplitBillDetailDO wxSplitBillDetailDO, SplitBillCheckDiffDO splitBillCheckDiffDO, List<SplitBillCheckDiffDO> addDiffList,
                                List<Long> idList, List<Long> wxSplitIds, List<SplitBillCheckConsistencyDO> addConsistencyList,List<Long> settleIds) {
        // 字段差异
        if ((Objects.nonNull(settleDetailOriginalDO.getTpSettleNo()) && Objects.nonNull(wxSplitBillDetailDO.getSplitDetailNo()) && !settleDetailOriginalDO.getTpSettleNo().equals(wxSplitBillDetailDO.getSplitDetailNo()))
                || !settleDetailOriginalDO.getAmountOutput().equals(wxSplitBillDetailDO.getSplitAmt())
                || !settleDetailOriginalDO.getTpSourceId().equals(wxSplitBillDetailDO.getSplitSourceId())
                || !settleDetailOriginalDO.getTpReceiveId().equals(wxSplitBillDetailDO.getSplitReceiveId())) {

            addDiffList.add(buildSplitBillCheckDiffDO(settleDetailOriginalDO, wxSplitBillDetailDO, splitBillCheckDiffDO));
            wxSplitIds.add(wxSplitBillDetailDO.getId());
            settleIds.add(settleDetailOriginalDO.getId());
            return;
        }
        idList.add(splitBillCheckDiffDO.getId());
        wxSplitIds.add(wxSplitBillDetailDO.getId());
        settleIds.add(settleDetailOriginalDO.getId());
        // 添加平账记录
        addConsistencyList.add(settleWxBaseService.buildSplitBillCheckConsistencyDO(settleDetailOriginalDO, wxSplitBillDetailDO));
    }

    private SplitBillCheckDiffDO buildSplitBillCheckDiffDO(SettleDetailOriginalDO settleDetailOriginalDO, WxSplitBillDetailDO wxSplitBillDetailDO, SplitBillCheckDiffDO splitBillCheckDiffDO) {
        splitBillCheckDiffDO.setWxDetailId(wxSplitBillDetailDO.getId());
        SettleWxDiffReasonDTO settleWxDiffReasonDTO = new SettleWxDiffReasonDTO();

        if (Objects.nonNull(settleDetailOriginalDO)) {
            splitBillCheckDiffDO.setBizOriginalId(settleDetailOriginalDO.getId());
            if (Objects.nonNull(settleDetailOriginalDO.getTpSettleNo()) && Objects.nonNull(wxSplitBillDetailDO.getSplitDetailNo()) && !settleDetailOriginalDO.getTpSettleNo().equals(wxSplitBillDetailDO.getSplitDetailNo())) {
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitDetailNo());
                dto.setBiz(settleDetailOriginalDO.getTpSettleNo());
                settleWxDiffReasonDTO.setTpSettleNo(dto);
            }
            if (!settleDetailOriginalDO.getAmountOutput().equals(wxSplitBillDetailDO.getSplitAmt())) {
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitAmt().toString());
                dto.setBiz(settleDetailOriginalDO.getAmountOutput().toString());
                settleWxDiffReasonDTO.setAmtDiff(dto);
            }
            if (!settleDetailOriginalDO.getTpSourceId().equals(wxSplitBillDetailDO.getSplitSourceId())) {
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitSourceId());
                dto.setBiz(settleDetailOriginalDO.getTpSourceId());
                settleWxDiffReasonDTO.setSplitSource(dto);
            }
            if (!settleDetailOriginalDO.getTpReceiveId().equals(wxSplitBillDetailDO.getSplitReceiveId())) {
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitReceiveId());
                dto.setBiz(settleDetailOriginalDO.getTpReceiveId());
                settleWxDiffReasonDTO.setSplitTarget(dto);
            }
            splitBillCheckDiffDO.setDiffReason(JSONObject.toJSONString(settleWxDiffReasonDTO));
        } else {
            splitBillCheckDiffDO.setDiffReason(JSONObject.toJSONString(settleWxDiffReasonDTO));
        }
        splitBillCheckDiffDO.setCheckCount(splitBillCheckDiffDO.getCheckCount() + ONE);
        splitBillCheckDiffDO.setSettleDetailNo(wxSplitBillDetailDO.getSettleDetailNo());
        splitBillCheckDiffDO.setStatus(WxSplitDiffStatus.NOT_ACCOUNTS.getCode());
        splitBillCheckDiffDO.setDiffScene(SettleDetailScene.NOT_SCENE.getCode());
        splitBillCheckDiffDO.modify();
        return splitBillCheckDiffDO;
    }



    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(SETTLE_WX_SPLIT_DIFF_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
