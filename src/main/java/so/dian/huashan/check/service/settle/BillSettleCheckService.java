package so.dian.huashan.check.service.settle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.check.emuns.settle.*;
import so.dian.huashan.check.mapper.BillSettleCheckConsistencyMapper;
import so.dian.huashan.check.mapper.BillSettleCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO;
import so.dian.huashan.check.service.settle.entity.SettlCheckProhibitionRule;
import so.dian.huashan.collection.config.BizProperties;
import so.dian.huashan.collection.mapper.BillSettleControlOrderMapper;
import so.dian.huashan.collection.mapper.BillSettleOrderOriginalMapper;
import so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO;
import so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO;
import so.dian.huashan.collection.mapper.example.TimeRangeExample;
import so.dian.huashan.common.enums.OrderBizType;
import so.dian.huashan.common.mapper.contract.ContractShopDivideMapper;
import so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO;
import so.dian.huashan.common.mapper.ubud.BillingResultDetailMapper;
import so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO;
import so.dian.huashan.common.mapper.ubud.example.BillingResultDetailExample;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.transaction.ManualTransaction;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static so.dian.huashan.check.emuns.settle.MainBizType.BRAND_MERCHANT;
import static so.dian.huashan.check.emuns.settle.MainBizType.JOIN_MERCHANT;
import static so.dian.huashan.check.emuns.settle.MainBizType.NORMAL_MERCHANT;
import static so.dian.huashan.check.job.settle.SettleCheckJob.SETTLE_CHECK_JOB_NAME;
import static so.dian.huashan.common.enums.BizErrorCodeEnum.SETTLE_CHECK_STATUS_ILLEGAL;
import static so.dian.huashan.common.enums.BizErrorCodeEnum.SETTLE_ORDER_STATUS_IS_NULL;
import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miaoshuai
 * @create: 2023/12/15 10:26
 * @description:
 */
@Slf4j
@Service
public class BillSettleCheckService extends BillSettleBaseCheck {

    private final static String MAX_ID = "max.id";

    @Autowired
    private BillSettleOrderOriginalMapper settleOrderOriginalMapper;

    @Autowired
    private BillSettleControlOrderMapper settleControlOrderMapper;

    @Autowired
    private ContractShopDivideMapper contractShopDivideMapper;

    @Autowired
    private BillingResultDetailMapper billingResultDetailMapper;

    @Autowired
    private BillSettleCheckDiffMapper settleCheckDiffMapper;

    @Autowired
    private BillSettleCheckConsistencyMapper settleCheckConsistencyMapper;

    @Autowired
    private BillSettleCheckService self;

    @Autowired
    private BizProperties bizProperties;

    public List<String> listIds(InstallShardParam shardParam) {

        TimeRangeExample timeRange = calcTimeRange(shardParam);
        Long maxId = shardParam.getExtParam(MAX_ID);
        List<Long> ids = settleOrderOriginalMapper.selectIdByTimeRange(maxId, timeRange, shardParam.getPageSize());
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));

        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    /**
     * 增量对账入口
     * @param shardParam 任务分片执行参数
     */
    public void incrementCheck(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<BillSettleOrderOriginalDO> orderOriginalDOS = settleOrderOriginalMapper.selectByIds(ids);
        if (CollUtil.isEmpty(orderOriginalDOS))
            return;

        // 根据订单编码查询抽单池
        List<String> orderNos = orderOriginalDOS.stream().map(BillSettleOrderOriginalDO::getOrderNo).collect(Collectors.toList());
        List<BillSettleControlOrderDO> controlOrderDOS = settleControlOrderMapper.selectByOrderNos(orderNos);
        Map<String, List<BillSettleControlOrderDO>> controlOrderMap = controlOrderDOS.stream().collect(Collectors.groupingBy(this::getControlOrderKey));

        List<BillSettleOrderOriginalDO> toModifyOriginalDOS = Lists.newArrayList();
        List<BillSettleCheckDiffDO> checkDiffDOS = Lists.newArrayList();
        List<BillSettleCheckConsistencyDO> consistencyDOS = Lists.newArrayList();

        for (BillSettleOrderOriginalDO orderOriginalDO : orderOriginalDOS) {

            try {
                SettleOriginalStatus originalStatus = doCheck(orderOriginalDO, controlOrderMap);

                switch (originalStatus) {
                    case DIFF:
                        BillSettleCheckDiffDO checkDiffDO = new BillSettleCheckDiffDO();
                        checkDiffDO.setOriginalId(orderOriginalDO.getId());
                        checkDiffDO.setOrderNo(orderOriginalDO.getOrderNo());
                        checkDiffDO.setOrderStatus(orderOriginalDO.getOrderStatus());
                        checkDiffDO.setVersion(0);
                        checkDiffDO.init();
                        checkDiffDOS.add(checkDiffDO);

                        orderOriginalDO.setStatus(SettleOriginalStatus.DIFF.getCode());
                        toModifyOriginalDOS.add(orderOriginalDO);
                        break;
                    case NO_CHECK:
                        orderOriginalDO.setStatus(SettleOriginalStatus.NO_CHECK.getCode());
                        toModifyOriginalDOS.add(orderOriginalDO);
                        break;
                    case CONSISTENCY:
                        BillSettleCheckConsistencyDO consistencyDO = new BillSettleCheckConsistencyDO();
                        consistencyDO.setOriginalId(orderOriginalDO.getId());
                        consistencyDO.setOrderNo(orderOriginalDO.getOrderNo());
                        consistencyDO.setOrderStatus(orderOriginalDO.getOrderStatus());
                        consistencyDO.setCheckType(ConsistencyCheckType.INCREMENT.getCode());
                        consistencyDO.init();
                        consistencyDOS.add(consistencyDO);

                        orderOriginalDO.setStatus(SettleOriginalStatus.CONSISTENCY.getCode());
                        toModifyOriginalDOS.add(orderOriginalDO);
                        break;
                    default:
                        throw BizException.create(SETTLE_CHECK_STATUS_ILLEGAL);
                }
            }catch (Exception e) {
                log.error(BILLING_SETTLE, "清结算业务对账失败，订单[{}], 分片子任务[{}]", orderOriginalDO.getOrderNo(), shardParam.getSubTaskId());
                throw e;
            }
        }

        self.batchSave(toModifyOriginalDOS, checkDiffDOS, consistencyDOS);
    }

    /**
     * 差异对账入口
     */
    public void diffCheck() {

        log.info(BILLING_SETTLE, "开始执行差异对账");
        long pageNo = 1;
        List<BillSettleCheckDiffDO> checkDiffDOS = settleCheckDiffMapper.selectReCheck(PageRequest.of(pageNo, 500));

        int loopCount = 0;
        while (CollUtil.isNotEmpty(checkDiffDOS)) {
            if (++ loopCount == bizProperties.getSettle().getMaxLoop()) {
                log.warn(BILLING_SETTLE, "清结算差异对账while循环已经达到最高次数");
            }

            List<Long> orifinalIds = checkDiffDOS.stream().map(BillSettleCheckDiffDO::getOriginalId)
                    .collect(Collectors.toList());
            List<BillSettleOrderOriginalDO> orderOriginalDOS = settleOrderOriginalMapper.selectByIds(orifinalIds);
            if (CollUtil.isEmpty(orderOriginalDOS))
                return;

            Map<Long, BillSettleOrderOriginalDO> idAndOriginalMap = orderOriginalDOS.stream()
                    .collect(Collectors.toMap(BillSettleOrderOriginalDO::getId, Function.identity()));

            // 根据订单编码查询抽单池
            List<String> orderNos = orderOriginalDOS.stream().map(BillSettleOrderOriginalDO::getOrderNo).collect(Collectors.toList());
            List<BillSettleControlOrderDO> controlOrderDOS = settleControlOrderMapper.selectByOrderNos(orderNos);
            Map<String, List<BillSettleControlOrderDO>> controlOrderMap = controlOrderDOS.stream().collect(Collectors.groupingBy(this::getControlOrderKey));

            List<Long> toDeleteDiffs = Lists.newArrayList();
            List<Long> toAddVersionDiffs = Lists.newArrayList();
            List<BillSettleOrderOriginalDO> toUpdateOriginals = Lists.newArrayList();
            List<BillSettleCheckConsistencyDO> toSaveConsistencys = Lists.newArrayList();
            for (BillSettleCheckDiffDO checkDiffDO : checkDiffDOS) {
                BillSettleOrderOriginalDO orderOriginalDO = idAndOriginalMap.get(checkDiffDO.getOriginalId());
                if (Objects.isNull(orderOriginalDO)) {
                    log.warn(BILLING_SETTLE, "差异对账查询不到订单底表数据, 差异数据ID:[{}]", checkDiffDO.getId());
                    continue;
                }

                SettleOriginalStatus originalStatus = doCheck(orderOriginalDO, controlOrderMap);
                switch (originalStatus) {
                    case DIFF:
                        toAddVersionDiffs.add(checkDiffDO.getId());
                        break;
                    case NO_CHECK:
                        orderOriginalDO.setStatus(SettleOriginalStatus.NO_CHECK.getCode());
                        toUpdateOriginals.add(orderOriginalDO);
                        toDeleteDiffs.add(checkDiffDO.getId());
                        break;
                    case CONSISTENCY:
                        BillSettleCheckConsistencyDO consistencyDO = new BillSettleCheckConsistencyDO();
                        consistencyDO.setOriginalId(orderOriginalDO.getId());
                        consistencyDO.setOrderNo(orderOriginalDO.getOrderNo());
                        consistencyDO.setOrderStatus(orderOriginalDO.getOrderStatus());
                        consistencyDO.setCheckType(ConsistencyCheckType.DIFF.getCode());
                        toSaveConsistencys.add(consistencyDO);

                        orderOriginalDO.setStatus(SettleOriginalStatus.DIFF_CONSISTENCY.getCode());
                        toUpdateOriginals.add(orderOriginalDO);

                        toDeleteDiffs.add(checkDiffDO.getId());
                        break;
                    default:
                        throw BizException.create(SETTLE_CHECK_STATUS_ILLEGAL);
                }
            }

            self.batchSaveDiff(toDeleteDiffs, toUpdateOriginals, toSaveConsistencys, toAddVersionDiffs);

            log.info(BILLING_SETTLE, "差异对账执行完成一批");
            checkDiffDOS = settleCheckDiffMapper.selectReCheck(PageRequest.of(++ pageNo, 500));
        }
    }

    @Transactional
    public void batchSaveDiff(List<Long> toDeleteDiffs, List<BillSettleOrderOriginalDO> toUpdateOriginals, List<BillSettleCheckConsistencyDO> toSaveConsistencys, List<Long> toAddVersionDiffs) {
        if (CollUtil.isNotEmpty(toDeleteDiffs))
            settleCheckDiffMapper.batchDeleteByIds(toDeleteDiffs);

        if (CollUtil.isNotEmpty(toAddVersionDiffs))
            settleCheckDiffMapper.batchAddVersion(toAddVersionDiffs);

        if (CollUtil.isNotEmpty(toUpdateOriginals))
            settleOrderOriginalMapper.batchUpdate(toUpdateOriginals);

        if (CollUtil.isNotEmpty(toSaveConsistencys))
            settleCheckConsistencyMapper.batchInsert(toSaveConsistencys);
    }

    @Transactional
    public void batchSave(List<BillSettleOrderOriginalDO> orderOriginalDOS, List<BillSettleCheckDiffDO> checkDiffDOS, List<BillSettleCheckConsistencyDO> consistencyDOS) {
        if (CollUtil.isNotEmpty(orderOriginalDOS))
            settleOrderOriginalMapper.batchUpdate(orderOriginalDOS);

        if (CollUtil.isNotEmpty(checkDiffDOS))
            settleCheckDiffMapper.batchInsert(checkDiffDOS);

        if (CollUtil.isNotEmpty(consistencyDOS))
            settleCheckConsistencyMapper.batchInsert(consistencyDOS);
    }

    /**
     * 手动平账
     * @param diffIds 清结算差异表ID集合
     */
    public void manualConsistency(List<Long> diffIds) {
        if (CollUtil.isEmpty(diffIds))
            return;

        List<BillSettleCheckDiffDO> checkDiffDOS = settleCheckDiffMapper.selectByIds(diffIds);
        if (CollUtil.isEmpty(checkDiffDOS))
            return;

        List<BillSettleOrderOriginalDO> toUpdateOriginals = Lists.newArrayList();
        List<BillSettleCheckConsistencyDO> toSaveConsistencys = Lists.newArrayList();
        for (BillSettleCheckDiffDO checkDiffDO : checkDiffDOS) {
            BillSettleCheckConsistencyDO consistencyDO = new BillSettleCheckConsistencyDO();
            consistencyDO.setOriginalId(checkDiffDO.getOriginalId());
            consistencyDO.setOrderNo(checkDiffDO.getOrderNo());
            consistencyDO.setOrderStatus(checkDiffDO.getOrderStatus());
            consistencyDO.setCheckType(ConsistencyCheckType.DIFF.getCode());
            toSaveConsistencys.add(consistencyDO);

            BillSettleOrderOriginalDO orderOriginalDO = new BillSettleOrderOriginalDO();
            orderOriginalDO.setId(checkDiffDO.getOriginalId());
            orderOriginalDO.setStatus(SettleOriginalStatus.DIFF_CONSISTENCY.getCode());
            toUpdateOriginals.add(orderOriginalDO);
        }

        ManualTransaction transaction = ManualTransaction.builder().build();
        transaction.invoke(r -> {
            if (CollUtil.isNotEmpty(diffIds))
                settleCheckDiffMapper.batchDeleteByIds(diffIds);

            if (CollUtil.isNotEmpty(toUpdateOriginals))
                settleOrderOriginalMapper.batchUpdate(toUpdateOriginals);

            if (CollUtil.isNotEmpty(toSaveConsistencys))
                settleCheckConsistencyMapper.batchInsert(toSaveConsistencys);
        });
    }

    private SettleOriginalStatus doCheck(BillSettleOrderOriginalDO orderOriginalDO, Map<String, List<BillSettleControlOrderDO>> controlOrderMap) {

        // 根据门店查询有效的分成主数据
        List<ContractShopDivideDO> shopDivideDOS = contractShopDivideMapper.selectUsable(orderOriginalDO.getLoanShopId(), orderOriginalDO.getPaySuccessTime().getTime());
        if (CollUtil.isEmpty(shopDivideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        if (Objects.nonNull(bizProperties.getSettle().getPaySuccessTime()) && Objects.nonNull(orderOriginalDO.getPaySuccessTime())) {
            DateTime paySuccessTimeEnd = new DateTime(bizProperties.getSettle().getPaySuccessTime());
            if (paySuccessTimeEnd.after(orderOriginalDO.getPaySuccessTime()))
                return SettleOriginalStatus.NO_CHECK;
        }

        if(orderOriginalDO.getBizType().equals(OrderBizType.ORDER_BOX_ORDER.getKey()) ){
            SettlCheckProhibitionRule prohibitionRule = new SettlCheckProhibitionRule(orderOriginalDO);
            List<MainBizType> checkRatioTypes = Lists.newArrayList(NORMAL_MERCHANT, BRAND_MERCHANT, JOIN_MERCHANT);
            shopDivideDOS = shopDivideDOS.stream()
                    .filter(shopDivideDO -> {
                        MainBizType mainBizType = MainBizType.from(shopDivideDO.getMainBizType());
                        if (checkRatioTypes.stream().anyMatch(type -> type.equals(mainBizType))) {
                            if (Objects.nonNull(shopDivideDO.getRatio()) && shopDivideDO.getRatio() > 0) {
                                return Boolean.TRUE;
                            }else {
                                return Boolean.FALSE;
                            }
                        }
                        return Boolean.TRUE;
                    })
                    .filter(shopDivideDO -> { // 抽单池判断
                        BillSettleControlOrderDO tmpControl = new BillSettleControlOrderDO();
                        tmpControl.setOrderNo(orderOriginalDO.getOrderNo());
                        tmpControl.setMainBizId(shopDivideDO.getMainBizId());
                        tmpControl.setMainBizType(shopDivideDO.getMainBizType().intValue());
                        tmpControl.setSettleSubjectType(shopDivideDO.getSettleSubjectType().intValue());
                        tmpControl.setSettleSubjectId(shopDivideDO.getSettleSubjectId());
                        List<BillSettleControlOrderDO> settleControlOrderDOS = controlOrderMap.get(getControlOrderKey(tmpControl));
                        return CollUtil.isEmpty(settleControlOrderDOS);
                    })
                    .filter(shopDivideDO ->!prohibitionRule.isHit(shopDivideDO)) // 禁入规则判断
                    .collect(Collectors.toList());
        }else if(orderOriginalDO.getBizType().equals(OrderBizType.TIMES_CARD_ORDER.getKey())) {
            shopDivideDOS = shopDivideDOS.stream().filter(o->o.getSettleSubjectType().equals(SettleSubjectType.XIAODIAN.getCode()) && (o.getMainBizType().equals(MainBizType.AGENT.getCode()) || o.getMainBizType().equals(MainBizType.JV_COMPANY.getCode()))).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(shopDivideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        SettleOrderStatus orderStatus = SettleOrderStatus.from(orderOriginalDO.getOrderStatus());
        if (Objects.isNull(orderStatus)) {
            throw BizException.create(SETTLE_ORDER_STATUS_IS_NULL);
        }

        for (ContractShopDivideDO shopDivideDO : shopDivideDOS) {
            BillingResultDetailExample detailExample = BillingResultDetailExample.builder()
                    .bizNo(orderOriginalDO.getOrderNo())
                    .settleTargetId(shopDivideDO.getMainBizId().toString())
                    .settleTargetType(shopDivideDO.getMainBizType().toString())
                    .tradeType(orderStatus.equals(SettleOrderStatus.PAY) || orderStatus.equals(SettleOrderStatus.CARD_PAY)? TradeType.PAYMENT.getCode() : TradeType.REFUND.getCode())
                    .build();
            List<BillingResultDetailDO> resultDetailDOS = billingResultDetailMapper.selectByExample(detailExample);
            if (CollUtil.isEmpty(resultDetailDOS)) {
                return SettleOriginalStatus.DIFF;
            }
        }

        return SettleOriginalStatus.CONSISTENCY;
    }

    private String getControlOrderKey(BillSettleControlOrderDO controlOrderDO) {
        return StrUtil.join(StrUtil.DOT,
                controlOrderDO.getOrderNo(),
                controlOrderDO.getMainBizType(),
                controlOrderDO.getMainBizId(),
                controlOrderDO.getSettleSubjectType(),
                controlOrderDO.getSettleSubjectId());
    }

    public void dataDelete() {
        DateTime dateTime = DateUtil.offsetDay(new Date(), -3);
        dateTime = DateUtil.beginOfDay(dateTime);
        Long originalmaxId = settleOrderOriginalMapper.selectMaxIdByGmtCreate(dateTime.getTime());
        Long consistencyMaxId = settleCheckConsistencyMapper.selectMaxIdByGmtCreate(dateTime.getTime());

        int loopCount = 0;
        Integer deleteSize = Optional.ofNullable(bizProperties.getSettle().getDeleteSize()).orElse(100000);
        while (Objects.nonNull(originalmaxId) || Objects.nonNull(consistencyMaxId)) {
            if (++ loopCount == bizProperties.getSettle().getMaxLoop()) {
                log.warn(BILLING_SETTLE, "清结算数据归档while循环已经达到最高次数");
                return;
            }

            if (Objects.nonNull(originalmaxId)) {
                int result = settleOrderOriginalMapper.deleteByGmtCreateAndMaxId(dateTime.getTime(), originalmaxId, deleteSize);
                log.info(BILLING_SETTLE, "原始底表删除完成一批, gmtCreate={}, maxId={}, result={}", dateTime.getTime(), originalmaxId, result);
                originalmaxId = settleOrderOriginalMapper.selectMaxIdByGmtCreate(dateTime.getTime());
            }


            if (Objects.nonNull(consistencyMaxId)) {
                int result = settleCheckConsistencyMapper.deleteByGmtCreateAndMaxId(dateTime.getTime(), consistencyMaxId, deleteSize);
                log.info(BILLING_SETTLE, "对账一致表删除完成一批, gmtCreate={}, maxId={}, result={}", dateTime.getTime(), consistencyMaxId, result);
                consistencyMaxId = settleCheckConsistencyMapper.selectMaxIdByGmtCreate(dateTime.getTime());
            }

            ThreadUtil.sleep(2, TimeUnit.SECONDS);
        }
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(SETTLE_CHECK_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::incrementCheck;
    }
}
