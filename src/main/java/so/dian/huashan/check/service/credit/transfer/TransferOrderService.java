package so.dian.huashan.check.service.credit.transfer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.common.CreditCheckCommon;
import so.dian.huashan.check.emuns.CheckTypeEnum;
import so.dian.huashan.check.job.credit.transfer.TransferOrderJob;
import so.dian.huashan.check.manage.CreditCheckManager;
import so.dian.huashan.check.manage.dto.CreditCheckDTO;
import so.dian.huashan.common.mapper.supplychain.TransferOrderMapper;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@Slf4j
@Service
public class TransferOrderService extends ShardParamRecordService {

    @Autowired
    private TransferOrderMapper transferOrderMapper;
    @Autowired
    private CreditCheckManager creditCheckManager;
    @Autowired
    private CreditCheckCommon creditCheckCommon;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private final static String MAX_ID = "max.id";
    public List<String> listIds(InstallShardParam shardParam) {
        log.info("TransferOrderService get listIds shardParam:{}",shardParam);
        String checkDate = shardParam.getJobParam().getExtParam(TransferOrderJob.CHECK_DATE_TIME);
        if (StringUtils.isEmpty(checkDate)) {
            shardParam.putExtParam(TransferOrderJob.CHECK_DATE_TIME, LocalDateUtils.format(LocalDateUtils.yesterday(), NORM_DATETIME_PATTERN));
            checkDate = LocalDateUtils.format(LocalDateUtils.yesterday(), NORM_DATETIME_PATTERN);
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Date startTime = DateUtil.beginOfDay(checkDateTime);

        Date endTime = DateUtil.endOfDay(checkDateTime);
        log.info("TransferOrderService get listIds startTime:{},endTime:{}",startTime,endTime);
        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        transferOrderMapper.selectByCheckDateTime(startTime, endTime, shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    /**
     * 对比
     */
    public void check(ExecuteShardParam shardParam) {
        log.info("TransferOrderService get shardParam:{}",shardParam);
        CreditCheckDTO creditCheckDTO = creditCheckCommon.transferOrderCheck(shardParam.bizIdToLong(),CheckTypeEnum.INCREMENT.getCode(),shardParam.getBatchNo());
        if (Objects.isNull(creditCheckDTO)) {
            return;
        }
        // 批量添加
        creditCheckManager.batchSaveAndDelete(Collections.singletonList(creditCheckDTO));
    }


    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(TransferOrderJob.CREDIT_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
