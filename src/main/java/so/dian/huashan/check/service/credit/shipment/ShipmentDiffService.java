package so.dian.huashan.check.service.credit.shipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.common.CreditCheckCommon;
import so.dian.huashan.check.emuns.CheckTypeEnum;
import so.dian.huashan.check.job.credit.shipment.ShipmentDiffCheckJob;
import so.dian.huashan.check.manage.CreditCheckManager;
import so.dian.huashan.check.manage.dto.CreditCheckDTO;
import so.dian.huashan.check.mapper.CreditCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.CreditCheckDiffDO;
import so.dian.huashan.common.enums.CreditBizTypeEnum;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@Service
@Slf4j
public class ShipmentDiffService extends ShardParamRecordService {

    @Autowired
    private CreditCheckDiffMapper creditCheckDiffMapper;
    private final static String MAX_ID = "max.id";
    @Autowired
    private CreditCheckCommon creditCheckCommon;
    @Autowired
    private CreditCheckManager creditCheckManager;

    public List<String> listIds(InstallShardParam shardParam) {
        log.info("ShipmentDiffService get listIds shardParam:{}",shardParam);
        String checkDate = shardParam.getJobParam().getExtParam(ShipmentDiffCheckJob.CHECK_DATE_TIME);

        if (StringUtils.isEmpty(checkDate)) {
            shardParam.putExtParam(ShipmentDiffCheckJob.CHECK_DATE_TIME, LocalDateUtils.format(LocalDateUtils.yesterday(), NORM_DATETIME_PATTERN));
        }
        String batchNo = creditCheckDiffMapper.selectMaxBatchNo();
        if (StringUtils.isEmpty(batchNo)){
            return Collections.emptyList();
        }
        log.info("ShipmentDiffService get listIds batchNo:{}",batchNo);
        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        creditCheckDiffMapper.selectByCheckBatchNo(batchNo, shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {
        log.info("ShipmentDiffService get shardParam:{}",shardParam);
        List<CreditCheckDiffDO> creditCheckDiffDOList = creditCheckDiffMapper.selectByIds(shardParam.bizIdToLong());
        if (CollectionUtil.isEmpty(creditCheckDiffDOList)){
            return;
        }
        // 对数据进行分组
        Set<Long> tradeSetIds = new HashSet<>();
        Set<Long> orderSetIds = new HashSet<>();
        Set<Long> transferSetIds = new HashSet<>();
        for (CreditCheckDiffDO creditCheckDiffDO : creditCheckDiffDOList){
            CreditBizTypeEnum creditBizTypeEnum = CreditBizTypeEnum.transferType(creditCheckDiffDO.getBizType());
            if (Objects.isNull(creditBizTypeEnum)){
                return;
            }
            switch (creditBizTypeEnum) {
                case TRADE:
                    tradeSetIds.add(creditCheckDiffDO.getBizId());
                    break;
                case ORDER:
                    orderSetIds.add(creditCheckDiffDO.getBizId());
                    break;
                case TRANSFER_ORDER:
                    transferSetIds.add(creditCheckDiffDO.getBizId());
                    break;
                default:
                    break;
            }
        }
        List<CreditCheckDTO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(tradeSetIds)){
            CreditCheckDTO creditCheckDTO = creditCheckCommon.shipmentTradeCheck(new ArrayList<>(tradeSetIds), CheckTypeEnum.DIFF.getCode(),shardParam.getBatchNo());
            if (Objects.nonNull(creditCheckDTO)){
                list.add(creditCheckDTO);
            }
        }
        if (CollectionUtil.isNotEmpty(orderSetIds)){
            CreditCheckDTO creditCheckDTO = creditCheckCommon.shipmentTradeCheckBySubOrder(new ArrayList<>(orderSetIds), CheckTypeEnum.DIFF.getCode(),shardParam.getBatchNo());
            if (Objects.nonNull(creditCheckDTO)){
                list.add(creditCheckDTO);
            }
        }
        if (CollectionUtil.isNotEmpty(transferSetIds)){
            CreditCheckDTO creditCheckDTO = creditCheckCommon.transferOrderCheck(new ArrayList<>(transferSetIds), CheckTypeEnum.DIFF.getCode(),shardParam.getBatchNo());
            if (Objects.nonNull(creditCheckDTO)){
                list.add(creditCheckDTO);
            }
        }
        creditCheckManager.diffBatchSaveAndDelete(list,creditCheckDiffDOList.stream().map(CreditCheckDiffDO::getId).collect(Collectors.toList()));
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(ShipmentDiffCheckJob.CREDIT_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
