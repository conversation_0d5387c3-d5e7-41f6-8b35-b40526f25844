package so.dian.huashan.check.service.income;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.check.handler.impl.HuazhuBillHandler;
import so.dian.huashan.check.job.income.ChannelIncomeCheckJob;
import so.dian.huashan.collection.mapper.HuazhuIncomeOriginalInfoMapper;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static so.dian.huashan.check.job.income.HuazhuCheckJob.HUAZHU_CHECK_JOB;

/**
 * @author: miaoshuai
 * @create: 2023/12/26 16:53
 * @description:
 */
@Service
public class HuazhuCheckService extends ShardParamRecordService {

    private final static String MAX_ID = "max.id";

    @Autowired
    private HuazhuIncomeOriginalInfoMapper originalInfoMapper;

    @Autowired
    private HuazhuBillHandler huazhuBillHandler;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDateStr = shardParam.getJobParam().getExtParam(ChannelIncomeCheckJob.CHECK_DATE);
        DateTime checkDate = DateUtil.format2DateYyyyMMdd(Integer.parseInt(checkDateStr));
        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        originalInfoMapper.selectIdForShard(checkDate, shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {
        AbstractCheckContext checkContext = AbstractCheckContext.builder()
                .taskMasterId(shardParam.getTaskMasterId())
                .taskSubId(shardParam.getSubTaskId())
                .voucher(JSON.toJSONString(shardParam.bizIdToLong()))
                .build();
        huazhuBillHandler.executeBiz(checkContext);
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(HUAZHU_CHECK_JOB);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
