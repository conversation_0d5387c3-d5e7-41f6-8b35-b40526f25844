package so.dian.huashan.check.service.settle;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.check.emuns.settle.CheckDiffCode;
import so.dian.huashan.check.emuns.settle.ControlConsistencyStatus;
import so.dian.huashan.check.manage.ControlOrderCheckDiffManage;
import so.dian.huashan.check.mapper.ControlOrderCheckConsistencyMapper;
import so.dian.huashan.check.mapper.ControlOrderCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckDiffDO;
import so.dian.huashan.collection.config.BizProperties;
import so.dian.huashan.collection.mapper.BillSettleControlOrderMapper;
import so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO;
import so.dian.huashan.collection.mapper.example.TimeRangeExample;
import so.dian.huashan.common.mapper.polar.OrdersWideTableMapper;
import so.dian.huashan.common.service.entity.ControlOrderBO;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static so.dian.huashan.check.job.settle.ControlOrderMarkCheckJob.SETTLE_CONTROL_CHECK_JOB_NAME;
import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

@Slf4j
@Service
public class ControlOrderCheckService extends BillSettleBaseCheck {

    private final static String MAX_ID = "max.id";

    @Autowired
    private BillSettleControlOrderMapper controlOrderMapper;

    @Autowired
    private OrdersWideTableMapper ordersWideTableMapper;

    @Autowired
    private ControlOrderCheckDiffMapper checkDiffMapper;

    @Autowired
    private ControlOrderCheckConsistencyMapper checkConsistencyMapper;

    @Autowired
    private ControlOrderCheckDiffManage orderCheckDiffManage;

    @Autowired
    private BizProperties bizProperties;

    @Autowired
    private ControlOrderCheckService self;

    public List<String> listIds(InstallShardParam shardParam) {

        TimeRangeExample timeRange = calcTimeRange(shardParam);
        Long maxId = shardParam.getExtParam(MAX_ID);
        List<Long> ids = controlOrderMapper.selectIdsForShard(maxId, timeRange, shardParam.getPageSize());
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));

        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void incrementCheck(ExecuteShardParam shardParam) {
        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<BillSettleControlOrderDO> controlOrderDOS = controlOrderMapper.selectByIds(ids);
        if (CollUtil.isEmpty(controlOrderDOS))
            return;

        List<String> orderNos = controlOrderDOS.stream().map(BillSettleControlOrderDO::getOrderNo).collect(Collectors.toList());
        List<ControlOrderBO> controlOrderBOS = ordersWideTableMapper.selectByOrderNos(orderNos);
        Map<String, List<ControlOrderBO>> orderNoAndControlMap = controlOrderBOS.stream().collect(Collectors.groupingBy(ControlOrderBO::getOrderNo));

        List<ControlOrderCheckDiffDO> checkDiffDOS = checkDiffMapper.selectByControlOrderIds(ids);
        Map<Long, ControlOrderCheckDiffDO> controlOrderIdAndDiffMap = checkDiffDOS.stream().collect(Collectors.toMap(ControlOrderCheckDiffDO::getControlOrderId, Function.identity()));

        List<ControlOrderCheckConsistencyDO> consistencyDOS = checkConsistencyMapper.selectByControlOrderIds(ids);
        Map<Long, ControlOrderCheckConsistencyDO> coontrolOrderIdMap = consistencyDOS.stream().collect(Collectors.toMap(ControlOrderCheckConsistencyDO::getControlOrderId, Function.identity()));

        List<ControlOrderCheckDiffDO> toWriteDiffs = Lists.newArrayList();
        List<ControlOrderCheckConsistencyDO> consistencyForSave = Lists.newArrayList();
        List<Long> removeOldConsistencyIds = Lists.newArrayList();

        for (BillSettleControlOrderDO controlOrderDO : controlOrderDOS) {
            try {
                ControlOrderCheckDiffDO checkDiffDO = controlOrderIdAndDiffMap.get(controlOrderDO.getId());
                List<ControlOrderBO> checkControls = orderNoAndControlMap.get(controlOrderDO.getOrderNo());
                if (CollUtil.isEmpty(checkControls)) {
                    toWriteDiffs.addAll(buildDiff(controlOrderDO, checkDiffDO, CheckDiffCode.CONTROL_ES_NOT_EXIST));
                    continue;
                }

                if (checkControls.size() > 1) {
                    toWriteDiffs.addAll(buildDiff(controlOrderDO, checkDiffDO, CheckDiffCode.CONTROL_ES_MULTI));
                    continue;
                }

                ControlOrderBO controlOrderBO = CollUtil.getFirst(checkControls);
                if (!Objects.equals(controlOrderDO.getControlStatus(), controlOrderBO.getControlResult())) {
                    toWriteDiffs.addAll(buildDiff(controlOrderDO, checkDiffDO, CheckDiffCode.CONTROL_STATUS_NOT_SAME));
                    continue;
                }

                ControlOrderCheckConsistencyDO consistencyDO = coontrolOrderIdMap.get(controlOrderDO.getId());
                if (Objects.nonNull(consistencyDO))
                    removeOldConsistencyIds.add(consistencyDO.getId());

                consistencyForSave.add(buillConsistency(controlOrderDO));
            }catch (Exception e) {
                log.error(BILLING_SETTLE, "清结算抽单标识对账异常，异常的抽单池ID:{}", controlOrderDO.getId(), e);
                throw e;
            }
        }

        self.saveWithTx(toWriteDiffs, consistencyForSave, removeOldConsistencyIds);
    }

    @Transactional
    public void saveWithTx(List<ControlOrderCheckDiffDO> checkDiffDOS,
                           List<ControlOrderCheckConsistencyDO> consistencyDOS,
                           List<Long> consistencyIds) {
        if (CollUtil.isNotEmpty(checkDiffDOS))
            orderCheckDiffManage.batchAddOrModify(checkDiffDOS);

        if (CollUtil.isNotEmpty(consistencyDOS))
            checkConsistencyMapper.batchInsert(consistencyDOS);

        if (CollUtil.isNotEmpty(consistencyIds))
            checkConsistencyMapper.batchLogicDelete(consistencyIds);
    }

    private ControlOrderCheckConsistencyDO buillConsistency(BillSettleControlOrderDO controlOrderDO) {
        ControlOrderCheckConsistencyDO consistencyDO = new ControlOrderCheckConsistencyDO();
        consistencyDO.setStatus(ControlConsistencyStatus.INCREMENT_CONSISTENCY.getCode());
        consistencyDO.setControlOrderId(controlOrderDO.getId());
        consistencyDO.setOrderNo(controlOrderDO.getOrderNo());
        consistencyDO.setControlStatus(controlOrderDO.getControlStatus());
        consistencyDO.init();
        return consistencyDO;
    }

    private List<ControlOrderCheckDiffDO> buildDiff(BillSettleControlOrderDO controlOrderDO, ControlOrderCheckDiffDO orderCheckDiffDO, CheckDiffCode diffCode) {

        List<ControlOrderCheckDiffDO> checkDiffDOS = Lists.newArrayList();
        ControlOrderCheckDiffDO checkDiffDO = new ControlOrderCheckDiffDO();
        checkDiffDO.setControlOrderId(controlOrderDO.getId());
        checkDiffDO.setOrderNo(controlOrderDO.getOrderNo());
        checkDiffDO.setDiffCode(diffCode.getCode());
        checkDiffDO.setRetryCount(0);
        checkDiffDO.init();
        checkDiffDOS.add(checkDiffDO);

        if (Objects.nonNull(orderCheckDiffDO)) {
            ControlOrderCheckDiffDO toDelete = new ControlOrderCheckDiffDO();
            toDelete.setId(orderCheckDiffDO.getId());
            checkDiffDOS.add(toDelete);
        }
        return checkDiffDOS;
    }


    public void diffCheck() {
        log.info(BILLING_SETTLE, "开始清结算抽单标识差异对账");

        long pageNo = 1;
        int loopCount = 0;

        List<ControlOrderCheckDiffDO> checkDiffDOS = checkDiffMapper.selectReCheck(PageRequest.of(pageNo, 500));
        while (CollUtil.isNotEmpty(checkDiffDOS)) {
            if (++ loopCount == bizProperties.getSettle().getMaxLoop()) {
                log.warn(BILLING_SETTLE, "c清结算抽单标识差异对账while循环已经达到最高次数");
            }

            List<String> orderNos = checkDiffDOS.stream().map(ControlOrderCheckDiffDO::getOrderNo).collect(Collectors.toList());
            List<ControlOrderBO> controlOrderBOS = ordersWideTableMapper.selectByOrderNos(orderNos);
            Map<String, List<ControlOrderBO>> orderNoAndControlMap = controlOrderBOS.stream().collect(Collectors.groupingBy(ControlOrderBO::getOrderNo));

            List<Long> controlOrderIds = checkDiffDOS.stream().map(ControlOrderCheckDiffDO::getControlOrderId).collect(Collectors.toList());
            List<BillSettleControlOrderDO> settleControlOrderDOS = controlOrderMapper.selectByIds(controlOrderIds);
            Map<Long, BillSettleControlOrderDO> idAndOrderMap = settleControlOrderDOS.stream().collect(Collectors.toMap(BillSettleControlOrderDO::getId, Function.identity()));

            List<ControlOrderCheckConsistencyDO> consistencyDOS = checkConsistencyMapper.selectByControlOrderIds(controlOrderIds);
            Map<Long, ControlOrderCheckConsistencyDO> controlOrderIdMap = consistencyDOS.stream().collect(Collectors.toMap(ControlOrderCheckConsistencyDO::getControlOrderId, Function.identity()));

            List<ControlOrderCheckDiffDO> toModifys = Lists.newArrayList();
            List<Long> toRemoveIds = Lists.newArrayList();
            List<Long> removeConsistencyIds = Lists.newArrayList();
            List<ControlOrderCheckConsistencyDO> consistencyToAdds = Lists.newArrayList();

            for (ControlOrderCheckDiffDO checkDiffDO : checkDiffDOS) {
                try {
                    List<ControlOrderBO> checkControls = orderNoAndControlMap.get(checkDiffDO.getOrderNo());
                    if (CollUtil.isEmpty(checkControls)) {
                        toModifys.add(rebuildDiff(checkDiffDO, CheckDiffCode.CONTROL_ES_NOT_EXIST));
                        continue;
                    }

                    if (checkControls.size() > 1) {
                        toModifys.add(rebuildDiff(checkDiffDO, CheckDiffCode.CONTROL_ES_MULTI));
                        continue;
                    }

                    ControlOrderBO controlOrderBO = CollUtil.getFirst(checkControls);
                    BillSettleControlOrderDO controlOrderDO = idAndOrderMap.get(checkDiffDO.getControlOrderId());
                    if (!Objects.equals(controlOrderDO.getControlStatus(), controlOrderBO.getControlResult())) {
                        toModifys.add(rebuildDiff(checkDiffDO, CheckDiffCode.CONTROL_STATUS_NOT_SAME));
                    }else {
                        toRemoveIds.add(checkDiffDO.getId());
                        ControlOrderCheckConsistencyDO consistencyDO = controlOrderIdMap.get(checkDiffDO.getControlOrderId());
                        if (Objects.nonNull(consistencyDO)) {
                            removeConsistencyIds.add(consistencyDO.getId());
                        }
                        consistencyToAdds.add(rebuillConsistency(checkDiffDO, controlOrderDO));
                    }
                }catch (Exception e) {
                    log.error(BILLING_SETTLE, "清结算抽单标识差异对账异常, 差异表ID:{}", checkDiffDO.getId(), e);
                    throw e;
                }
            }

            self.batchModifyDiff(toModifys, toRemoveIds, removeConsistencyIds, consistencyToAdds);
            log.info(BILLING_SETTLE, "抽单池差异对账执行完成一批");
            checkDiffDOS = checkDiffMapper.selectReCheck(PageRequest.of(++ pageNo, 500));
        }

        log.info("抽单池差异对账执行完成");
    }

    private ControlOrderCheckDiffDO rebuildDiff(ControlOrderCheckDiffDO checkDiffDO, CheckDiffCode diffCode) {

        ControlOrderCheckDiffDO againDiff = new ControlOrderCheckDiffDO();
        againDiff.setId(checkDiffDO.getId());
        againDiff.setRetryCount(checkDiffDO.getRetryCount() + 1);
        againDiff.setDiffCode(diffCode.getCode());
        againDiff.modify();
        return againDiff;
    }

    private ControlOrderCheckConsistencyDO rebuillConsistency(ControlOrderCheckDiffDO checkDiffDO, BillSettleControlOrderDO controlOrderDO) {
        ControlOrderCheckConsistencyDO consistencyDO = new ControlOrderCheckConsistencyDO();
        consistencyDO.setStatus(ControlConsistencyStatus.DIFF_CONSISTENCY.getCode());
        consistencyDO.setControlOrderId(checkDiffDO.getControlOrderId());
        consistencyDO.setOrderNo(checkDiffDO.getOrderNo());
        consistencyDO.setControlStatus(controlOrderDO.getControlStatus());
        consistencyDO.init();
        return consistencyDO;
    }

    @Transactional
    public void batchModifyDiff(List<ControlOrderCheckDiffDO> toModifys,
                                List<Long> toRemoveIds,
                                List<Long> removeConsistencyIds,
                                List<ControlOrderCheckConsistencyDO> consistencyToAdds) {
        if (CollUtil.isNotEmpty(toModifys))
            checkDiffMapper.batchUpdate(toModifys);

        if (CollUtil.isNotEmpty(toRemoveIds))
            checkDiffMapper.batchLogicDeleteByIds(toRemoveIds, 1);

        if (CollUtil.isNotEmpty(removeConsistencyIds))
            checkConsistencyMapper.batchLogicDelete(removeConsistencyIds);

        if (CollUtil.isNotEmpty(consistencyToAdds))
            checkConsistencyMapper.batchInsert(consistencyToAdds);
    }

    public void manualConsistency(List<Long> diffIds) {
        if (CollUtil.isEmpty(diffIds))
            return;

        checkDiffMapper.batchDeleteByIds(diffIds);
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(SETTLE_CONTROL_CHECK_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::incrementCheck;
    }
}
