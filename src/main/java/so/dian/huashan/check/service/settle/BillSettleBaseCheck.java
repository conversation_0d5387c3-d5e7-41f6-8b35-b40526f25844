package so.dian.huashan.check.service.settle;

import cn.hutool.core.date.DateTime;
import so.dian.huashan.collection.mapper.example.TimeRangeExample;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.InstallShardParam;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

public abstract class BillSettleBaseCheck extends ShardParamRecordService {

    private final static String CURRENT_TIME = "currentTime";

    private final static String START_TIME = "startTime";
    private final static String END_TIME = "endTime";

    protected TimeRangeExample calcTimeRange(InstallShardParam shardParam) {

        Date current = shardParam.getExtParam(CURRENT_TIME);
        if (Objects.isNull(current)) {
            current = DateUtil.getNow();
            shardParam.putExtParam(CURRENT_TIME, current);
        }

        DateTime lastTime = DateUtil.offsetHour(current, -2);
        DateTime start = DateUtil.beginOfHour(lastTime);
        DateTime end = DateUtil.endOfHour(lastTime);

        Long userStartTime = (Long) shardParam.getJobParam().getExtParam().get(START_TIME);
        Long userEndTime = (Long) shardParam.getJobParam().getExtParam().get(END_TIME);
        DateTime userStart = Optional.ofNullable(userStartTime).map(DateTime::of).orElse(null);
        DateTime userEnd = Optional.ofNullable(userEndTime).map(DateTime::of).orElse(null);
        if (Objects.nonNull(userStart) && Objects.nonNull(userEnd)) {
            start = userStart;
            end = userEnd;
        }

        return new TimeRangeExample(start, end);
    }
}
