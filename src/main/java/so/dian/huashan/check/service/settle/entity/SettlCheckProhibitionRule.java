package so.dian.huashan.check.service.settle.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import so.dian.huashan.check.emuns.settle.MainBizType;
import so.dian.huashan.check.emuns.settle.SettleSubjectType;
import so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO;
import so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 16:43
 * @description:
 */
public class SettlCheckProhibitionRule {

    private final static Long COST_PRICE = 7000L; // 成本价70元
    private final static Long TEN_CENT = 10L; // 10分钱
    private final static Long DEFAULT_MAX_COST = 99_00L; // 默认封顶价99元

    private final List<SettleSubjectType> settleSubjectTypeRule1 = Lists.newArrayList(SettleSubjectType.XIAODIAN);
    private final List<MainBizType> mainBizTypeRule1 = Lists.newArrayList(MainBizType.JV_COMPANY, MainBizType.AGENT);

    private final List<SettleSubjectType> settleSubjectTypeRule2 = Lists.newArrayList(SettleSubjectType.JV_COMPANY, SettleSubjectType.AGENT);
    private final List<MainBizType> mainBizTypeRule2 = Lists.newArrayList(MainBizType.AGENT);

    private final List<SettleSubjectType> settleSubjectTypeRule3 = Lists.newArrayList(SettleSubjectType.XIAODIAN, SettleSubjectType.AGENT, SettleSubjectType.JV_COMPANY);
    private final List<MainBizType> mainBizTypeRule3 = Lists.newArrayList(MainBizType.NORMAL_MERCHANT, MainBizType.BRAND_MERCHANT, MainBizType.JOIN_MERCHANT);

    private final BillSettleOrderOriginalDO orderOriginalDO;

    public SettlCheckProhibitionRule(BillSettleOrderOriginalDO orderOriginalDO) {
        this.orderOriginalDO = Objects.requireNonNull(orderOriginalDO, "orderOriginalDO must not null");
    }

    public boolean isHit(ContractShopDivideDO shopDivideDO) {
        if (Objects.isNull(shopDivideDO))
            return Boolean.FALSE;

        SettleSubjectType settleSubjectType = SettleSubjectType.from(shopDivideDO.getSettleSubjectType());
        MainBizType mainBizType = MainBizType.from(shopDivideDO.getMainBizType());

        if (StrUtil.isBlank(orderOriginalDO.getReturnBoxNo())
                && orderOriginalDO.getPayAmount() <= COST_PRICE) {

            if (settleSubjectTypeRule1.stream().anyMatch(settleSubjectType1 -> settleSubjectType1.equals(settleSubjectType)) &&
                    mainBizTypeRule1.stream().anyMatch(mainBizType1 -> mainBizType1.equals(mainBizType)))
                return Boolean.TRUE;

            if (settleSubjectTypeRule2.stream().anyMatch(settleSubjectType2 -> settleSubjectType2.equals(settleSubjectType)) &&
                    mainBizTypeRule2.stream().anyMatch(mainBizType2 -> mainBizType2.equals(mainBizType)))
                return Boolean.TRUE;
        }

        if (StrUtil.isBlank(orderOriginalDO.getReturnBoxNo())
                || orderOriginalDO.getOrderAmount() >= getMaxPrice()
                || orderOriginalDO.getPayAmount() <= TEN_CENT) {

            if (settleSubjectTypeRule3.stream().anyMatch(settleSubjectType3 -> settleSubjectType3.equals(settleSubjectType)) &&
                    mainBizTypeRule3.stream().anyMatch(mainBizType3 -> mainBizType3.equals(mainBizType)))
                return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    private Long getMaxPrice() {
        JSONObject loanPriceInfo = JSON.parseObject(orderOriginalDO.getLoanPriceInfo());
        if (Objects.isNull(loanPriceInfo))
            return DEFAULT_MAX_COST;

        return Optional.ofNullable(loanPriceInfo.getLong("maxCost")).orElse(DEFAULT_MAX_COST);
    }
}
