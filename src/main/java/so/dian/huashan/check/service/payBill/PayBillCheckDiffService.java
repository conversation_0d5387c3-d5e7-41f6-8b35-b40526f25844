package so.dian.huashan.check.service.payBill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.emuns.payBill.PayBillCheckType;
import so.dian.huashan.check.emuns.payBill.PayBillDiffStatus;
import so.dian.huashan.check.emuns.payBill.PayBillReason;
import so.dian.huashan.check.emuns.payBill.PayBillStatus;
import so.dian.huashan.check.job.payBill.PayBillCheckJob;
import so.dian.huashan.check.mapper.PayBillCheckConsistencyMapper;
import so.dian.huashan.check.mapper.PayBillCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.collection.mapper.PayBillMapper;
import so.dian.huashan.collection.mapper.PayBillThirdMapper;
import so.dian.huashan.collection.mapper.dos.PayBillDO;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:37
 * @description:
 */
@Service
@Slf4j
public class PayBillCheckDiffService extends PayBillService {

    private final static String MAX_ID = "max.id";

    @Resource
    private PayBillCheckDiffMapper payBillCheckDiffMapper;

    @Resource
    private PayBillThirdMapper payBillThirdMapper;

    @Resource
    private PayBillCheckConsistencyMapper payBillCheckConsistencyMapper;

    @Autowired
    private PayBillCheckDiffService self;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDate = shardParam.getJobParam().getExtParam(PayBillCheckJob.CHECK_DATE_TIME);
        if(StringUtils.isEmpty(checkDate)){
            log.error("对账日期为空，shardParam："+ JSONObject.toJSONString(shardParam));
            return Collections.emptyList();
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Long startTime = DateUtil.beginOfDay(checkDateTime).getTime();

        Long endTime = DateUtil.endOfDay(checkDateTime).getTime();

        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        payBillCheckDiffMapper.selectByCheckDateTime(startTime,endTime,shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<PayBillCheckDiffDO> payBillCheckDiffDOS = payBillCheckDiffMapper.selectByIds(ids);
        if (CollUtil.isEmpty(payBillCheckDiffDOS))
            return;

        // 业务对账逻辑
        // 1）根据三方流水单号查出这批付款但对应的三方数据
        List<String> list = payBillCheckDiffDOS.stream().map(PayBillCheckDiffDO::getChannelTradeNo).collect(Collectors.toList());
        Map<String,PayBillThirdDO> map = new HashMap();
        if(CollectionUtils.isNotEmpty(list)){
            List<PayBillThirdDO> payBillThirdDOS = payBillThirdMapper.selectByTradeNos(list);
            if(CollectionUtils.isNotEmpty(payBillThirdDOS)){
                map = payBillThirdDOS.stream().collect(Collectors.toMap(PayBillThirdDO::getChannelTradeNo, Function.identity()));
            }
        }

        List<PayBillCheckDiffDO> addDiffList = new ArrayList<>();

        List<PayBillCheckConsistencyDO> addConsistencyList = new ArrayList<>();

        List<Long> idList = new ArrayList<>();

        List<Long> payBillThirdIds = new ArrayList<>();

        // 2）对数据进行for循环
        for(PayBillCheckDiffDO payBillCheckDiffDO : payBillCheckDiffDOS){
            PayBillThirdDO payBillThirdDO = map.get(payBillCheckDiffDO.getChannelTradeNo());
            if(Objects.isNull(payBillThirdDO)){
                payBillCheckDiffDO.setGmtUpdate(System.currentTimeMillis());
                addDiffList.add(payBillCheckDiffDO);
                continue;
            }
            // 比较金额
            if(!payBillCheckDiffDO.getTradeAmount().equals(payBillThirdDO.getTradeAmount())){
                payBillCheckDiffDO.setTradeAmount(payBillCheckDiffDO.getTradeAmount());
                payBillCheckDiffDO.setThirdTradeAmount(payBillThirdDO.getTradeAmount());
                payBillCheckDiffDO.setReason(PayBillReason.NOT_AMOUNT.getCode());
                payBillCheckDiffDO.setGmtUpdate(System.currentTimeMillis());
                addDiffList.add(payBillCheckDiffDO);
                payBillThirdIds.add(payBillThirdDO.getId());
                continue;
            }
            idList.add(payBillCheckDiffDO.getPayBillId());
            payBillThirdIds.add(payBillThirdDO.getId());
            // 添加平账记录
            addConsistencyList.add(payBillCheckConsistencyDO(payBillCheckDiffDO,payBillThirdDO));
        }

        self.batchDiffSave(addDiffList,addConsistencyList,idList,payBillThirdIds);
    }

    private PayBillCheckConsistencyDO payBillCheckConsistencyDO(PayBillCheckDiffDO payBillCheckDiffDO,PayBillThirdDO payBillThirdDO){
        PayBillCheckConsistencyDO payBillCheckConsistencyDO = new PayBillCheckConsistencyDO();
        payBillCheckConsistencyDO.setPayBillId(payBillCheckDiffDO.getPayBillId());
        payBillCheckConsistencyDO.setPayBillThirdId(payBillThirdDO.getId());
        payBillCheckConsistencyDO.setCheckType(PayBillCheckType.DIFF.getCode());
        payBillCheckConsistencyDO.setPaySubjectId(payBillCheckDiffDO.getPaySubjectId());
        payBillCheckConsistencyDO.setPaySubjectType(payBillCheckDiffDO.getPaySubjectType());
        payBillCheckConsistencyDO.setTradeNo(payBillCheckDiffDO.getTradeNo());
        payBillCheckConsistencyDO.setChannelTradeNo(payBillCheckDiffDO.getChannelTradeNo());
        payBillCheckConsistencyDO.setTradeTime(payBillCheckDiffDO.getTradeTime());
        payBillCheckConsistencyDO.setTradeType(payBillCheckDiffDO.getTradeType());
        payBillCheckConsistencyDO.setSource(payBillCheckDiffDO.getSource());
        payBillCheckConsistencyDO.setTradeAmount(payBillCheckDiffDO.getTradeAmount());
        payBillCheckConsistencyDO.init();
        return payBillCheckConsistencyDO;
    }

    @Transactional
    public void batchDiffSave(List<PayBillCheckDiffDO> addDiffList,List<PayBillCheckConsistencyDO> addConsistencyList,List<Long> idList,List<Long> payBillThirdIds) {
        if (CollUtil.isNotEmpty(idList)){
            payBillCheckDiffMapper.batchDeleteByPayBillIds(idList);
        }

        if (CollUtil.isNotEmpty(addDiffList)){
            payBillCheckDiffMapper.batchUpdate(addDiffList);
        }

        if (CollUtil.isNotEmpty(addConsistencyList)){
            payBillCheckConsistencyMapper.batchInsert(addConsistencyList);
        }

        if(CollUtil.isNotEmpty(payBillThirdIds)){
            payBillThirdMapper.batchUpdateStatusByIds(payBillThirdIds,PayBillStatus.PROCESSED.getCode());
        }
    }

}
