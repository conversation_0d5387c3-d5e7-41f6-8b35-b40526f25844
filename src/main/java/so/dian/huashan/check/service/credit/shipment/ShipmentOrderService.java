package so.dian.huashan.check.service.credit.shipment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.common.CreditCheckCommon;
import so.dian.huashan.check.emuns.CheckTypeEnum;
import so.dian.huashan.check.job.credit.shipment.ShipmentOrderCheckJob;
import so.dian.huashan.check.manage.CreditCheckManager;

import so.dian.huashan.check.manage.dto.CreditCheckDTO;
import so.dian.huashan.common.mapper.supplychain.ShipmentOrderMapper;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@Service
@Slf4j
public class ShipmentOrderService extends ShardParamRecordService {
    @Autowired
    private ShipmentOrderMapper shipmentOrderMapper;
    @Autowired
    private CreditCheckManager creditCheckManager;
    @Autowired
    private CreditCheckCommon creditCheckCommon;
    private final static String MAX_ID = "max.id";

    public List<String> listIds(InstallShardParam shardParam) {
        log.info("ShipmentOrderService get listIds shardParam:{}",shardParam);
        String checkDate = shardParam.getJobParam().getExtParam(ShipmentOrderCheckJob.CHECK_DATE_TIME);
        if (StringUtils.isEmpty(checkDate)) {
            shardParam.putExtParam(ShipmentOrderCheckJob.CHECK_DATE_TIME, LocalDateUtils.format(LocalDateUtils.yesterday(), NORM_DATETIME_PATTERN));
            checkDate = LocalDateUtils.format(LocalDateUtils.yesterday(), NORM_DATETIME_PATTERN);
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Date startTime = DateUtil.beginOfDay(checkDateTime);

        Date endTime = DateUtil.endOfDay(checkDateTime);
        log.info("ShipmentOrderService get listIds startTime:{},endTime:{}",startTime,endTime);
        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        shipmentOrderMapper.selectByCheckDateTime(startTime, endTime, shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    /**
     * 校验
     */
    public void check(ExecuteShardParam shardParam) {
        log.info("ShipmentOrderService get shardParam:{}",shardParam);
        CreditCheckDTO creditCheckDTO = creditCheckCommon.shipmentOrderCheck(shardParam.bizIdToLong(), CheckTypeEnum.INCREMENT.getCode(),shardParam.getBatchNo());
        if (Objects.isNull(creditCheckDTO)){
            return;
        }
        // 批量添加
        creditCheckManager.batchSaveAndDelete(Collections.singletonList(creditCheckDTO));
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(ShipmentOrderCheckJob.CREDIT_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
