package so.dian.huashan.check.service.pay.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.himalaya.common.entity.PageData;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.check.controller.req.GetCheckDiffPageReq;
import so.dian.huashan.check.controller.req.GetSummaryPageReq;
import so.dian.huashan.check.controller.response.GetCheckDiffResponse;
import so.dian.huashan.check.controller.response.GetSummaryResponse;
import so.dian.huashan.check.emuns.payBill.BillSourceEnum;
import so.dian.huashan.check.emuns.payBill.PayBillReason;
import so.dian.huashan.check.emuns.settle.SettleSubjectType;
import so.dian.huashan.check.mapper.PayBillCheckDiffMapper;
import so.dian.huashan.check.mapper.PayBillSummaryMapper;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.check.mapper.entity.PayBillSummaryDO;
import so.dian.huashan.check.service.pay.PayBillService;
import so.dian.huashan.common.facade.AgentFacade;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.model.dto.CheckOffSumAndCountDTO;
import so.dian.huashan.model.dto.ManualReconciliationDTO;
import so.dian.huashan.model.param.CheckOffSumAndCountParam;
import so.dian.huashan.model.param.SummaryParam;
import so.dian.huashan.model.query.CheckDiffQuery;
import so.dian.huashan.model.query.GetSummaryPageQuery;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static so.dian.huashan.common.enums.BizErrorCodeEnum.*;

@Slf4j
@Service
public class PayBillServiceImpl implements PayBillService {

    @Resource
    private PayBillCheckDiffMapper payBillCheckDiffMapper;
    @Resource
    private PayBillSummaryMapper payBillSummaryMapper;
    @Autowired
    private AgentFacade agentFacade;

    @Override
    @Transactional
    public Boolean manualReconciliation(ManualReconciliationDTO manualReconciliationDTO) {
        log.info("PayBillService manualReconciliation get manualReconciliationDTO:{}", JSON.toJSON(manualReconciliationDTO));
        if (Objects.isNull(manualReconciliationDTO) || Objects.isNull(manualReconciliationDTO.getId())
                || Objects.isNull(manualReconciliationDTO.getStatus())) {
            throw BizException.create(PARAMETER_ERROR);
        }
        if (Objects.nonNull(manualReconciliationDTO.getRemark()) && manualReconciliationDTO.getRemark().length() > 200) {
            throw BizException.create(NOTES_INCLUDE_PUNCTUATION_MARKS);
        }

        PayBillCheckDiffDO payBillCheckDiffDO = payBillCheckDiffMapper.selectByPrimaryKey(manualReconciliationDTO.getId());
        if (Objects.isNull(payBillCheckDiffDO)){
            throw new RuntimeException("差异表数据不存在");
        }
        payBillCheckDiffDO.setRemark(manualReconciliationDTO.getRemark());
        payBillCheckDiffDO.setStatus(manualReconciliationDTO.getStatus());
        payBillCheckDiffDO.setGmtUpdate(System.currentTimeMillis());
        // 更新状态
        payBillCheckDiffMapper.updateByPrimaryKey(payBillCheckDiffDO);

        // 获取差异信息
        CheckOffSumAndCountDTO checkOffDiffAmountAndCount = getCheckOffSumAndCountDTO(payBillCheckDiffDO);
        PayBillSummaryDO payBillSummaryDO = new PayBillSummaryDO();
        if (Objects.isNull(checkOffDiffAmountAndCount)||Objects.isNull(checkOffDiffAmountAndCount.getCount())
                ||Objects.isNull(checkOffDiffAmountAndCount.getDiffAmount())){
            payBillSummaryDO.setPayDiffCount(0);
            payBillSummaryDO.setPayDiffAmount(0L);
        } else {
            payBillSummaryDO.setPayDiffCount(checkOffDiffAmountAndCount.getCount());
            payBillSummaryDO.setPayDiffAmount(checkOffDiffAmountAndCount.getDiffAmount());
        }

        List<PayBillSummaryDO> payBillSummaryDOList = getPayBillSummaryDOList(payBillCheckDiffDO);
        if (CollectionUtils.isEmpty(payBillSummaryDOList)){
            throw BizException.create(SUMMARY_DATA_GENERATING);
        }
        payBillSummaryDO.setId(payBillSummaryDOList.get(0).getId());
        payBillSummaryMapper.updateByPrimaryKeySelective(payBillSummaryDO);
        return true;
    }

    private List<PayBillSummaryDO> getPayBillSummaryDOList(PayBillCheckDiffDO payBillCheckDiffDO){
        SummaryParam summaryParam = new SummaryParam();
        summaryParam.setPaySubjectId(payBillCheckDiffDO.getPaySubjectId());
        summaryParam.setSource(payBillCheckDiffDO.getSource());
        summaryParam.setNaturalDate(DateUtil.parseDateYyyyMMdd2IntThrowException(payBillCheckDiffDO.getTradeTime()));
        return payBillSummaryMapper.selectByParam(summaryParam);
    }
    private CheckOffSumAndCountDTO getCheckOffSumAndCountDTO(PayBillCheckDiffDO payBillCheckDiffDO){
        log.info("getCheckOffSumAndCountDTO get payBillCheckDiffDO:{}",payBillCheckDiffDO);
        CheckOffSumAndCountParam checkDiffParam = new CheckOffSumAndCountParam();
        checkDiffParam.setPaySubjectId(payBillCheckDiffDO.getPaySubjectId());
        checkDiffParam.setSource(payBillCheckDiffDO.getSource());
        checkDiffParam.setTradeStartTime(DateUtil.getStartDate(payBillCheckDiffDO.getTradeTime()));
        checkDiffParam.setTradeEndTime(DateUtil.getEndDate(payBillCheckDiffDO.getTradeTime()));
        return payBillCheckDiffMapper.checkOffDiffAmountAndCount(checkDiffParam);
    }

    @Override
    public PageData<GetSummaryResponse> getSummary(GetSummaryPageReq getSummaryPageReq) {
        log.info("getSummary get getSummaryPageReq:{}",getSummaryPageReq);
        if (Objects.isNull(getSummaryPageReq)){
            return PageData.create(new ArrayList<>(), 0L);
        }
        Page<PayBillSummaryDO> page = PageHelper
                .startPage(getSummaryPageReq.getPageNo(), getSummaryPageReq.getPageSize(), "natural_date desc")
                .doSelectPage(() -> payBillSummaryMapper.selectPageByQuery(getSummaryPageQuery(getSummaryPageReq)));
        return PageData.create(getGetSummaryResponseList(page.getResult()), page.getTotal());
    }
    @Override
    public PageData<GetCheckDiffResponse> getCheckDiff(GetCheckDiffPageReq getCheckDiffPageReq) {
        log.info("getCheckDiff get getCheckDiffPageReq:{}",getCheckDiffPageReq);
        if (Objects.isNull(getCheckDiffPageReq)){
            return PageData.create(new ArrayList<>(), 0L);
        }
        Page<PayBillCheckDiffDO> page = PageHelper
                .startPage(getCheckDiffPageReq.getPageNo(), getCheckDiffPageReq.getPageSize(), "trade_time desc")
                .doSelectPage(() ->  payBillCheckDiffMapper.selectByQuery(getCheckDiffQuery(getCheckDiffPageReq)));
        return PageData.create(getCheckDiffResponseList(page.getResult()), page.getTotal());
    }

    /**
     * 类型转化
     */
    private CheckDiffQuery getCheckDiffQuery(GetCheckDiffPageReq getCheckDiffPageReq){
        CheckDiffQuery checkDiffQuery = new CheckDiffQuery();
        checkDiffQuery.setStatus(getCheckDiffPageReq.getStatus());
        checkDiffQuery.setPaySubjectId(getCheckDiffPageReq.getPaySubjectId());
        checkDiffQuery.setTradeNo(getCheckDiffPageReq.getTradeNo());
        checkDiffQuery.setChannelTradeNo(getCheckDiffPageReq.getChannelTradeNo());
        checkDiffQuery.setPaySubjectType(getCheckDiffPageReq.getPaySubjectType());
        checkDiffQuery.setSource(getCheckDiffPageReq.getPayChannel());
        if (Objects.nonNull(getCheckDiffPageReq.getTradeStartTime())){
            checkDiffQuery.setTradeStartTime(DateUtil.getStartDate(getCheckDiffPageReq.getTradeStartTime()));
        }
        if (Objects.nonNull(getCheckDiffPageReq.getTradeEndTime())){
            checkDiffQuery.setTradeEndTime(DateUtil.getEndDate(getCheckDiffPageReq.getTradeEndTime()));
        }
        return checkDiffQuery;
    }

    /**
     * 差异表类型转化
     * @param payBillCheckDiffDOList
     * @return
     */
    private List<GetCheckDiffResponse> getCheckDiffResponseList(List<PayBillCheckDiffDO> payBillCheckDiffDOList) {
        if (CollectionUtils.isEmpty(payBillCheckDiffDOList)) {
            return null;
        }
        // 获取代理商名称
        List<Long> agentIds = payBillCheckDiffDOList.stream().map(PayBillCheckDiffDO::getPaySubjectId).collect(Collectors.toList());
        // 获取代理商名称
        Map<Long, String> agentMap = getAgentMap(agentIds);
        return payBillCheckDiffDOList.stream().map(payBillCheckDiffDO -> {
            GetCheckDiffResponse response = new GetCheckDiffResponse();
            response.setPayChannel(payBillCheckDiffDO.getSource());
            response.setPayChannelName(BillSourceEnum.getDesc(payBillCheckDiffDO.getSource()));
            response.setPayDiffAmount(payBillCheckDiffDO.getTradeAmount()-payBillCheckDiffDO.getThirdTradeAmount());
            response.setChannelTradeNo(payBillCheckDiffDO.getChannelTradeNo());
            response.setReason(payBillCheckDiffDO.getReason());
            response.setTradeTime(DateUtil.toLongDateString(payBillCheckDiffDO.getTradeTime()));
            response.setPaySubjectType(payBillCheckDiffDO.getPaySubjectType());
            response.setPayChannelSuccessAmount(payBillCheckDiffDO.getThirdTradeAmount());
            response.setPaySuccessAmount(payBillCheckDiffDO.getTradeAmount());
            response.setReasonName(PayBillReason.getDesc(payBillCheckDiffDO.getReason()));
            response.setId(payBillCheckDiffDO.getId());
            response.setTradeNo(payBillCheckDiffDO.getTradeNo());
            response.setChannelTradeNo(payBillCheckDiffDO.getChannelTradeNo());
            response.setStatus(payBillCheckDiffDO.getStatus());
            response.setPaySubjectName(agentMap.get(payBillCheckDiffDO.getPaySubjectId()));
            response.setPaySubjectTypeName(SettleSubjectType.getDesc(payBillCheckDiffDO.getPaySubjectType()));
            response.setRemark(payBillCheckDiffDO.getRemark());
            return response;
        }).collect(Collectors.toList());
       }
    /**
     * 获取参数转化
     * @param getSummaryPageReq
     * @return
     */
    private GetSummaryPageQuery getSummaryPageQuery(GetSummaryPageReq getSummaryPageReq){
        return GetSummaryPageQuery.builder()
                .source(getSummaryPageReq.getPayChannel())
                .checkStartDate(DateUtil.parseDateYyyyMMdd2IntThrowException(getSummaryPageReq.getCheckStartDate()))
                .checkEndDate(DateUtil.parseDateYyyyMMdd2IntThrowException(getSummaryPageReq.getCheckEndDate()))
                .paySubjectType(getSummaryPageReq.getPaySubjectType())
                .paySubjectId(getSummaryPageReq.getPaySubjectId())
                .pageNo(getSummaryPageReq.getPageNo())
                .pageSize(getSummaryPageReq.getPageNo())
                .build();
    }

    /**
     *  获取汇总
     */
    private List<GetSummaryResponse> getGetSummaryResponseList(List<PayBillSummaryDO> payBillSummaryDOList) {
        if (CollectionUtils.isEmpty(payBillSummaryDOList)) {
            return null;
        }
        List<Long> agentIds = payBillSummaryDOList.stream().map(PayBillSummaryDO::getPaySubjectId).collect(Collectors.toList());
        // 获取代理商名称
        Map<Long, String> agentMap = getAgentMap(agentIds);
        return payBillSummaryDOList.stream().map(k -> {
            GetSummaryResponse getSummaryResponse = new GetSummaryResponse();
            getSummaryResponse.setCheckDate(DateUtil.toLongDateString(DateUtil.formatDate2DateYyyyMM(k.getNaturalDate())));
            getSummaryResponse.setPayChannel(k.getSource());
            getSummaryResponse.setPaySubjectType(k.getPaySubjectType());
            getSummaryResponse.setDiffAmount(k.getPayDiffAmount());
            getSummaryResponse.setDiffNum(k.getPayDiffCount());
            getSummaryResponse.setPayChannelName(BillSourceEnum.getDesc(k.getSource()));
            getSummaryResponse.setPaySubjectName(agentMap.get(k.getPaySubjectId()));
            getSummaryResponse.setPaySubjectId(k.getPaySubjectId());
            getSummaryResponse.setPaySubjectTypeName(SettleSubjectType.getDesc(k.getPaySubjectType()));
            return getSummaryResponse;
        }).collect(Collectors.toList());
    }
    /**
     * 获取代理商名称map
     */
    private Map<Long, String> getAgentMap(List<Long> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)){
            return new HashMap<>();
        }
        List<AgentDTO> agentDTOList = agentFacade.getByIds(agentIds);
        if (CollectionUtils.isEmpty(agentDTOList)) {
            return new HashMap<>();
        }
        return agentDTOList.stream().collect(Collectors.toMap(AgentDTO::getAgentId, AgentDTO::getAgentName, (v1, v2) -> v1));
    }
}
