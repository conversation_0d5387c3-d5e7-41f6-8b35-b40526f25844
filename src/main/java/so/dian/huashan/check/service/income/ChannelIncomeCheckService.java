package so.dian.huashan.check.service.income;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.check.handler.impl.ChannelAccountingHandler;
import so.dian.huashan.check.job.income.ChannelIncomeCheckJob;
import so.dian.huashan.collection.mapper.ThirdPartyBillMapper;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static so.dian.huashan.check.job.income.ChannelIncomeCheckJob.INCOME_CHECK_JOB_NAME;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:37
 * @description:
 */
@Service
public class ChannelIncomeCheckService extends ShardParamRecordService {

    private final static String MAX_ID = "max.id";

    @Autowired
    private ThirdPartyBillMapper thirdPartyBillMapper;

    @Autowired
    private ChannelAccountingHandler channelAccountingHandler;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDateStr = shardParam.getJobParam().getExtParam(ChannelIncomeCheckJob.CHECK_DATE);

        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        thirdPartyBillMapper.selectIdForShard(Integer.parseInt(checkDateStr), shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {

        AbstractCheckContext checkContext = AbstractCheckContext.builder()
                .taskMasterId(shardParam.getTaskMasterId())
                .taskSubId(shardParam.getSubTaskId())
                .voucher(JSON.toJSONString(shardParam.bizIdToLong()))
                .build();
        channelAccountingHandler.executeBiz(checkContext);
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(INCOME_CHECK_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
