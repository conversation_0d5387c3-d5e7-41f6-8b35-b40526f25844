package so.dian.huashan.check.service.payBill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.emuns.payBill.PayBillDiffStatus;
import so.dian.huashan.check.emuns.payBill.PayBillReason;
import so.dian.huashan.check.emuns.payBill.PayBillStatus;
import so.dian.huashan.check.emuns.payBill.PayBillTradeType;
import so.dian.huashan.check.job.payBill.PayBillCheckJob;
import so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.collection.mapper.PayBillMapper;
import so.dian.huashan.collection.mapper.PayBillThirdMapper;
import so.dian.huashan.collection.mapper.dos.PayBillDO;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:37
 * @description:
 */
@Service
@Slf4j
public class PayBillCheckService extends PayBillService {

    private final static String MAX_ID = "max.id";

    @Resource
    private PayBillMapper payBillMapper;

    @Resource
    private PayBillThirdMapper payBillThirdMapper;

    @Autowired
    private PayBillCheckService self;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDate = shardParam.getJobParam().getExtParam(PayBillCheckJob.CHECK_DATE_TIME);
        if(StringUtils.isEmpty(checkDate)){
            log.error("对账日期为空，shardParam："+ JSONObject.toJSONString(shardParam));
            return Collections.emptyList();
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Long startTime = DateUtil.beginOfDay(checkDateTime).getTime();

        Long endTime = DateUtil.endOfDay(checkDateTime).getTime();

        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        payBillMapper.selectByCheckDateTime(startTime,endTime,shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<PayBillDO> payBillDOS = payBillMapper.selectByIds(ids);
        if (CollUtil.isEmpty(payBillDOS))
            return;

        // 业务对账逻辑
        // 1）根据三方流水单号查出这批付款但对应的三方数据
        List<String> list = payBillDOS.stream().map(PayBillDO::getChannelTradeNo).collect(Collectors.toList());
        Map<String,PayBillThirdDO> map = new HashMap();
        if(CollectionUtils.isNotEmpty(list)){
            List<PayBillThirdDO> payBillThirdDOS = payBillThirdMapper.selectByTradeNos(list);
            if(CollectionUtils.isNotEmpty(payBillThirdDOS)){
                map = payBillThirdDOS.stream().collect(Collectors.toMap(PayBillThirdDO::getChannelTradeNo, Function.identity()));
            }
        }

        List<PayBillCheckDiffDO> addDiffList = new ArrayList<>();

        List<PayBillCheckConsistencyDO> addConsistencyList = new ArrayList<>();

        List<Long> idList = new ArrayList<>();

        List<Long> payBillIds = new ArrayList<>();

        List<Long> payBillThirdIds = new ArrayList<>();

        // 2）对数据进行for循环
        for(PayBillDO payBillDO : payBillDOS){
            // 3）判断原数据状态是否为2，如果是，需要逻辑删除平账表对应的数据和差异表对应的数据
            Integer  status = payBillDO.getStatus() & 15;
            if(PayBillStatus.PROCESSING.getCode().equals(status)){
                // 删除平账表和差异表关于这条对账结果的数据
                idList.add(payBillDO.getId());
            }
            PayBillThirdDO payBillThirdDO = map.get(payBillDO.getChannelTradeNo());
            if(Objects.isNull(payBillThirdDO)){
                addDiffList.add(buildPayBillCheckDiffDO(payBillDO,null, PayBillReason.NOT_THIRD.getCode()));
                payBillIds.add(payBillDO.getId());
                continue;
            }
            // 比较金额
            if(!payBillDO.getTradeAmount().equals(payBillThirdDO.getTradeAmount())){
                addDiffList.add(buildPayBillCheckDiffDO(payBillDO,payBillThirdDO,PayBillReason.NOT_AMOUNT.getCode()));
                payBillIds.add(payBillDO.getId());
                payBillThirdIds.add(payBillThirdDO.getId());
                continue;
            }
            payBillIds.add(payBillDO.getId());
            payBillThirdIds.add(payBillThirdDO.getId());
            // 添加平账记录
            addConsistencyList.add(buildPayBillCheckConsistencyDO(payBillDO,payBillThirdDO));
        }

        self.batchSave("payBillJob",addDiffList,addConsistencyList,idList,payBillIds,payBillThirdIds);
    }

    private PayBillCheckDiffDO buildPayBillCheckDiffDO(PayBillDO payBillDO,PayBillThirdDO payBillThirdDO,Integer reason){
        PayBillCheckDiffDO payBillCheckDiffDO = new PayBillCheckDiffDO();
        payBillCheckDiffDO.setPayBillId(payBillDO.getId());
        if(Objects.nonNull(payBillThirdDO)){
            payBillCheckDiffDO.setPayBillThirdId(payBillThirdDO.getId());
            if(PayBillTradeType.IN.getCode().equals(payBillThirdDO.getTradeType())){
                payBillCheckDiffDO.setThirdTradeAmount(-payBillThirdDO.getTradeAmount());
            }else{
                payBillCheckDiffDO.setThirdTradeAmount(payBillThirdDO.getTradeAmount());
            }
        }else{
            payBillCheckDiffDO.setThirdTradeAmount(0L);
        }
        payBillCheckDiffDO.setPaySubjectId(payBillDO.getPaySubjectId());
        payBillCheckDiffDO.setPaySubjectType(payBillDO.getPaySubjectType());
        payBillCheckDiffDO.setChannelTradeNo(payBillDO.getChannelTradeNo());
        payBillCheckDiffDO.setTradeNo(payBillDO.getTradeNo());
        payBillCheckDiffDO.setTradeTime(payBillDO.getTradeTime());
        payBillCheckDiffDO.setTradeAmount(Objects.isNull(payBillDO.getTradeAmount()) ? 0L : payBillDO.getTradeAmount());
        payBillCheckDiffDO.setTradeType(payBillDO.getTradeType());
        payBillCheckDiffDO.setStatus(PayBillDiffStatus.NOT_ACCOUNTS.getCode());
        payBillCheckDiffDO.setSource(payBillDO.getSource());
        payBillCheckDiffDO.setReason(reason);
        payBillCheckDiffDO.init();
        return payBillCheckDiffDO;
    }
}
