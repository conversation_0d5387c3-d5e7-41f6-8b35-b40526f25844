package so.dian.huashan.check.service.iot;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.check.emuns.iot.SettleDetailStatus;
import so.dian.huashan.check.job.iot.SettleDetailCheckJob;
import so.dian.huashan.check.job.iot.WxSplitBillCheckJob;
import so.dian.huashan.check.mapper.SplitBillCheckConsistencyMapper;
import so.dian.huashan.check.mapper.SplitBillCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO;
import so.dian.huashan.collection.mapper.SettleDetailOriginalMapper;
import so.dian.huashan.collection.mapper.WxSplitBillDetailMapper;
import so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:37
 * @description:
 */
@Service
@Slf4j
public class SettleWxBaseService {

    @Resource
    private SettleDetailOriginalMapper settleDetailOriginalMapper;

    @Resource
    private WxSplitBillDetailMapper wxSplitBillDetailMapper;

    @Resource
    private SplitBillCheckDiffMapper splitBillCheckDiffMapper;

    @Resource
    private SplitBillCheckConsistencyMapper splitBillCheckConsistencyMapper;


    public SplitBillCheckConsistencyDO buildSplitBillCheckConsistencyDO(SettleDetailOriginalDO settleDetailOriginalDO,WxSplitBillDetailDO wxSplitBillDetailDO){
        SplitBillCheckConsistencyDO splitBillCheckConsistencyDO = new SplitBillCheckConsistencyDO();
        splitBillCheckConsistencyDO.setSettleDetailOriginalId(settleDetailOriginalDO.getId());
        splitBillCheckConsistencyDO.setWxSplitBillDetailId(wxSplitBillDetailDO.getId());
        splitBillCheckConsistencyDO.setSplitDetailNo(wxSplitBillDetailDO.getSplitDetailNo());
        splitBillCheckConsistencyDO.setSplitAmt(settleDetailOriginalDO.getAmountOutput());
        splitBillCheckConsistencyDO.setSplitReceiveId(settleDetailOriginalDO.getTpReceiveId());
        splitBillCheckConsistencyDO.setSplitSourceId(settleDetailOriginalDO.getTpSourceId());
        splitBillCheckConsistencyDO.init();
        return splitBillCheckConsistencyDO;
    }

    @Transactional
    public void batchSave(String code, List<SplitBillCheckDiffDO> addDiffList, List<SplitBillCheckConsistencyDO> addConsistencyList, List<Long> idList, List<Long> settleDetailIds, List<Long> wxBillThirdIds) {

        if(CollUtil.isNotEmpty(idList) && WxSplitBillCheckJob.WX_SPLIT_BILL_JOB_NAME.equals(code)){
            splitBillCheckDiffMapper.batchDeleteByWxSplitIds(idList);
            splitBillCheckConsistencyMapper.batchDeleteBySplitBillThirdIds(idList);
        }else if(CollUtil.isNotEmpty(idList) && SettleDetailCheckJob.SETTLE_BILL_JOB_NAME.equals(code)){
            splitBillCheckDiffMapper.batchDeleteBySettleDetailIds(idList);
            splitBillCheckConsistencyMapper.batchDeleteBySplitBillIds(idList);
        }

        if (CollUtil.isNotEmpty(addDiffList)){
            splitBillCheckDiffMapper.batchInsert(addDiffList);
        }

        if (CollUtil.isNotEmpty(addConsistencyList)){
            splitBillCheckConsistencyMapper.batchInsert(addConsistencyList);
        }

        if(CollUtil.isNotEmpty(settleDetailIds)){
            settleDetailOriginalMapper.batchUpdateStatusByIds(settleDetailIds, SettleDetailStatus.PROCESSED.getCode());
        }

        if(CollUtil.isNotEmpty(wxBillThirdIds)){
            wxSplitBillDetailMapper.batchUpdateStatusByIds(wxBillThirdIds,SettleDetailStatus.PROCESSED.getCode());
        }
    }

    @Transactional
    public void batchDiffSave(List<SplitBillCheckDiffDO> addDiffList, List<SplitBillCheckConsistencyDO> addConsistencyList, List<Long> idList, List<Long> wxSplitIds,List<Long> settleIds) {
        if (CollUtil.isNotEmpty(idList)) {
            splitBillCheckDiffMapper.batchDeleteByIds(idList);
        }

        if (CollUtil.isNotEmpty(addDiffList)) {
            splitBillCheckDiffMapper.batchUpdate(addDiffList);
        }

        if (CollUtil.isNotEmpty(addConsistencyList)) {
            splitBillCheckConsistencyMapper.batchInsert(addConsistencyList);
        }

        if (CollUtil.isNotEmpty(wxSplitIds)) {
            wxSplitBillDetailMapper.batchUpdateStatusByIds(wxSplitIds, SettleDetailStatus.PROCESSED.getCode());
        }

        if (CollUtil.isNotEmpty(settleIds)) {
            settleDetailOriginalMapper.batchUpdateStatusByIds(settleIds, SettleDetailStatus.PROCESSED.getCode());
        }

    }
}
