package so.dian.huashan.check.service.iot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.emuns.iot.SettleDetailScene;
import so.dian.huashan.check.emuns.iot.SettleDetailStatus;
import so.dian.huashan.check.emuns.iot.WxSplitDiffStatus;
import so.dian.huashan.check.job.dto.DiffReasonDTO;
import so.dian.huashan.check.job.dto.SettleWxDiffReasonDTO;
import so.dian.huashan.check.job.iot.SettleDetailCheckJob;
import so.dian.huashan.check.job.iot.WxSplitBillCheckJob;
import so.dian.huashan.check.job.payBill.PayBillCheckJob;
import so.dian.huashan.check.mapper.entity.SplitBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.SplitBillCheckDiffDO;
import so.dian.huashan.collection.mapper.SettleDetailOriginalMapper;
import so.dian.huashan.collection.mapper.WxSplitBillDetailMapper;
import so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.framework.task.ShardParamRecordService;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;
import static so.dian.huashan.check.job.iot.WxSplitBillCheckJob.WX_SPLIT_BILL_JOB_NAME;

/**
 * @author: xingba
 * @create: 2024/09/10 16:37
 * @description:
 */
@Service
@Slf4j
public class WxSplitBillCheckService extends ShardParamRecordService {

    private final static String MAX_ID = "max.id";

    @Resource
    private SettleDetailOriginalMapper settleDetailOriginalMapper;

    @Resource
    private WxSplitBillDetailMapper wxSplitBillDetailMapper;

    @Resource
    private SettleWxBaseService settleWxBaseService;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDate = shardParam.getJobParam().getExtParam(WxSplitBillCheckJob.CHECK_DATE_TIME);
        if(StringUtils.isEmpty(checkDate)){
            shardParam.putExtParam(WxSplitBillCheckJob.CHECK_DATE_TIME, LocalDateUtils.format(LocalDateUtils.date(),NORM_DATETIME_PATTERN));
            checkDate = LocalDateUtils.format(LocalDateUtils.date(),NORM_DATETIME_PATTERN);
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Long startTime = DateUtil.beginOfMonth(checkDateTime).getTime();

        Long endTime = DateUtil.endOfMonth(checkDateTime).getTime();

        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        wxSplitBillDetailMapper.selectByCheckDateTime(startTime,endTime,shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<WxSplitBillDetailDO> wxSplitBillDetailDOS = wxSplitBillDetailMapper.selectByIds(ids);
        if (CollUtil.isEmpty(wxSplitBillDetailDOS))
            return;

        // 业务对账逻辑
        // 1）根据三方流水单号查出这批付款但对应的三方数据
        List<String> list = wxSplitBillDetailDOS.stream().map(WxSplitBillDetailDO::getSettleDetailNo).collect(Collectors.toList());
        Map<String,SettleDetailOriginalDO> map = new HashMap();
        if(CollectionUtils.isNotEmpty(list)){
            List<SettleDetailOriginalDO> settleDetailDOS = settleDetailOriginalMapper.selectBySettleDetailNos(list);
            if(CollectionUtils.isNotEmpty(settleDetailDOS)){
                map = settleDetailDOS.stream().collect(Collectors.toMap(SettleDetailOriginalDO::getSettleDetailNo, Function.identity()));
            }
        }

        List<SplitBillCheckDiffDO> addDiffList = new ArrayList<>();

        List<SplitBillCheckConsistencyDO> addConsistencyList = new ArrayList<>();

        List<Long> idList = new ArrayList<>();

        List<Long> settleBillIds = new ArrayList<>();

        List<Long> wxBillIds = new ArrayList<>();

        // 2）对数据进行for循环
        for(WxSplitBillDetailDO wxSplitBillDetailDO : wxSplitBillDetailDOS){
            // 3）判断原数据状态是否为2，如果是，需要逻辑删除平账表对应的数据和差异表对应的数据
            Integer  status = wxSplitBillDetailDO.getCheckStatus();
            if(SettleDetailStatus.PROCESSED.getCode().equals(status)){
                log.info("渠道对账状态为已处理，不在处理，ID={}",wxSplitBillDetailDO.getId());
                continue;
            }
            if(SettleDetailStatus.PROCESSING.getCode().equals(status)){
                // 删除平账表和差异表关于这条对账结果的数据
                idList.add(wxSplitBillDetailDO.getId());
            }

            SettleDetailOriginalDO settleDetailOriginalDO = map.get(wxSplitBillDetailDO.getSettleDetailNo());

            if(Objects.isNull(settleDetailOriginalDO)){
                addDiffList.add(buildSplitBillCheckDiffDO(null,wxSplitBillDetailDO, SettleDetailScene.NOT_BIZ.getCode()));
                wxBillIds.add(wxSplitBillDetailDO.getId());
                continue;
            }
            // 比较金额
            if((Objects.nonNull(settleDetailOriginalDO.getTpSettleNo()) && Objects.nonNull(wxSplitBillDetailDO.getSplitDetailNo()) && !settleDetailOriginalDO.getTpSettleNo().equals(wxSplitBillDetailDO.getSplitDetailNo()))
                    || !settleDetailOriginalDO.getAmountOutput().equals(wxSplitBillDetailDO.getSplitAmt())
                    || !settleDetailOriginalDO.getTpSourceId().equals(wxSplitBillDetailDO.getSplitSourceId())
                    || !settleDetailOriginalDO.getTpReceiveId().equals(wxSplitBillDetailDO.getSplitReceiveId())){
                addDiffList.add(buildSplitBillCheckDiffDO(settleDetailOriginalDO,wxSplitBillDetailDO, SettleDetailScene.NOT_SCENE.getCode()));
                wxBillIds.add(wxSplitBillDetailDO.getId());
                settleBillIds.add(settleDetailOriginalDO.getId());
                continue;
            }
            wxBillIds.add(wxSplitBillDetailDO.getId());
            settleBillIds.add(settleDetailOriginalDO.getId());
            // 添加平账记录
            addConsistencyList.add(settleWxBaseService.buildSplitBillCheckConsistencyDO(settleDetailOriginalDO,wxSplitBillDetailDO));
        }

        settleWxBaseService.batchSave(WX_SPLIT_BILL_JOB_NAME,addDiffList,addConsistencyList,idList,settleBillIds,wxBillIds);
    }

    private SplitBillCheckDiffDO buildSplitBillCheckDiffDO(SettleDetailOriginalDO settleDetailOriginalDO,WxSplitBillDetailDO wxSplitBillDetailDO,Integer reason){
        SplitBillCheckDiffDO splitBillCheckDiffDO = new SplitBillCheckDiffDO();
        splitBillCheckDiffDO.setWxDetailId(wxSplitBillDetailDO.getId());
        SettleWxDiffReasonDTO settleWxDiffReasonDTO = new SettleWxDiffReasonDTO();

        if(Objects.nonNull(settleDetailOriginalDO)){
            splitBillCheckDiffDO.setBizOriginalId(settleDetailOriginalDO.getId());
            if(Objects.nonNull(settleDetailOriginalDO.getTpSettleNo()) && Objects.nonNull(wxSplitBillDetailDO.getSplitDetailNo()) && !settleDetailOriginalDO.getTpSettleNo().equals(wxSplitBillDetailDO.getSplitDetailNo())){
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitDetailNo());
                dto.setBiz(settleDetailOriginalDO.getTpSettleNo());
                settleWxDiffReasonDTO.setTpSettleNo(dto);
            }
            if(!settleDetailOriginalDO.getAmountOutput().equals(wxSplitBillDetailDO.getSplitAmt())){
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitAmt().toString());
                dto.setBiz(settleDetailOriginalDO.getAmountOutput().toString());
                settleWxDiffReasonDTO.setAmtDiff(dto);
            }
            if(!settleDetailOriginalDO.getTpSourceId().equals(wxSplitBillDetailDO.getSplitSourceId())){
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitSourceId());
                dto.setBiz(settleDetailOriginalDO.getTpSourceId());
                settleWxDiffReasonDTO.setSplitSource(dto);
            }
            if(!settleDetailOriginalDO.getTpReceiveId().equals(wxSplitBillDetailDO.getSplitReceiveId())){
                DiffReasonDTO dto = new DiffReasonDTO();
                dto.setChannel(wxSplitBillDetailDO.getSplitReceiveId());
                dto.setBiz(settleDetailOriginalDO.getTpReceiveId());
                settleWxDiffReasonDTO.setSplitTarget(dto);
            }
            splitBillCheckDiffDO.setDiffReason(JSONObject.toJSONString(settleWxDiffReasonDTO));
        }else{
            splitBillCheckDiffDO.setDiffReason(JSONObject.toJSONString(settleWxDiffReasonDTO));
        }
        splitBillCheckDiffDO.setCheckCount(0);
        splitBillCheckDiffDO.setSettleDetailNo(wxSplitBillDetailDO.getSettleDetailNo());
        splitBillCheckDiffDO.setStatus(WxSplitDiffStatus.NOT_ACCOUNTS.getCode());
        splitBillCheckDiffDO.setDiffScene(reason);
        splitBillCheckDiffDO.init();
        return splitBillCheckDiffDO;
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(WX_SPLIT_BILL_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::check;
    }
}
