package so.dian.huashan.check.service.income;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.check.controller.req.IncomeDiffBillReq;
import so.dian.huashan.check.emuns.ChannelDiffOperationType;
import so.dian.huashan.check.manage.ResultChannelBillManage;

import static so.dian.huashan.check.emuns.ErrorCode.CHANNEL_DIFF_IDS_EMPYT;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2024/03/29 13:41
 * @description:
 */
@Service
public class IncomeBillService {

    @Autowired
    private ResultChannelBillManage resultChannelBillManage;

    public Boolean batchModifyType(IncomeDiffBillReq billReq) {

        if (CollUtil.isEmpty(billReq.getChannelDiffIds()))
            throw BizException.create(CHANNEL_DIFF_IDS_EMPYT);

        ChannelDiffOperationType diffStatus = ChannelDiffOperationType.from(billReq.getChannelDiffType());
        Assert.notNull(diffStatus, "状态不能为空");

        return resultChannelBillManage.batchModifyType(billReq.getChannelDiffIds(), diffStatus);
    }
}
