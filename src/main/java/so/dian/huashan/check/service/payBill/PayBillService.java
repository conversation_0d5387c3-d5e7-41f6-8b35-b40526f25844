package so.dian.huashan.check.service.payBill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.check.emuns.payBill.PayBillCheckType;
import so.dian.huashan.check.emuns.payBill.PayBillDiffStatus;
import so.dian.huashan.check.emuns.payBill.PayBillReason;
import so.dian.huashan.check.emuns.payBill.PayBillStatus;
import so.dian.huashan.check.job.payBill.PayBillCheckJob;
import so.dian.huashan.check.mapper.PayBillCheckConsistencyMapper;
import so.dian.huashan.check.mapper.PayBillCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.collection.mapper.PayBillMapper;
import so.dian.huashan.collection.mapper.PayBillThirdMapper;
import so.dian.huashan.collection.mapper.dos.PayBillDO;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:37
 * @description:
 */
@Service
@Slf4j
public class PayBillService {

    @Resource
    private PayBillMapper payBillMapper;

    @Resource
    private PayBillThirdMapper payBillThirdMapper;

    @Resource
    private PayBillCheckDiffMapper payBillCheckDiffMapper;

    @Resource
    private PayBillCheckConsistencyMapper payBillCheckConsistencyMapper;


    public PayBillCheckConsistencyDO buildPayBillCheckConsistencyDO(PayBillDO payBillDO,PayBillThirdDO payBillThirdDO){
        PayBillCheckConsistencyDO payBillCheckConsistencyDO = new PayBillCheckConsistencyDO();
        payBillCheckConsistencyDO.setPayBillId(payBillDO.getId());
        payBillCheckConsistencyDO.setPayBillThirdId(payBillThirdDO.getId());
        payBillCheckConsistencyDO.setCheckType(PayBillCheckType.INCREMENT.getCode());
        payBillCheckConsistencyDO.setPaySubjectId(payBillDO.getPaySubjectId());
        payBillCheckConsistencyDO.setPaySubjectType(payBillDO.getPaySubjectType());
        payBillCheckConsistencyDO.setTradeNo(payBillDO.getTradeNo());
        payBillCheckConsistencyDO.setChannelTradeNo(payBillDO.getChannelTradeNo());
        payBillCheckConsistencyDO.setTradeTime(payBillDO.getTradeTime());
        payBillCheckConsistencyDO.setTradeType(payBillDO.getTradeType());
        payBillCheckConsistencyDO.setSource(payBillDO.getSource());
        payBillCheckConsistencyDO.setTradeAmount(payBillDO.getTradeAmount());
        payBillCheckConsistencyDO.init();
        return payBillCheckConsistencyDO;
    }

    @Transactional
    public void batchSave(String code,List<PayBillCheckDiffDO> addDiffList,List<PayBillCheckConsistencyDO> addConsistencyList,List<Long> idList,List<Long> payBillIds,List<Long> payBillThirdIds) {

        if(CollUtil.isNotEmpty(idList) && "payBillThirdJob".equals(code)){
            payBillCheckDiffMapper.batchDeleteByPayBillThirdIds(idList);
            payBillCheckConsistencyMapper.batchDeleteByPayBillThirdIds(idList);
        }else if(CollUtil.isNotEmpty(idList) && "payBillJob".equals(code)){
            payBillCheckDiffMapper.batchDeleteByPayBillIds(idList);
            payBillCheckConsistencyMapper.batchDeleteByPayBillIds(idList);
        }

        if (CollUtil.isNotEmpty(addDiffList)){
            payBillCheckDiffMapper.batchInsert(addDiffList);
        }

        if (CollUtil.isNotEmpty(addConsistencyList)){
            payBillCheckConsistencyMapper.batchInsert(addConsistencyList);
        }

        //修改状态需要和1048560与才能加1
        if(CollUtil.isNotEmpty(payBillIds)){
            payBillMapper.batchUpdateStatusByIds(payBillIds,PayBillStatus.PROCESSED.getCode());
        }

        if(CollUtil.isNotEmpty(payBillThirdIds)){
            payBillThirdMapper.batchUpdateStatusByIds(payBillThirdIds,PayBillStatus.PROCESSED.getCode());
        }
    }
}
