package so.dian.huashan.check.service.pay;

import so.dian.himalaya.common.entity.PageData;
import so.dian.huashan.check.controller.req.GetCheckDiffPageReq;
import so.dian.huashan.check.controller.req.GetSummaryPageReq;
import so.dian.huashan.check.controller.response.GetCheckDiffResponse;
import so.dian.huashan.check.controller.response.GetSummaryResponse;
import so.dian.huashan.model.dto.ManualReconciliationDTO;

public interface PayBillService {
    /**
     * 手动平账
     */
    Boolean manualReconciliation(ManualReconciliationDTO manualReconciliationDTO);

    /**
     *  汇总
     */
    PageData<GetSummaryResponse> getSummary(GetSummaryPageReq getSummaryPageReq);

    PageData<GetCheckDiffResponse> getCheckDiff(GetCheckDiffPageReq getCheckDiffPageReq);
}
