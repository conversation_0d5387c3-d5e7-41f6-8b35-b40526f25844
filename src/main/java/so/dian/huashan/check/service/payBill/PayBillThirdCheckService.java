package so.dian.huashan.check.service.payBill;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.check.emuns.payBill.*;
import so.dian.huashan.check.job.payBill.PayBillCheckJob;
import so.dian.huashan.check.mapper.entity.PayBillCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.PayBillCheckDiffDO;
import so.dian.huashan.collection.mapper.PayBillMapper;
import so.dian.huashan.collection.mapper.PayBillThirdMapper;
import so.dian.huashan.collection.mapper.dos.PayBillDO;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/12/20 16:37
 * @description:
 */
@Service
@Slf4j
public class PayBillThirdCheckService extends PayBillService{

    private final static String MAX_ID = "max.id";

    @Resource
    private PayBillMapper payBillMapper;

    @Resource
    private PayBillThirdMapper payBillThirdMapper;

    @Resource
    private PayBillThirdCheckService self;

    public List<String> listIds(InstallShardParam shardParam) {

        String checkDate = shardParam.getJobParam().getExtParam(PayBillCheckJob.CHECK_DATE_TIME);
        if(StringUtils.isEmpty(checkDate)){
            log.error("三方对账日期为空，shardParam："+ JSONObject.toJSONString(shardParam));
            return Collections.emptyList();
        }
        DateTime checkDateTime = LocalDateUtils.parse(checkDate);
        // 时间的开始时间
        Long startTime = DateUtil.beginOfDay(checkDateTime).getTime();

        Long endTime = DateUtil.endOfDay(checkDateTime).getTime();

        Page<Long> page = PageHelper.startPage(1, shardParam.getPageSize(), Boolean.FALSE)
                .doSelectPage(() ->
                        payBillThirdMapper.selectByCheckDateTime(startTime,endTime,shardParam.getExtParam(MAX_ID)));
        List<Long> ids = page.getResult();
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));
        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    public void check(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<PayBillThirdDO> payBillThirdDOS = payBillThirdMapper.selectByIds(ids);
        if (CollUtil.isEmpty(payBillThirdDOS))
            return;

        // 业务对账逻辑
        // 1）根据三方流水单号查出这批付款但对应的三方数据
        List<String> list = payBillThirdDOS.stream().map(PayBillThirdDO::getChannelTradeNo).collect(Collectors.toList());
        Map<String,PayBillDO> map = new HashMap();
        if(CollectionUtils.isNotEmpty(list)){
            List<PayBillDO> payBillDOS = payBillMapper.selectByTradeNos(list);
            if(CollectionUtils.isNotEmpty(payBillDOS)){
                map = payBillDOS.stream().collect(Collectors.toMap(PayBillDO::getChannelTradeNo, Function.identity()));
            }
        }

        List<PayBillCheckDiffDO> addDiffList = new ArrayList<>();

        List<PayBillCheckConsistencyDO> addConsistencyList = new ArrayList<>();

        List<Long> idList = new ArrayList<>();

        List<Long> payBillIds = new ArrayList<>();

        List<Long> payBillThirdIds = new ArrayList<>();

        // 2）对数据进行for循环
        for(PayBillThirdDO payBillThirdDO : payBillThirdDOS){
            // 3）判断原数据状态是否为2，如果是，需要逻辑删除平账表对应的数据和差异表对应的数据
            Integer  status = payBillThirdDO.getStatus() & 15;
            if(PayBillStatus.PROCESSING.getCode().equals(status)){
                // 删除平账表和差异表关于这条对账结果的数据
                idList.add(payBillThirdDO.getId());
            }

            PayBillDO payBillDO = map.get(payBillThirdDO.getChannelTradeNo());

            if(Objects.nonNull(payBillDO) && payBillThirdDO.getThirdStatus().equals(PayBillThirdStatus.REFUND.getCode())){
                addDiffList.add(buildPayBillCheckDiffDO(null,payBillThirdDO, PayBillReason.REFUND_TICKET.getCode()));
                payBillThirdIds.add(payBillThirdDO.getId());
                continue;
            }
            if(Objects.isNull(payBillDO)){
                addDiffList.add(buildPayBillCheckDiffDO(null,payBillThirdDO, PayBillReason.NOT_BIZ.getCode()));
                payBillThirdIds.add(payBillThirdDO.getId());
                continue;
            }
            // 比较金额
            if(!payBillDO.getTradeAmount().equals(payBillThirdDO.getTradeAmount())){
                addDiffList.add(buildPayBillCheckDiffDO(payBillDO,payBillThirdDO,PayBillReason.NOT_AMOUNT.getCode()));
                payBillThirdIds.add(payBillThirdDO.getId());
                payBillIds.add(payBillDO.getId());
                continue;
            }
            payBillThirdIds.add(payBillThirdDO.getId());
            payBillIds.add(payBillDO.getId());
            // 添加平账记录
            addConsistencyList.add(buildPayBillCheckConsistencyDO(payBillDO,payBillThirdDO));
        }

        self.batchSave("payBillThirdJob",addDiffList,addConsistencyList,idList,payBillIds,payBillThirdIds);
    }

    private PayBillCheckDiffDO buildPayBillCheckDiffDO(PayBillDO payBillDO,PayBillThirdDO payBillThirdDO,Integer reason){
        PayBillCheckDiffDO payBillCheckDiffDO = new PayBillCheckDiffDO();
        if(Objects.nonNull(payBillDO)){
            payBillCheckDiffDO.setPayBillId(payBillDO.getId());
            payBillCheckDiffDO.setTradeAmount(payBillDO.getTradeAmount());
        }else{
            payBillCheckDiffDO.setTradeAmount(0L);
        }
        payBillCheckDiffDO.setTradeNo(payBillThirdDO.getTradeNo());
        payBillCheckDiffDO.setPaySubjectId(payBillThirdDO.getPaySubjectId());
        payBillCheckDiffDO.setPaySubjectType(payBillThirdDO.getPaySubjectType());
        payBillCheckDiffDO.setPayBillThirdId(payBillThirdDO.getId());
        if(PayBillTradeType.IN.getCode().equals(payBillThirdDO.getTradeType())){
            payBillCheckDiffDO.setThirdTradeAmount(-payBillThirdDO.getTradeAmount());
        }else{
            payBillCheckDiffDO.setThirdTradeAmount(payBillThirdDO.getTradeAmount());
        }
        payBillCheckDiffDO.setChannelTradeNo(payBillThirdDO.getChannelTradeNo());
        payBillCheckDiffDO.setTradeTime(payBillThirdDO.getTradeTime());
        payBillCheckDiffDO.setTradeType(payBillThirdDO.getTradeType());
        payBillCheckDiffDO.setStatus(PayBillDiffStatus.NOT_ACCOUNTS.getCode());
        payBillCheckDiffDO.setSource(payBillThirdDO.getSource());
        payBillCheckDiffDO.setReason(reason);
        payBillCheckDiffDO.init();
        return payBillCheckDiffDO;
    }
}
