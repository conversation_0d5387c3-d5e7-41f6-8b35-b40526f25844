package so.dian.huashan.check.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import so.dian.huashan.check.emuns.ChannelEnum;
import so.dian.huashan.check.handler.AbstractCheckHandler;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.task.enums.InvokeTypeEnum;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.enums.TaskSubStatusEnum;
import so.dian.huashan.task.handler.AbstractTaskHandler;
import so.dian.huashan.task.handler.context.TaskResultContext;
import so.dian.huashan.task.job.dto.TaskSubDTO;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class ChannelAccountService {

    @Resource
    private TaskSubMapper taskSubMapper;

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private Map<String, AbstractTaskHandler> handlerMap;

    @Resource
    private Map<String, AbstractCheckHandler> checkHandlerMap;

    @Resource
    private RedissonClient redissonClient;

    @Qualifier("checkExecutor")
    @Resource
    private ThreadPoolTaskExecutor checkExecutor;

    private static final Integer size = 100;


    public void channel(TaskMasterDO taskMasterDO, InvokeTypeEnum invokeType, Integer index, Integer total){
        int pageNo = 1 ;
        Page<TaskSubDO> page = PageHelper.startPage(pageNo, size)
                .doSelectPage(() -> taskSubMapper.selectByTaskMasterIdStatus(index, total, taskMasterDO.getId(), null));
        if (page == null || CollectionUtils.isEmpty(page.getResult())) {
            log.info("没有数据,taskMasterDO:{},index:{}",JSONObject.toJSONString(taskMasterDO),index);
            return;
        }
        log.info("page开始={}",page.getTotal());
        RAtomicLong allTimesLong = redissonClient.getAtomicLong("huashan_all_times:" + taskMasterDO.getId());
        log.info("allTimesLong={},taskMasterDO={}",allTimesLong.get(),taskMasterDO.getId());
        // 生成主表任务数据
        AbstractCheckHandler abstractCheckHandler = checkHandlerMap.get(ChannelEnum.from(invokeType.accountCode()).desc());
        while (CollectionUtils.isNotEmpty(page.getResult())) {
            for(TaskSubDO taskSubDO : page.getResult()){
                if(!taskSubDO.getStatus().equals(TaskStatusEnum.INIT.code())){
                    continue;
                }
                getCheckExecutor().submit(() -> {
                    try {
                        log.info("taskSubDO,id={}",taskSubDO.getId());
                        // 每100条进行核对，核对完毕修改
                        abstractCheckHandler.process(AbstractCheckContext.builder().taskMasterId(taskSubDO.getTaskMasterId()).taskSubId(taskSubDO.getId()).voucher(taskSubDO.getVoucher()).build());
                    } catch (Exception e) {
                        log.error(">>>> 异步执对账逻辑", e);
                    }
                    long shardCount = allTimesLong.decrementAndGet();
                    updateTask(shardCount,taskSubDO);
                });
            }
            log.info("page循环={},allTimesLong={}",page.getTotal(),allTimesLong.get());
            // 对账完成更新对应的
            if (page.getResult().size() < size) {
                break;
            }
            pageNo = pageNo +1;
            page = PageHelper.startPage(pageNo, size)
                    .doSelectPage(() -> taskSubMapper.selectByTaskMasterIdStatus(index,total,taskMasterDO.getId(),null));
        }
    }

    public void channel(TaskMasterDO taskMasterDO, InvokeTypeEnum invokeType){
        int pageNo = 1 ;
        Page<TaskSubDO> page = PageHelper.startPage(pageNo, size)
                .doSelectPage(() -> taskSubMapper.selectByTaskMasterIdStatusList(taskMasterDO.getId(), Arrays.asList(TaskStatusEnum.INIT.code(), TaskSubStatusEnum.FAIL.code())));
        if (page == null || CollectionUtils.isEmpty(page.getResult())) {
            log.info("ChannelDifferenceJob 没有数据,taskMasterDO:{}",JSONObject.toJSONString(taskMasterDO));
            return;
        }
        RAtomicLong failTimesLong = redissonClient.getAtomicLong("huashan_fail_times:" + taskMasterDO.getId());
        failTimesLong.set(page.getTotal());
        // 生成主表任务数据
        AbstractCheckHandler abstractCheckHandler = checkHandlerMap.get(ChannelEnum.from(invokeType.accountCode()).desc());
        while (CollectionUtils.isNotEmpty(page.getResult())) {
            for(TaskSubDO taskSubDO : page.getResult()){
                getCheckExecutor().submit(() -> {
                    try {
                        // 每100条进行核对，核对完毕修改
                        abstractCheckHandler.process(AbstractCheckContext.builder().taskMasterId(taskSubDO.getTaskMasterId()).taskSubId(taskSubDO.getId()).voucher(taskSubDO.getVoucher()).build());
                    } catch (Exception e) {
                        log.error(">>>> 异步执对账失败补偿逻辑", e);
                    }
                    long shardCount = failTimesLong.decrementAndGet();
                    updateTask(shardCount,taskSubDO);
                });
            }
            // 对账完成更新对应的
            if (page.getResult().size() < size) {
                break;
            }
            pageNo = pageNo +1;
            page = PageHelper.startPage(pageNo, size)
                    .doSelectPage(() -> taskSubMapper.selectByTaskMasterIdStatusList(taskMasterDO.getId(),Arrays.asList(TaskStatusEnum.INIT.code(),TaskSubStatusEnum.FAIL.code())));
        }
    }

    private void updateTask(Long shardCount,TaskSubDO taskSubDO){
        AbstractTaskHandler abstractTaskHandler = handlerMap.get(CommonConstants.TASK_SUB);
        if (shardCount <= 0L) {
            log.info("减到0的taskSubDO,id={}",taskSubDO.getId());
            List<TaskSubDTO> statistics = taskSubMapper.statistics(taskSubDO.getTaskMasterId());
            Map<Integer,List<TaskSubDTO>> settleBillMap = statistics.stream()
                    .collect(Collectors.groupingBy(TaskSubDTO::getStatus));
            TaskResultContext taskResultContext = new TaskResultContext();
            taskResultContext.setTaskMasterId(taskSubDO.getTaskMasterId());
            taskResultContext.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
            List<TaskSubDTO> taskSubDTOS = settleBillMap.get(TaskSubStatusEnum.SUCCESS.code());
            if(CollectionUtils.isNotEmpty(taskSubDTOS)){
                TaskSubDTO taskSubDTO = taskSubDTOS.get(0);
                taskResultContext.setSuccessTimes(taskSubDTO.getCount());
            }else{
                taskResultContext.setSuccessTimes(0L);
            }
            List<TaskSubDTO> taskSubList = settleBillMap.get(TaskSubStatusEnum.FAIL.code());
            if(CollectionUtils.isNotEmpty(taskSubList)){
                TaskSubDTO taskSubDTO = taskSubList.get(0);
                taskResultContext.setFailTimes(taskSubDTO.getCount());
            }else{
                taskResultContext.setFailTimes(0L);
            }
            abstractTaskHandler.masterResult(taskResultContext);
        }
    }

    protected ThreadPoolTaskExecutor getCheckExecutor() {
        return checkExecutor;
    }
}