package so.dian.huashan.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SummaryParam {
    /**
     *  付款公司
     */
    private Long paySubjectId;

    /**
     * 支付通道
     */
    private Integer source;

    /**
     * 核对开始日期
     */
    private Integer checkStartDate;
    /**
     * 核对结束日期
     */
    private Integer checkEndDate;

    /**
     * 自然日
     */
    private Integer naturalDate;

}
