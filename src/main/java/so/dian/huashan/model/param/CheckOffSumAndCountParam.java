package so.dian.huashan.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckOffSumAndCountParam {
    /**
     *  付款公司
     */
    private Long paySubjectId;

    /**
     * 支付通道
     */
    private Integer source;

    /**
     * 核对开始日期
     */
    private Date tradeStartTime;
    /**
     * 核对结束日期
     */
    private Date tradeEndTime;
}
