package so.dian.huashan.model.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckDiffParam {
    /**
     * 状态
     */
    private Integer status;

    /**
     * 公司主体id
     */
    private Long paySubjectId;

    /**
     * 更新时间
     */
    private Long updateStartTime;

    /**
     * 更新时间
     */
    private Long updateEndTime;
}
