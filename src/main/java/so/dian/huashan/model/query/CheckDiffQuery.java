package so.dian.huashan.model.query;

import lombok.Data;

import java.util.Date;

@Data
public class CheckDiffQuery {
    /**
     *  付款公司
     */
    private String paySubjectId;
    /**
     * 来源
     */
    private Integer source;


    /**
     * 支付单流水号
     */
    private String tradeNo;

    /**
     * 支付通道交易流水号
     */
    private String channelTradeNo;

    /**
     * 付款主体
     */
    private Integer paySubjectType;

    /**
     * 交易开始时间（打款开始时间）
     */
    private Date tradeStartTime;
    /**
     * 交易结束时间（打款结束时间）
     */
    private Date tradeEndTime;

    /**
     *  状态  0-未平账，1-手动平账，2-不参与平账（对账结果）
     */
    private Integer status;
}
