package so.dian.huashan.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GetSummaryPageQuery {
    /**
     *  付款主体
     */
    private Integer paySubjectType;

    /**
     *  付款公司
     */
    private Long paySubjectId;

    /**
     * 支付通道
     */
    private Integer source;

    /**
     * 核对开始日期
     */
    private Integer checkStartDate;
    /**
     * 核对结束日期
     */
    private Integer checkEndDate;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;
}
