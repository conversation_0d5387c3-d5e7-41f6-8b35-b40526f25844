package so.dian.huashan.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ManualReconciliationDTO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 账单状态 0-未平账，1-手动平账，2-不参与平账
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
