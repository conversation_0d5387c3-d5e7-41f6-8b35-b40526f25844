package so.dian.huashan.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PullJobDTO implements Serializable {

    private ThirdpartyPayType payType;

    private String beginDate;

    /**
     * 申请单号
     */
    private String partnerOrderId;

    private String url;
}
