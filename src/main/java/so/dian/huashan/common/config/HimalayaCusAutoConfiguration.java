package so.dian.huashan.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import so.dian.himalaya.boot.aop.aspects.ChainLogAspect;
import so.dian.himalaya.boot.aop.aspects.RTAspect;
import so.dian.himalaya.boot.aop.aspects.RequestLimitAspect;
import so.dian.himalaya.boot.controller.LoggerLevelController;
import so.dian.himalaya.boot.exception.GlobalExceptionHandler;
import so.dian.himalaya.boot.filter.StreamFilter;
import so.dian.himalaya.boot.properties.ApplicationProperties;
import so.dian.himalaya.boot.util.SpringBeanUtils;

@Configuration
public class HimalayaCusAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(SpringBeanUtils.class)
    public SpringBeanUtils springBeanUtils() {
        return new SpringBeanUtils();
    }

    @Bean
    @Order(2)
    @ConditionalOnMissingBean(ChainLogAspect.class)
    public ChainLogAspect chainLogAspect() {
        return new ChainLogAspect();
    }

    @Bean
    @Order(4)
    @ConditionalOnMissingBean(RequestLimitAspect.class)
    public RequestLimitAspect requestLimitAspect() {
        return new RequestLimitAspect();
    }

    @Bean
    @Order(3)
    @ConditionalOnMissingBean(RTAspect.class)
    public RTAspect rtAspect() {
        return new RTAspect();
    }

    @Bean
    @Order(4)
    @ConditionalOnMissingBean(StreamFilter.class)
    public StreamFilter streamFilter() {
        return new StreamFilter();
    }

    @Bean
    @ConditionalOnMissingBean(GlobalExceptionHandler.class)
    public GlobalExceptionHandler globalExceptionHandler() {
        return new GlobalExceptionHandler();
    }

    @Bean
    @ConditionalOnMissingBean(LoggerLevelController.class)
    public LoggerLevelController loggerLevelController() {
        return new LoggerLevelController();
    }

    @Bean
    @Order(1)
    @ConditionalOnMissingBean(ApplicationProperties.class)
    public ApplicationProperties applicationProperties() {
        return new ApplicationProperties();
    }
}
