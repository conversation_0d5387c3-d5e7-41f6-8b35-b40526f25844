package so.dian.huashan.common.config;

import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.http.AbstractHttpClient;
import com.wechat.pay.java.core.http.DefaultHttpClientBuilder;
import com.wechat.pay.java.service.billdownload.BillDownloadService;
import com.wechat.pay.java.service.profitsharing.ProfitsharingService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.common.enums.ThirdpartyPayType;


@Configuration
public class WeixinApiConfig {

    @Bean
    public Config weixinConfig() {
        ApiConfig apiConfig = ApiConfig.getConfig(ThirdpartyPayType.WEIXIN_PARTNER);
        return new RSAAutoCertificateConfig.Builder()
                        .merchantId(apiConfig.getParams().get("mch_id"))
                        .privateKey(apiConfig.getParams().get("privateKeyPath"))
                        .merchantSerialNumber(apiConfig.getParams().get("merchantSerialNumber"))
                        .apiV3Key(apiConfig.getParams().get("apiV3Key"))
                        .build();
    }

    @Bean
    public AbstractHttpClient weixinHttpClient(Config config) {
        return new DefaultHttpClientBuilder().config(config).build();
    }

    @Bean
    public ProfitsharingService profitsharingService(AbstractHttpClient httpClient, Config config) {
        return new ProfitsharingService.Builder().httpClient(httpClient)
                .encryptor(config.createEncryptor()).decryptor(config.createDecryptor()).build();
    }

    @Bean
    public BillDownloadService billDownloadService(AbstractHttpClient httpClient, Config config) {
        return new BillDownloadService.Builder().httpClient(httpClient).decryptor(config.createDecryptor()).build();
    }
}
