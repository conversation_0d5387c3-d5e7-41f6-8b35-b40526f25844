package so.dian.huashan.common.config;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import so.dian.huashan.common.mapper.tiantai.PimSpuMapper;
import so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/10/24 17:56
 * @description:
 */
@Slf4j
@Configuration
public class CacheConfig {

    @Autowired
    private PimSpuMapper pimSpuMapper;

    @Bean("spuCache")
    public LoadingCache<String, PimSpuDO> buildSpuCache() {

        CacheLoader<String, PimSpuDO> spuLoader = new CacheLoader<String, PimSpuDO>() {
            @Override
            public PimSpuDO load(String key) {
                List<PimSpuDO> pimSpuDOS = pimSpuMapper.selectByCodes(Collections.singletonList(key));
                return CollUtil.getFirst(pimSpuDOS);
            }

            @Override
            public Map<String, PimSpuDO> loadAll(Iterable<? extends String> keys) {
                log.info("SPU缓存加载，keys:{}", JSON.toJSONString(keys));
                List<String> spuCodes = Lists.newArrayList(keys);
                List<PimSpuDO> pimSpuDOS = pimSpuMapper.selectByCodes(spuCodes);
                return pimSpuDOS.stream().collect(Collectors.toMap(PimSpuDO::getSpuCode, Function.identity()));
            }
        };

        return CacheBuilder.newBuilder()
                .concurrencyLevel(1)
                .expireAfterAccess(1, TimeUnit.HOURS)
                .initialCapacity(64)
                .maximumSize(200)
                .recordStats()
                .build(spuLoader);
    }
}
