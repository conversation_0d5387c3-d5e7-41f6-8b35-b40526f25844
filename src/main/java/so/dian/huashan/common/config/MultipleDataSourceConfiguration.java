package so.dian.huashan.common.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Configuration;
import so.dian.huashan.framework.scan.MapperScans;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/10 10:05
 * @description:
 */
@Configuration
@MapperScans({
        @MapperScan(basePackages =
                {"so.dian.huashan.collection.mapper",
                        "so.dian.huashan.task.mapper",
                        "so.dian.huashan.check.mapper"},
                sqlSessionFactoryRef = "huashan"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.lhc"},
                sqlSessionFactoryRef = "lhc"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.newyork.sharding"},
                sqlSessionFactoryRef = "newyork-sharding"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.newyork.tidb"},
                sqlSessionFactoryRef = "newyork-tidb"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.customer"},
                sqlSessionFactoryRef = "customer"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.oss"},
                sqlSessionFactoryRef = "oss"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.shop"},
                sqlSessionFactoryRef = "shop"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.tiantai"},
                sqlSessionFactoryRef = "tiantai"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.emei"},
                sqlSessionFactoryRef = "emei"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.contract"},
                sqlSessionFactoryRef = "contract"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.ubud"},
                sqlSessionFactoryRef = "ubud"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.yandang"},
                sqlSessionFactoryRef = "yandang"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.polar"},
                sqlSessionFactoryRef = "polar"),
        @MapperScan(basePackages =
                {"so.dian.huashan.common.mapper.supplychain"},
                sqlSessionFactoryRef = "supplychain")
})
public class MultipleDataSourceConfiguration {
}
