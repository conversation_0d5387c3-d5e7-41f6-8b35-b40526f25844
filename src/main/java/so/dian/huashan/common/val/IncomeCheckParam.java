package so.dian.huashan.common.val;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.service.val.UseBaseParam;

import java.lang.reflect.Field;
import java.util.Objects;

@Setter
@Getter
public class IncomeCheckParam extends UseBaseParam {

    /**
     * 指定拉取三方账单日
     */
    private String billDate;

    /**
     * 指定拉取三方账单渠道
     */
    private String payType;

    /**
     * 指定查询哪个账单日的数据进行对账
     */
    private String checkDate;

    public IncomeCheckParam(String batchNo, String taskCode) {
        super(batchNo, taskCode);
    }

    public static IncomeCheckParam from(TaskBaseParam param) {
        if (Objects.isNull(param))
            return null;


        IncomeCheckParam checkParam = new IncomeCheckParam(param.getBatchNo(), param.getTaskCode());
        JSONObject jsonObject = JSON.parseObject(param.getUserParam());
        if (Objects.isNull(jsonObject))
            return checkParam;

        Field[] declaredFields = IncomeCheckParam.class.getDeclaredFields();
        for (Field declaredField : declaredFields) {
            ReflectUtil.setFieldValue(checkParam, declaredField.getName(), jsonObject.get(declaredField.getName()));
        }
        return checkParam;
    }
}
