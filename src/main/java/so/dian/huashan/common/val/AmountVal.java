package so.dian.huashan.common.val;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/04/01 11:31
 * @description:
 */
public class AmountVal {

    private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");

    private static final int DEFAULT_SCALE = 2;

    private Long amountFen;

    public AmountVal(Long amountFen) {
        this.amountFen = Objects.requireNonNull(amountFen, "amountFen must not null");
    }

    public BigDecimal formatYuan() {
        return new BigDecimal(amountFen).divide(ONE_HUNDRED, DEFAULT_SCALE, RoundingMode.HALF_UP);
    }
}
