package so.dian.huashan.common.facade.remote;

import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.agent.api.dto.request.QueryAgentConstant;
import so.dian.himalaya.common.entity.BizResult;

import java.util.List;

import static so.dian.huashan.common.enums.BizErrorCode.REMOTE_CALL_ERROR;

@FeignClient(name = "agent",fallbackFactory = AgentClient.AgentFallbackFactory.class)
public interface AgentClient {
    /**
     * 根据id获取agent
     */
    @RequestMapping(value = "/agent/getAgentById", method = RequestMethod.GET)
    BizResult<AgentDTO> getAgentById(@RequestParam("agentId") Long agentId);

    @RequestMapping(value = "/agent/getIdsByName", method = RequestMethod.GET)
    BizResult<List<Long>> getIdsByName(@RequestParam("agentName") String agentName, @RequestParam("agentType") Integer agentType);

    @RequestMapping(value = "/agent/getNameByIds", method = RequestMethod.GET)
    BizResult<List<AgentDTO>> getNameByIds(@RequestParam("agentIds") List<Long> agentIds);

    @RequestMapping(value = "/agent/getAgentByIdAndOptions", method = RequestMethod.POST)
    BizResult<AgentDTO> getAgentByIdAndOptions(@RequestParam("agentId") Integer agentId, @RequestBody QueryAgentConstant queryAgentConstant);

    @Slf4j
    @Service
    class AgentFallbackFactory implements FallbackFactory<AgentClient> {

        @Override
        public AgentClient create(Throwable throwable) {

            return new AgentClient() {

                @Override
                public BizResult<AgentDTO> getAgentById(Long agentId) {
                    log.error("agent getAgentById fallback, agentId:{}", agentId, throwable);
                    return BizResult.error(REMOTE_CALL_ERROR);
                }

                @Override
                public BizResult<List<Long>> getIdsByName(String agentName, Integer agentType) {
                    log.error("agent getIdsByName fallback, agentName:{},agentType:{}", agentName, agentType, throwable);
                    return BizResult.error(REMOTE_CALL_ERROR);
                }

                @Override
                public BizResult<List<AgentDTO>> getNameByIds(List<Long> agentIds) {
                    log.error("agent getIdsByName fallback, agentIds:{}", agentIds, throwable);
                    return BizResult.error(REMOTE_CALL_ERROR);
                }

                @Override
                public BizResult<AgentDTO> getAgentByIdAndOptions(Integer agentId, QueryAgentConstant queryAgentConstant) {
                    log.error("agent getAgentByIdAndOptions fallback,params:{},{}",agentId,queryAgentConstant,throwable);
                    return BizResult.error(REMOTE_CALL_ERROR);
                }
            };
        }
    }
}
