package so.dian.huashan.common.facade;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.common.facade.remote.DataCenterClient;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: ahuang
 * @CreateTime: 2025-05-30 15:23
 * @Description:
 */
@Slf4j
@Component
public class DataCenterFacade {

    @Resource
    private DataCenterClient dataCenterClient;

    public Map<String, BigDecimal> fetchDataByCode() {
        DataCenterClient.FetchDataByCodeRequest request = new DataCenterClient.FetchDataByCodeRequest();
        request.setReportCode("credit_bank_wallet_pay");
        Map<String, String> params = new HashMap<>();
        String yesterday = DateUtil.format(DateUtil.yesterday(), DatePattern.PURE_DATE_PATTERN);
        params.put("biz_date", yesterday);
        request.setParamMap(params);

        BizResult<DataCenterClient.FetchDataData> result = dataCenterClient.fetchDataByCode(request);
        if (Objects.isNull(result) || !result.isSuccess()) {
            return null;
        }
        return result.getData().getRows().stream().collect(Collectors.toMap(
                DataCenterClient.CreditBankWalletPayRow::getChannel_type, // key 是 channel_type
                DataCenterClient.CreditBankWalletPayRow::getTotal_pay_amount // value 是 total_pay_amount
        ));
    }
}
