package so.dian.huashan.common.facade;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.common.rpc.result.DianResult;
import so.dian.genesis.api.payment.dto.PaymentBaseInfo;
import so.dian.genesis.api.payment.req.PaymentAccountPageReq;
import so.dian.huashan.common.facade.remote.IotPaymentClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class IotPaymentFacade {

    @Autowired
    private IotPaymentClient iotPaymentClient;

    public List<PaymentBaseInfo> pagePaymentAccount(PaymentAccountPageReq paymentAccountPageReq){
        log.info("IotPaymentFacade pagePaymentAccount paymentAccountPageReq:{}",JSON.toJSON(paymentAccountPageReq));
        DianResult<List<PaymentBaseInfo>> result = iotPaymentClient.pagePaymentAccount(paymentAccountPageReq);
        log.info("IotPaymentFacade pagePaymentAccount result:{}",JSON.toJSON(result));
        if (Objects.isNull(result)|| !result.getSuccess()){
            log.error("获取二级商户失败paymentAccountPageReq:{},result:{}", JSON.toJSON(paymentAccountPageReq),JSON.toJSON(result));
            return null;
        }
        return result.getData();
    }
}
