package so.dian.huashan.common.facade.remote;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.fis.lushan.api.req.JobLogSaveReq;
import so.dian.himalaya.common.entity.BizResult;

import static so.dian.huashan.common.enums.BizErrorCode.REMOTE_CALL_ERROR;

/**
 * LushanClient
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 16:50
 */
@FeignClient(name = "lushan", fallbackFactory = LushanClient.LushanClientFallback.class)
public interface LushanClient {

    @PostMapping({"/log/job/report"})
    BizResult<Long> reportJob(@RequestBody JobLogSaveReq saveReq);

    @Slf4j
    @Component
    class LushanClientFallback implements FallbackFactory<LushanClient> {

        @Override
        public LushanClient create(Throwable cause) {
            return new LushanClient() {
                @Override
                public BizResult<Long> reportJob(JobLogSaveReq saveReq) {
                    log.error("reportJob LushanClient 异常，请求参数:{}", JSONObject.toJSONString(saveReq));
                    return BizResult.error(REMOTE_CALL_ERROR);
                }
            };
        }
    }
}
