package so.dian.huashan.common.facade.remote;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;
import so.dian.mail.vo.MailInfoVo;

/**
 * @Author: ahuang
 * @CreateTime: 2025-03-05 10:55
 * @Description:
 */
@FeignClient(name = "dove")
public interface DoveClient {

    @RequestMapping(
            path = {"/mail/send_mail"},
            method = {RequestMethod.POST}
    )
    BizResult<Boolean> sendMail(MailInfoVo var1);

}