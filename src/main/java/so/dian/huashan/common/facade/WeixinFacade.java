package so.dian.huashan.common.facade;

import com.wechat.pay.java.core.http.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.model.dto.BalanceDTO;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
@Slf4j
@Component
public class WeixinFacade {
    @Autowired
    private AbstractHttpClient httpClient;

    /**
     * 查询ISV余额
     */
    public BalanceDTO queryIsvBalance() {
        String requestPath = "https://api.mch.weixin.qq.com/v3/merchant/fund/balance/BASIC";
        HttpHeaders headers = new HttpHeaders();
        headers.addHeader(Constant.ACCEPT, MediaType.APPLICATION_JSON.getValue());
        headers.addHeader(Constant.CONTENT_TYPE, MediaType.APPLICATION_JSON.getValue());
        HttpRequest httpRequest =
                new HttpRequest.Builder()
                        .httpMethod(HttpMethod.GET)
                        .url(requestPath)
                        .headers(headers)
                        .build();
        try {
            HttpResponse<Map> execute = httpClient.execute(httpRequest, Map.class);
            Double availableAmount = (Double) execute.getServiceResponse().get("available_amount");
            Double pendingAmount = (Double) execute.getServiceResponse().get("pending_amount");
            return BalanceDTO.builder()
                    .availableBalance(availableAmount.longValue())
                    .unAvailableBalance(pendingAmount.longValue())
                    .build();
        } catch (Exception e){
            log.error("小电余额查询失败httpRequest:{}",httpRequest,e);
        }
        return null;
    }

    /**
     * 查询二级商户余额
     */
    public BalanceDTO queryBalance(Long merchantId) {
        if (Objects.isNull(merchantId)) {
            return null;
        }
        String requestPath = "https://api.mch.weixin.qq.com/v3/ecommerce/fund/balance/" + merchantId;
        HttpHeaders headers = new HttpHeaders();
        headers.addHeader(Constant.ACCEPT, MediaType.APPLICATION_JSON.getValue());
        headers.addHeader(Constant.CONTENT_TYPE, MediaType.APPLICATION_JSON.getValue());
        HttpRequest httpRequest =
                new HttpRequest.Builder()
                        .httpMethod(HttpMethod.GET)
                        .url(requestPath)
                        .headers(headers)
                        .build();
        try {
            HttpResponse<Map> execute = httpClient.execute(httpRequest, Map.class);
            Double availableAmount = (Double) execute.getServiceResponse().get("available_amount");
            Double pendingAmount = (Double) execute.getServiceResponse().get("pending_amount");
            String mchId = (String) execute.getServiceResponse().get("sub_mchid");
            return  BalanceDTO.builder()
                    .availableBalance(availableAmount.longValue())
                    .unAvailableBalance((pendingAmount.longValue()))
                    .merchantId(mchId)
                    .build();

        } catch (Exception e){
            log.error("二级商户余额查询失败merchantId:{},httpRequest:{}",merchantId,httpRequest,e);
        }
        return null;
    }
}
