package so.dian.huashan.common.facade;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.agent.api.dto.AgentDTO;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.common.facade.remote.AgentClient;

import java.util.Collections;
import java.util.List;
@Slf4j
@Component
public class AgentFacade {
    @Autowired
    private AgentClient agentClient;

    public List<AgentDTO> getByIds(List<Long> agentIds) {
        log.info("getByIds get agentIds:{}",agentIds);
        BizResult<List<AgentDTO>> result = agentClient.getNameByIds(agentIds);
        log.info("getByIds get result:{}",result);
        if (!result.isSuccess()) {
            log.error("get getByIds error.{}, result:{}", JSONObject.toJSONString(agentIds), result);
            return Collections.emptyList();
        }
        return result.getData();
    }

    public AgentDTO getAgentById(Long agentId) {
        log.info("getByIds get agentId:{}",agentId);
        BizResult<AgentDTO> result = agentClient.getAgentById(agentId);
        log.info("getByIds get result:{}",result);
        if (!result.isSuccess()) {
            log.warn("get getIdsByName agentId:{}, result:{}", agentId, result);
            return null;
        }
        return result.getData();
    }
}
