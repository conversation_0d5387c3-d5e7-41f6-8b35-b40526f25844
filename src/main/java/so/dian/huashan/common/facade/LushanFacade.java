package so.dian.huashan.common.facade;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.fis.lushan.api.enums.ReportLevelEnum;
import so.dian.fis.lushan.api.req.JobLogSaveReq;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.common.facade.remote.LushanClient;

import javax.annotation.Resource;
import java.util.Date;

/**
 * LushanFacade
 *
 * <AUTHOR>
 * @desc
 * @date 2023/4/25 16:58
 */
@Slf4j
@Component
public class LushanFacade {

    @Value("${huashan.biz.dingding.code}")
    private String code;

    @Resource
    private LushanClient lushanClient;

    public Long reportJob(JSONObject extraJson, String jobName) {
        return reportJob(extraJson, jobName, code);
    }

    public Long reportJob(JSONObject extraJson, String jobName, String funcCode) {
        JobLogSaveReq jobLogSaveReq = new JobLogSaveReq();
        jobLogSaveReq.setJobName(jobName);
        jobLogSaveReq.setReportTime(new Date());
        jobLogSaveReq.setFuncCode(funcCode);
        jobLogSaveReq.setReportLevel(ReportLevelEnum.INFO);
        jobLogSaveReq.setExtraJson(extraJson);
        BizResult<Long> result = lushanClient.reportJob(jobLogSaveReq);
        if (!result.isSuccess()) {
            log.error("get reportJob jobName:{},extraJson:{}, result:{}", jobName,extraJson,result);
            return null;
        }
        return result.getData();
    }
}
