package so.dian.huashan.common.facade;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.huashan.common.service.entity.ControlOrderBO;
import so.dian.huashan.framework.openTelemetry.OpenTelemetryTracer;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ControlOrderEsFacade {

    @Value("${huashan.biz.es.control.index}")
    private String controlIdex;

    @Autowired
    private RestHighLevelClient client;

    @OpenTelemetryTracer
    public List<ControlOrderBO> list(List<String> orderNos) {
        if (CollUtil.isEmpty(orderNos))
            return Collections.emptyList();

        SearchRequest request = new SearchRequest(controlIdex);
        request.source()
                .size(orderNos.size())
                .query(QueryBuilders.termsQuery("_id", orderNos))
                .fetchSource(new String[]{"order_no", "control_result"}, null);

        try {
            SearchResponse response = client.search(request, RequestOptions.DEFAULT);
            SearchHits searchHits = response.getHits();
            return Arrays.stream(searchHits.getHits()).map(hit -> JSON.parseObject(hit.getSourceAsString(), ControlOrderBO.class)).collect(Collectors.toList());
        }catch (Exception e) {
            log.error("ES查询发生异常", e);
            return Collections.emptyList();
        }
    }
}
