package so.dian.huashan.common.facade.remote;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.himalaya.common.entity.BizResult;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * @Author: ahuang
 * @CreateTime: 2025-05-30 14:41
 * @Description:
 */
@FeignClient(name = "deus-ex-machina", fallbackFactory = DataCenterClient.CreditReportClientFallback.class)
public interface DataCenterClient {

    @PostMapping("/query/fetchDataByCode")
    BizResult<FetchDataData> fetchDataByCode(@RequestBody FetchDataByCodeRequest request);

    @Slf4j
    @Component
    class CreditReportClientFallback implements FallbackFactory<DataCenterClient> {
        @Override
        public DataCenterClient create(Throwable cause) {
            return request -> {
                log.error("调用 fetchDataByCode 异常", cause);
                return null;
            };
        }
    }

    @Data
    class FetchDataByCodeRequest {
        private String reportCode;
        private Map<String, String> paramMap;
    }

    @Data
    class FetchDataData {
        private List<CreditBankWalletPayRow> rows;
    }

    @Data
    class CreditBankWalletPayRow {
        private String id;
        private String biz_date;
        private String channel_type;
        private BigDecimal total_pay_amount;
    }

    /**
     * 渠道类型枚举，用于安全访问 Map 中的 key
     */
    enum ChannelTypeEnum {
        XD_Wallet,
        IOT_Alipay
    }
}

