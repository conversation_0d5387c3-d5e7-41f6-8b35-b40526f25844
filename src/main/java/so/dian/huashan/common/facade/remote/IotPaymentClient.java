package so.dian.huashan.common.facade.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import so.dian.common.rpc.result.DianResult;
import so.dian.genesis.api.payment.dto.PaymentBaseInfo;
import so.dian.genesis.api.payment.req.PaymentAccountPageReq;

import java.util.List;

@FeignClient(name = "genesis",fallbackFactory = IotPaymentClient.IotPaymentClientFallback.class)
public interface IotPaymentClient {

    /**
     * 分页获取支付渠道的账号信息
     */
    @PostMapping(value = "/genesis/api/payment/account/page/get")
    DianResult<List<PaymentBaseInfo>> pagePaymentAccount(PaymentAccountPageReq paymentAccountPageReq);
    @Slf4j
    @Component
    class IotPaymentClientFallback implements FallbackFactory<IotPaymentClient> {

        @Override
        public IotPaymentClient create(Throwable cause) {
            return null;
        }
    }
}
