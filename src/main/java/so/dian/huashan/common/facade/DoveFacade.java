package so.dian.huashan.common.facade;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.common.rpc.exception.BizException;
import so.dian.commons.eden.entity.BizResult;
import so.dian.huashan.common.facade.remote.DoveClient;
import so.dian.mail.vo.MailInfoVo;

import javax.annotation.Resource;

/**
 * @Author: ahuang
 * @CreateTime: 2025-03-05 10:55
 * @Description:
 */
@Slf4j
@Component
public class DoveFacade {

    @Resource
    private DoveClient doveClient;

    public void sendMail(MailInfoVo info) {
        BizResult<Boolean> booleanBizResult = doveClient.sendMail(info);
        log.info("发邮件结果: " + booleanBizResult.toString());
        if (!booleanBizResult.isSuccess()) {
            throw new BizException("send email to {} failed", info.getTo());
        }
    }
}
