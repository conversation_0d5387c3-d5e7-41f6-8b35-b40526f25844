package so.dian.huashan.common.utils;

import cn.hutool.core.date.DatePattern;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.io.File;
import java.nio.file.Paths;
import java.util.Date;
/**
 * @Author: ahuang
 * @CreateTime: 2024-12-13 10:58
 * @Description: 处理第三方账单文件路径生成逻辑
 */
public class ThirdPartyBillPathUtil {

    // 一级目录（账户）
    private static final String ACCOUNT_DIRECTORY = "/account";

    // 扩展名
    private static final String FILE_CSV_EXT = ".csv";
    private static final String FILE_ZIP_EXT = ".zip";

    /**
     * 构建账单存储目录
     *
     * @param billDate 日期
     * @param payType  支付渠道类型
     * @return 目录路径
     */
    public static String getDirectory(String baseDir, Date billDate, ThirdpartyPayType payType) {
        String billDateStr = LocalDateUtils.format(billDate, DatePattern.PURE_DATE_FORMAT);
        String directoryPath = baseDir + ACCOUNT_DIRECTORY + "/" + billDateStr + "/"
                + payType.getChannel().name().toLowerCase() + "/" + payType.name().toLowerCase();

        // 确保目录存在
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        return directoryPath;
    }

    /**
     * 构建账单 CSV 文件路径
     *
     * @param baseDir   基础目录
     * @param billDate  日期
     * @param payType   支付渠道类型
     * @return 文件路径
     */
    public static String getCsvFilePath(String baseDir, Date billDate, ThirdpartyPayType payType) {
        return getDirectory(baseDir, billDate, payType) + "/" + getCsvFileName(payType, billDate);
    }

    /**
     * 构建账单 ZIP 文件路径
     *
     * @param baseDir   基础目录
     * @param billDate  日期
     * @param payType   支付渠道类型
     * @return 文件路径
     */
    public static String getZipFilePath(String baseDir, Date billDate, ThirdpartyPayType payType) {
        return getDirectory(baseDir, billDate, payType) + "/" + getZipFileName(payType, billDate);
    }

    /**
     * 获取 CSV 文件名
     *
     * @param payType  支付渠道类型
     * @param billDate 日期
     * @return 文件名 alipay_h5_20241210_1.csv
     */
    public static String getCsvFileName(ThirdpartyPayType payType, Date billDate) {
        String billDateStr = LocalDateUtils.format(billDate, DatePattern.PURE_DATE_FORMAT);
        return payType.name().toLowerCase() + "_" + billDateStr + FILE_CSV_EXT;
    }

    /**
     * 获取 ZIP 文件名
     *
     * @param payType  支付渠道类型
     * @param billDate 日期
     * @return 文件名 alipay_h5_20241210_1.zip
     */
    public static String getZipFileName(ThirdpartyPayType payType, Date billDate) {
        String billDateStr = LocalDateUtils.format(billDate, DatePattern.PURE_DATE_FORMAT);
        return payType.name().toLowerCase() + "_" + billDateStr + FILE_ZIP_EXT;
    }

    /**
     * 获取alipay 上传sftp目录
     * @param baseDir
     * @param billDate
     * @return
     */
    public static String getSftpDirectoryPath(String baseDir, String billDate) {
        return Paths.get(baseDir, "sftp", "alipay", billDate).toString();
    }
}
