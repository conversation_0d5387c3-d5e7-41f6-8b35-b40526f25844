package so.dian.huashan.common.utils;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2016-05-19
 */
public class Checker {

    private static final Pattern PATTERN_MOBILE = Pattern.compile("^[1][3,4,5,7,8][0-9]{9}$");
    private static final Pattern PATTERN_URI_FOR_WEB = Pattern
            .compile("^http://[0-9a-zA-Z_/\\.%-]+\\??[0-9a-zA-Z_=%&-]*$" +
                    "|^https://[0-9a-zA-Z_/\\.%]+\\??[0-9a-zA-Z_=%&-]*$" +
                    "|^[0-9a-zA-Z_/\\.%]+\\??[0-9a-zA-Z_=%&-]*$");
    private static final Pattern PATTERN_URI_FOR_APP = Pattern
            .compile("^imlifer://[0-9a-zA-Z_/\\.%]+\\??[0-9a-zA-Z_=%&]*$");
    private static final Pattern PATTERN_COLOR_RGB_HEX = Pattern.compile("^#[0-9A-Fa-f]{6}$");
    private static final Pattern PATTERN_CLIENT_VERSION = Pattern.compile("^[0-9]+\\.[0-9]+\\.[0-9]+$");

    public static boolean isEmpty(String str) {
        return StringUtils.isEmpty(str);
    }

    public static boolean isBlank(String str) {
        return StringUtils.isBlank(str);
    }

    public static boolean isEmpty(Collection collection) {
        return collection == null || collection.isEmpty();
    }

    public static boolean isNull(Object obj) {
        return obj == null;
    }

    public static boolean inArray(Object target, Object... array) {
        if (target == null || array == null || array.length <= 0) {
            return false;
        }

        for (Object obj : array) {
            if (target.equals(obj)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param min >= min
     * @param max <= max
     */
    public static boolean inRange(int target, Integer min, Integer max) {
        if (min == null && max == null) {
            return false;
        }

        if (min != null && target < min) {
            return false;
        }
        if (max != null && target > max) {
            return false;
        }

        return true;
    }

    public static boolean inRange(long target, Long min, Integer max) {
        if (min == null && max == null) {
            return false;
        }

        if (min != null && target < min) {
            return false;
        }
        if (max != null && target > max) {
            return false;
        }

        return true;
    }

    public static boolean isMobile(String mobile) {
        if (isBlank(mobile)) {
            return false;
        }

        Matcher m = PATTERN_MOBILE.matcher(mobile);
        return m.matches();
    }

    /**
     * 检查参数是否整数
     */
    public static boolean isPositive(Number n) {
        isNull(n);
        long l = 0;
        if (n instanceof Short) {
            l = n.shortValue();
        } else if (n instanceof Integer) {
            l = n.intValue();
        } else if (n instanceof Long) {
            l = n.longValue();
        }

        return l >= 1;
    }

    /**
     * static1.kongge.com/kongge/plpwnsbd.html?h5src=22_2_1 http://static1.kongge.com/kongge/plpwnsbd.html?h5src=22_2_1
     * https://static1.kongge.com/kongge/plpwnsbd.html?h5src=22_2_1
     */
    public static boolean isUri4Web(String uri) {
        if (isBlank(uri)) {
            return false;
        }

        Matcher m = PATTERN_URI_FOR_WEB.matcher(uri);
        return m.matches();
    }

    /**
     * imlifer://detail/561050?src=26_430100_108_03
     */
    public static boolean isUri4App(String uri) {
        if (isBlank(uri)) {
            return false;
        }

        Matcher m = PATTERN_URI_FOR_APP.matcher(uri);
        return m.matches();
    }

    /**
     * 是否红绿蓝颜色值 (RGB) 的十六进制 (hex) 表示法 例如#000000
     */
    public static boolean isColorRGBHex(String color) {
        if (isBlank(color)) {
            return false;
        }

        Matcher m = PATTERN_COLOR_RGB_HEX.matcher(color);
        return m.matches();
    }

    public static boolean isJSONString(String jsonString) {
        try {
            if (isBlank(jsonString)) {
                return false;
            }
            JSON.parseObject(jsonString);
            return true;
        } catch (Exception e) {
            // ignore
        }
        return false;
    }

    public static boolean isContainAny(String str, String any) {
        return StringUtils.containsAny(str, any);
    }

    public static boolean isClientVersion(String clientVersion) {
        if (isBlank(clientVersion)) {
            return false;
        }
        Matcher m = PATTERN_CLIENT_VERSION.matcher(clientVersion);
        return m.matches();
    }
}

