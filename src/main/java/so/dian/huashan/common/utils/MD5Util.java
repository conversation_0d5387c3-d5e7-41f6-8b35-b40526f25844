package so.dian.huashan.common.utils;
//
//import sun.misc.BASE64Decoder;
//import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

/**
 * 类MD5Util的实现描述，来源于hera
 * <AUTHOR>
 */
public class MD5Util {

    private final static String[] hexDigits = {"0", "1", "2", "3", "4", "5", "6", "7",
            "8", "9", "a", "b", "c", "d", "e", "f"};

    /***
     * MD5加码 生成32位md5码（小写字母）
     */
    public static String string2MD5(String inStr) throws Exception {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] byteArray = inStr.getBytes();

        byte[] md5Bytes = md5.digest(byteArray);
        StringBuilder hexValue = new StringBuilder();
        for (byte md5Byte : md5Bytes) {
            int val = ((int) md5Byte) & 0xff;
            if (val < 16) {
                hexValue.append("0");
            }
            hexValue.append(Integer.toHexString(val));
        }
        return hexValue.toString();

    }

//    /**
//     * Base64加密
//     */
//    public static String getBase64(String str) {
//        byte[] b = str.getBytes(StandardCharsets.UTF_8);
//        return new BASE64Encoder().encode(b);
//    }
//
//    /**
//     * Base64编码
//     */
//    public static String getBase64(byte[] b) {
//        String result = null;
//        if (b != null) {
//            result = new BASE64Encoder().encode(b);
//        }
//        return result;
//    }
//
//    /**
//     * Base64解密
//     */
//    public static String getFromBase64(String str) throws IOException {
//        String result = null;
//        if (str != null) {
//            BASE64Decoder decoder = new BASE64Decoder();
//            byte[] b = decoder.decodeBuffer(str);
//            result = new String(b, StandardCharsets.UTF_8);
//        }
//        return result;
//    }
//
//    public static byte[] decodeBase64(String str) throws IOException {
//        if (str != null) {
//            BASE64Decoder decoder = new BASE64Decoder();
//            return decoder.decodeBuffer(str);
//        }
//        return null;
//    }

    /**
     * MD5数字签名(大写字母)
     */
    public static String md5Digest(String src) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] b = md.digest(src.getBytes(StandardCharsets.UTF_8));

        StringBuilder sb = new StringBuilder();
        for (byte value : b) {
            String s = Integer.toHexString(value & 0xFF);
            if (s.length() == 1) {
                sb.append("0");
            }
            sb.append(s.toUpperCase());
        }
        return sb.toString();
    }

    /**
     * 转换字节数组为16进制字串
     *
     * @param b 字节数组
     * @return 16进制字串
     */
    public static String byteArrayToHexString(byte[] b) {
        StringBuilder resultSb = new StringBuilder();
        for (byte aB : b) {
            resultSb.append(byteToHexString(aB));
        }
        return resultSb.toString();
    }

    /**
     * 转换byte到16进制
     *
     * @param b 要转换的byte
     * @return 16进制格式
     */
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0) {
            n = 256 + n;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    /**
     * MD5编码
     *
     * @param origin 原始字符串
     * @return 经过MD5加密之后的结果
     */
    public static String MD5Encode(String origin) {
        String resultString = null;
        try {
            resultString = origin;
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(resultString.getBytes(StandardCharsets.UTF_8));
            resultString = byteArrayToHexString(md.digest());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultString;
    }
}
