package so.dian.huashan.common.utils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.IntFunction;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class ListUtil {

    public static <T, R> List<R> toList(List<T> sourceList, Function<? super T, ? extends R> mapper) {
        return sourceList.stream().map(mapper).collect(Collectors.toList());
    }

    public static <T, R> R[] toArray(List<T> sourceList, IntFunction<R[]> generator) {
        return sourceList.stream().toArray(generator);
    }

    public static <T, K> Map<K, T> toMap(List<T> sourceList, Function<? super T, ? extends K> keyMapper) {
        return sourceList.stream().collect(Collectors.toMap(keyMapper, Function.identity()));
    }

    public static <T, K> Map<K, List<T>> groupBy(List<T> sourceList, Function<? super T, ? extends K> keyMapper) {
        return sourceList.stream().collect(Collectors.groupingBy(keyMapper));
    }

    public static <T>List<T> filter(List<T> sourceList, Predicate<? super T> predicate) {
        return sourceList.stream().filter(predicate).collect(Collectors.toList());
    }

    public static <T>List<T> distinct(List<T> sourceList) {
        return sourceList.stream().distinct().collect(Collectors.toList());
    }
}
