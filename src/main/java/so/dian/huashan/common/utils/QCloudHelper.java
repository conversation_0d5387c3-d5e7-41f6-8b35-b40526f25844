package so.dian.huashan.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.meta.InsertOnly;
import com.qcloud.cos.request.UploadFileRequest;
import com.qcloud.cos.request.UploadSliceFileRequest;
import com.qcloud.cos.sign.Credentials;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.common.enums.BizErrorCodeEnum;

/**
 * 腾讯云存储
 *
 * <AUTHOR>
 * @date 2019/11/5 3:38 PM
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Slf4j
public class QCloudHelper {

    public static final String  COS_QCLOUD   = "http://img3.dian.so";

    private static long         appId        = 1255721034;

    private static String       secretId     = "AKIDljlp1EJqUhwQ0dI62yuATmWLLMwalqaL";

    private static String       secretKey    = "VfwJdFtGA9JUfq2dHvbetGykXKA3xtqo";

    private static String       bucketName   = "lhc-image";

    // 初始化秘钥信息
    private static Credentials  cred         = new Credentials(appId, secretId, secretKey);

    private static ClientConfig clientConfig = new ClientConfig();

    static {
        clientConfig.setRegion("sh");
    }

    // 初始化cosClient
    private static COSClient cosClient = new COSClient(clientConfig, cred);

    /**
     * https://cloud.tencent.com/document/product/436/6273 查看参数说明 上传文件(将内存数据上传到COS)
     *
     * @param localPath 本地文件路径
     * @param objectName 上传的文件名
     */
    public static BizResult<String> uploadFile(String localPath, String objectName) {

        try {
            // cos路径, 必须从bucket下的根/开始，文件路径不能以/结尾, 例如 /mytest/qianshan.txt
            UploadFileRequest request = new UploadFileRequest(bucketName, objectName, localPath);

            // 如果COS上已有文件, 则进行覆盖(默认不覆盖)
            request.setInsertOnly(InsertOnly.OVER_WRITE);

            String fileRet = cosClient.uploadFile(request);

            log.info("本地文件上传:{},腾讯云接口返回：{}", localPath, fileRet);

            if (StringUtils.isNotBlank(fileRet)) {
                JSONObject jsonObject = JSON.parseObject(fileRet);

                if (jsonObject != null && jsonObject.get("code").equals(0)) {
                    return BizResult.create(COS_QCLOUD + objectName);
                } else {
                    return BizResult.error(BizErrorCodeEnum.BILL_FILE_COS_ERROR, fileRet);
                }
            }

            return BizResult.error(BizErrorCodeEnum.BILL_FILE_COS_ERROR, "上传文件至腾讯云，腾讯云接口返回结果为空。");
        } catch (Exception e) {
            log.error("上传文件至腾讯云时出现异常,localPath:{}", localPath, e);
            return BizResult.error(BizErrorCodeEnum.BILL_FILE_COS_ERROR, e.getMessage());
        }
    }

    // https://cloud.tencent.com/document/product/436/6273 查看参数说明
    // 分片上传文件(将内存数据上传到COS)
    public static BizResult<String> uploadSliceFile(String localPath, String objectName) {

        try {
            // cos路径, 必须从bucket下的根/开始，文件路径不能以/结尾, 例如 /mytest/qianshan.txt
            UploadFileRequest request = new UploadFileRequest(bucketName, objectName, localPath);
            UploadSliceFileRequest sliceRequest = new UploadSliceFileRequest(request);

            // 如果COS上已有文件, 则进行覆盖(默认不覆盖)
            sliceRequest.setInsertOnly(InsertOnly.OVER_WRITE);

            log.info("开始分片上传本地账单文件:{}", localPath);

            String fileRet = cosClient.uploadSliceFile(sliceRequest);

            log.info("本地文件分片上传:{},腾讯云接口返回：{}", localPath, fileRet);

            if (StringUtils.isNotBlank(fileRet)) {
                JSONObject jsonObject = JSON.parseObject(fileRet);

                if (jsonObject != null && jsonObject.get("code").equals(0)) {
                    return BizResult.create(COS_QCLOUD + objectName);
                } else {
                    return BizResult.error(BizErrorCodeEnum.BILL_FILE_COS_ERROR, fileRet);
                }
            }

            return BizResult.error(BizErrorCodeEnum.BILL_FILE_COS_ERROR, "上传文件至腾讯云，腾讯云接口返回结果为空。");
        } catch (Exception e) {
            log.error("上传文件至腾讯云时出现异常,localPath:{}", localPath, e);
            return BizResult.error(BizErrorCodeEnum.BILL_FILE_COS_ERROR, e.getMessage());
        }
    }

    public static void main(String[] args) {
        //String fileName = "/Users/<USER>/bill/20191001/wechat/85/wechat_85_20191001.csv";
        //String objectName = "/bill/20191001/wechat/85/wechat_85_20191001.csv";

        String fileName = "/Users/<USER>/Desktop/bill_payment_order_template.xlsx";
        String objectName = "/bill/qianshan/template/bill_payment_order_template.xlsx";

        String localPathDown = "/Users/<USER>/Downloads/bill/20191001/wechat/85/wechat_85_20191001.csv";
//        if (!new File(fileName).exists()) {
//            new File("/Users/<USER>/Downloads/bill/20191001/wechat/85").mkdirs();
//        }

        long start = System.currentTimeMillis();
        // uploadFile(fileName, objectName);
        uploadSliceFile(fileName, objectName);
        System.out.println("====>" + (System.currentTimeMillis() - start) + "ms");

        // downloadFile(objectName, localPathDown);
    }
}
