package so.dian.huashan.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 文件压缩工具类
 *
 * @Author: shenhe
 * @Date: 2019-10-22
 */
@Slf4j
public class ZipUtil {
    public static void zipDirectory(String sourceDirPath, String zipFilePath, List<String> fileExtensions) throws IOException {
        File sourceDir = new File(sourceDirPath);

        if (!sourceDir.exists() || !sourceDir.isDirectory()) {
            throw new IllegalArgumentException("Source directory does not exist or is not a directory.");
        }

        // 检查目录是否为空（包括后缀过滤的文件）
        File[] filteredFiles = sourceDir.listFiles(file -> file.isDirectory() || matchesExtension(file, fileExtensions));
        if (filteredFiles == null || filteredFiles.length == 0) {
            log.warn(">>> 目录为空或没有符合条件的文件，未生成 ZIP 文件: {}", sourceDirPath);
            return; // 直接结束，不生成 ZIP 文件
        }

        try (FileOutputStream fos = new FileOutputStream(zipFilePath);
             ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(fos))) {

            // 遍历目录中的所有文件和子目录，递归添加到 ZIP 中
            addDirectoryToZip(sourceDir, sourceDir, zos, fileExtensions);
            log.info(">>> 目录 {} 压缩完成，输出文件: {}", sourceDirPath, zipFilePath);
        } catch (IOException e) {
            log.error("压缩目录 {} 到 {} 时发生异常", sourceDirPath, zipFilePath, e);
            throw e;
        }
    }

    private static void addDirectoryToZip(File rootDir, File sourceDir, ZipOutputStream zos, List<String> fileExtensions) throws IOException {
        File[] files = sourceDir.listFiles();

        if (files == null || files.length == 0) {
            // 如果目录为空，在 ZIP 文件中创建一个空目录条目
            String entryName = getEntryName(rootDir, sourceDir) + "/";
            zos.putNextEntry(new ZipEntry(entryName));
            zos.closeEntry();
        } else {
            for (File file : files) {
                if (file.isDirectory()) {
                    // 递归处理子目录
                    addDirectoryToZip(rootDir, file, zos, fileExtensions);
                } else if (matchesExtension(file, fileExtensions)) {
                    // 添加匹配后缀的文件到 ZIP
                    addFileToZip(rootDir, file, zos);
                }
            }
        }
    }

    private static boolean matchesExtension(File file, List<String> fileExtensions) {
        if (fileExtensions == null || fileExtensions.isEmpty()) {
            return true; // 不过滤文件
        }
        String fileName = file.getName().toLowerCase();
        for (String ext : fileExtensions) {
            if (fileName.endsWith(ext.toLowerCase())) {
                return true;
            }
        }
        return false;
    }

    private static void addFileToZip(File rootDir, File file, ZipOutputStream zos) throws IOException {
        String entryName = getEntryName(rootDir, file);
        zos.putNextEntry(new ZipEntry(entryName));

        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = bis.read(buffer)) > 0) {
                zos.write(buffer, 0, length);
            }
        } finally {
            zos.closeEntry();
        }
    }

    private static String getEntryName(File rootDir, File file) {
        // 获取 ZIP 中的相对路径
        return rootDir.toURI().relativize(file.toURI()).getPath();
    }

    /**
     * 压缩文件解压到指定目录
     *
     * @param zipFileName
     * @param targetPath
     * @throws Exception
     */
    public static void unzip(String zipFileName, String targetPath, Charset charset) throws Exception {
        inputStreamZip(FileUtil.getInputStream(zipFileName), targetPath, charset);
    }

    /**
     * 获取文件名：除扩展名外
     *
     * @param filename
     * @return
     */
    public static String getFileNameNoEx(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length()))) {
                return filename.substring(0, dot);
            }
        }
        return filename;
    }

    /**
     * 文件流解压到指定目录
     *
     * @param result
     * @param targetPath
     * @param charset
     * @throws Exception
     */
    public static void inputStreamZip(InputStream result, String targetPath, Charset charset) throws Exception {

        ZipInputStream zis = new ZipInputStream(result, charset);

        try {
            FileOutputStream fos;
            BufferedOutputStream bos;
            ZipEntry zipEntry;

            while ((zipEntry = zis.getNextEntry()) != null) {
                File temp = new File(targetPath + File.separator + zipEntry.getName());
                if (zipEntry.isDirectory()) {
                    temp.mkdirs();
                    continue;
                }

                fos = new FileOutputStream(temp);
                bos = new BufferedOutputStream(fos);

                int len = 0;
                byte[] bytes = new byte[1024];
                while ((len = zis.read(bytes)) != -1) {
                    bos.write(bytes, 0, len);
                }
                IoUtil.close(bos);
                IoUtil.close(fos);
                zis.closeEntry();
            }
        } catch (Exception e) {
            log.error("解压文件时出现异常，targetPath:{}", targetPath);
            throw e;
        } finally {
            IoUtil.close(zis);
        }
    }

    public static File getFileByExt(String directory, String extName) {
        List<File> fileList = FileUtil.loopFiles(directory);
        if (CollectionUtils.isNotEmpty(fileList)) {
            for (File file : fileList) {
                if (file.getName().contains(extName)) {
                    return file;
                }
            }
        }

        return null;
    }

    public static boolean renameFileName(File file, String newFileName) {
        File newFile = new File(newFileName);
        file.renameTo(newFile);
        return true;
    }

    public static String getFileExtName(String fileName) {
        return fileName.substring(fileName.lastIndexOf("."), fileName.length());
    }

    public static void unZipFile(String zipFilePath, String descFilePath, Charset charset) throws Exception {
        ZipFile zipFile = new ZipFile(zipFilePath, charset);// 此处可自动识别编码

        try {
            Enumeration<?> emu = zipFile.entries();

            BufferedInputStream bis;
            FileOutputStream fos;
            BufferedOutputStream bos;

            ZipEntry entry;

            while (emu.hasMoreElements()) {
                entry = (ZipEntry) emu.nextElement();

                File temp = new File(descFilePath + File.separator + entry.getName());
                if (entry.isDirectory()) {
                    temp.mkdirs();
                    continue;
                }

                bis = new BufferedInputStream(zipFile.getInputStream(entry));
                fos = new FileOutputStream(temp);
                bos = new BufferedOutputStream(fos);

                int len = 0;
                byte[] bytes = new byte[1024];
                while ((len = bis.read(bytes)) != -1) {
                    bos.write(bytes, 0, len);
                }

                IoUtil.close(bos);
                IoUtil.close(fos);
                IoUtil.close(bis);
            }
        } catch (Exception e) {
            log.error("解压文件时出现异常，zipFilePath:{},descFilePath:{}", zipFilePath, descFilePath);
            throw e;
        } finally {
            zipFile.close();
        }
    }

    public static void unGzip(String gzipFilePath, String descFilePath) throws Exception {
        byte[] unGzip = cn.hutool.core.util.ZipUtil.unGzip(Files.newInputStream(Paths.get(gzipFilePath)));
        IoUtil.write(Files.newOutputStream(Paths.get(descFilePath)), Boolean.TRUE, unGzip);
    }

    public static void unGzip(InputStream zipIs, String descFilePath) throws Exception {
        byte[] unGzip = cn.hutool.core.util.ZipUtil.unGzip(zipIs);
        IoUtil.write(Files.newOutputStream(Paths.get(descFilePath)), Boolean.TRUE, unGzip);
    }
}
