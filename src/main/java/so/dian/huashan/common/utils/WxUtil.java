package so.dian.huashan.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;

/**
 * 类WxUtil的实现描述：来源与hera
 *
 * <AUTHOR>
 */
@Slf4j
public class WxUtil {

    public static String sign(Map<String, String> map, String signKey) {
        String paraStr = UrlUtil.createQueryString(map) + "&key=" + signKey;
        return MD5Util.MD5Encode(paraStr).toUpperCase();
    }

    public static String signSHA256(Map<String, String> map, String signKey) throws Exception {
        String paraStr = UrlUtil.createQueryString(map) + "&key=" + signKey;
        return SHA256Util.HMACSHA256(paraStr,signKey);
    }

    public static String toXml(Map<String, String> map) {
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        for (String key : map.keySet()) {
            sb.append("<");
            sb.append(key);
            sb.append(">");
            sb.append(map.get(key));
            sb.append("</");
            sb.append(key);
            sb.append(">");
        }
        sb.append("</xml>");
        return sb.toString();
    }


    /**
     * 从xml中解析需要的参数
     */
    public static Map<String, String> parseXML(String xmlStr) throws Exception {
        StringReader sr = new StringReader(xmlStr);
        InputSource is = new InputSource(sr);
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(is);
        // 获取到document里面的全部结点
        NodeList allNodes = document.getFirstChild().getChildNodes();
        Node node;
        Map<String, String> map = new HashMap<>();
        int i = 0;
        while (i < allNodes.getLength()) {
            node = allNodes.item(i);
            if (node instanceof Element) {
                if (!Checker.isBlank(node.getTextContent())) {
                    map.put(node.getNodeName(), node.getTextContent());
                }
            }
            i++;
        }
        return map;
    }
}
