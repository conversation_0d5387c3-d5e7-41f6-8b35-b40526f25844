package so.dian.huashan.common.utils;

import org.springframework.aop.framework.Advised;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.aop.framework.AopProxy;
import org.springframework.aop.support.AopUtils;

import java.lang.reflect.Field;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2024/01/02 18:44
 * @description:
 */
public class AopProxyUtils {

    /**
     * 获取一个代理实例的真实对象
     */
    public static <T> T getTarget(Object proxy) throws Exception {
        if (!AopUtils.isAopProxy(proxy)) {
            return (T) proxy;
        }
        if (AopUtils.isJdkDynamicProxy(proxy)) {
            return (T) getJdkDynamicProxyTargetObject(proxy);
        } else {
            return (T) getCglibProxyTargetObject(proxy);
        }
    }

    private static Object getJdkDynamicProxyTargetObject(Object proxy) throws Exception {
        Field h = proxy.getClass().getSuperclass().getDeclaredField("h");
        h.setAccessible(true);
        AopProxy aopProxy = (AopProxy) h.get(proxy);
        Field advised = aopProxy.getClass().getDeclaredField("advised");
        advised.setAccessible(true);
        return ((AdvisedSupport) advised.get(aopProxy)).getTargetSource().getTarget();
    }

    private static Object getCglibProxyTargetObject(Object proxy) throws Exception {
        Advised advised = (Advised) proxy;
        return advised.getTargetSource().getTarget();
    }

}
