package so.dian.huashan.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import org.springframework.core.io.ClassPathResource;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.common.enums.BizErrorCode;

import javax.net.ssl.SSLContext;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.security.KeyStore;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * 类HutoolUtilPost的实现描述：hutool post扩展
 *
 * <AUTHOR>
 */
@Slf4j
public class HutoolPostUtil extends HttpUtil {

    // 编码格式。发送编码格式统一用UTF-8
    private static final String ENCODING = "UTF-8";

    // 设置连接超时时间，单位毫秒。
    private static final int CONNECT_TIMEOUT = 6000;

    // 请求获取数据的超时时间(即响应时间)，单位毫秒。
    private static final int SOCKET_TIMEOUT = 6000;

    /**
     * 发送post请求并下载文件
     *
     * @param urlString 网址
     * @param body      post表单数据
     * @return 返回数据
     */
    public static long hutoolPostDownload(String urlString, String body, String destFile) {

        HttpResponse httpResponse = HttpRequest.post(urlString).body(body).execute();

        if (!httpResponse.isOk()) {
            throw new HttpException("Server response error with status code: [{}]", httpResponse.getStatus());
        }

        return httpResponse.writeBody(destFile);
    }
    
    /**
     * 发送post请求；带请求头和请求参数
     *
     * @param url      请求地址
     * @param destFile 文件流写出地址
     */
    public static Long getDownload(String url, String destFile) throws Exception {
        if(StringUtils.isEmpty(url)){
            return null;
        }
        // 创建httpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // 创建http对象
        HttpGet httpGet = new HttpGet(url);

        /**
         * setConnectTimeout：设置连接超时时间，单位毫秒。
         * setConnectionRequestTimeout：设置从connect Manager(连接池)获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
         * setSocketTimeout：请求获取数据的超时时间(即响应时间)，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
         */
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();

        httpGet.setConfig(requestConfig);

        CloseableHttpResponse httpResponse = null;
        try {
            // 执行请求并获得响应结果
            RemoteResponse remoteResponse = getHttpClientResult(destFile, httpClient, httpGet);
            httpResponse = remoteResponse.getHttpResponse();
            return remoteResponse.getTransforByteCount();
        } finally {
            // 释放资源
            release(httpResponse, httpClient);
        }
    }

    /**
     * 发送post请求；带请求头和请求参数
     *
     * @param url      请求地址
     * @param destFile 文件流写出地址
     * @param body     请求参数
     * @return
     * @throws Exception
     */
    public static Long postDownload(String url, String body, String destFile) throws Exception {

        // 创建httpClient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();

        // 创建http对象
        HttpPost httpPost = new HttpPost(url);

        /**
         * setConnectTimeout：设置连接超时时间，单位毫秒。
         * setConnectionRequestTimeout：设置从connect Manager(连接池)获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
         * setSocketTimeout：请求获取数据的超时时间(即响应时间)，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
         */
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();

        httpPost.setConfig(requestConfig);

        // 封装请求参数
        StringEntity entity = new StringEntity(body, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);

        CloseableHttpResponse httpResponse = null;
        try {
            // 执行请求并获得响应结果
            RemoteResponse remoteResponse = getHttpClientResult(destFile, httpClient, httpPost);
            httpResponse = remoteResponse.getHttpResponse();
            return remoteResponse.getTransforByteCount();
        } finally {
            // 释放资源
            release(httpResponse, httpClient);
        }
    }

    /**
     * 发送post请求；带请求头和请求参数
     *
     * @param url      请求地址
     * @param destFile 文件流写出地址
     * @param body     请求参数
     * @param mchId    账户号
     * @return
     * @throws Exception
     */
    public static Long postDownload(String url, String body, String destFile,String mchId) throws Exception {

        char[] password = mchId.toCharArray();
        KeyStore keyStore = KeyStore.getInstance("PKCS12");

        /**
         * 配置证书
         */
        ClassPathResource resource = new ClassPathResource("cert/wechat/" + mchId + "/" + "apiclient_cert.p12");
        InputStream instream = resource.getInputStream();
        keyStore.load(instream, password);

        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, password).build();
        @SuppressWarnings("deprecation")
        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslcontext, new String[]{"TLSv1", "TLSv1.2"}, null,
                SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);

        /**
         * setConnectTimeout：设置连接超时时间，单位毫秒。
         * setConnectionRequestTimeout：设置从connect Manager(连接池)获取Connection 超时时间，单位毫秒。这个属性是新加的属性，因为目前版本是可以共享连接池的。
         * setSocketTimeout：请求获取数据的超时时间(即响应时间)，单位毫秒。 如果访问一个接口，多少时间内无法返回数据，就直接放弃此次调用。
         */
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(CONNECT_TIMEOUT).setSocketTimeout(SOCKET_TIMEOUT).build();
        // 创建httpClient对象
        CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).setSSLSocketFactory(sslsf)
                .build();
        // 创建http对象
        HttpPost httpPost = new HttpPost(url);
        // 封装请求参数
        StringEntity entity = new StringEntity(body, "utf-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);

        CloseableHttpResponse httpResponse = null;
        try {
            // 执行请求并获得响应结果
            RemoteResponse remoteResponse = getHttpClientResult(destFile, httpClient, httpPost);
            httpResponse = remoteResponse.getHttpResponse();
            return remoteResponse.getTransforByteCount();
        } finally {
            // 释放资源
            release(httpResponse, httpClient);
        }
    }

    /**
     * Description: 封装请求头
     *
     * @param params
     * @param httpMethod
     */
    public static void packageHeader(Map<String, String> params, HttpRequestBase httpMethod) {
        // 封装请求头
        if (params != null) {
            Set<Entry<String, String>> entrySet = params.entrySet();
            for (Entry<String, String> entry : entrySet) {
                // 设置到请求头到HttpRequestBase对象中
                httpMethod.setHeader(entry.getKey(), entry.getValue());
            }
        }
    }

    /**
     * Description: 封装请求参数
     *
     * @param params
     * @param httpMethod
     * @throws UnsupportedEncodingException
     */
    public static void packageParam(Map<String, String> params,
                                    HttpEntityEnclosingRequestBase httpMethod) throws UnsupportedEncodingException {
        // 封装请求参数
        if (params != null) {
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            Set<Entry<String, String>> entrySet = params.entrySet();
            for (Entry<String, String> entry : entrySet) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }

            // 设置到请求的http对象中
            httpMethod.setEntity(new UrlEncodedFormEntity(nvps, ENCODING));
        }
    }

    /**
     * Description: 获得响应结果
     */
    public static RemoteResponse getHttpClientResult(String destFilePath,
                                           CloseableHttpClient httpClient,
                                           HttpRequestBase httpMethod) throws Exception {
        // 执行请求

        BufferedOutputStream outPutStream = null;
        InputStream inputStream = null;

        CloseableHttpResponse httpResponse = null;
        try {
            httpResponse = httpClient.execute(httpMethod);

            // 获取返回结果
            if (httpResponse != null && httpResponse.getStatusLine() != null) {

                inputStream = httpResponse.getEntity().getContent();

                File destFile = FileUtil.file(destFilePath);
                outPutStream = FileUtil.getOutputStream(destFile);

                long byteCount = IoUtil.copyByNIO(inputStream, outPutStream, IoUtil.DEFAULT_MIDDLE_BUFFER_SIZE, null);

                return RemoteResponse.builder().httpResponse(httpResponse).transforByteCount(byteCount).build();
            } else {
                log.info("调用接口拉取文件时出现异常,url:{}, httpResponse:{}", httpMethod.getURI(), httpResponse);
                throw BizException.create(BizErrorCode.BILL_FILE_CALL_ERROR);
            }

        } catch (Exception e) {
            log.error("调用接口拉取文件时出现异常,url:{}, httpResponse:{}", httpMethod.getURI(), httpResponse, e);
            throw e;
        } finally {
            IoUtil.close(outPutStream);
            IoUtil.close(inputStream);
        }
    }

    /**
     * Description: 释放资源
     *
     * @param httpResponse
     * @param httpClient
     * @throws IOException
     */
    public static void release(CloseableHttpResponse httpResponse, CloseableHttpClient httpClient) throws IOException {
        // 释放资源
        if (httpResponse != null) {
            httpResponse.close();
        }
        if (httpClient != null) {
            httpClient.close();
        }
    }


    @Builder
    @Getter
    public static class RemoteResponse {

        private CloseableHttpResponse httpResponse;

        // 传输的比特数
        private Long transforByteCount;
    }
}
