package so.dian.huashan.common.utils;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.check.controller.req.PayBillModifyReq;
import so.dian.huashan.model.dto.ManualReconciliationDTO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class TypeConversionUtils {

    public static <T> T convert(Object source, Class<T> targetClass) {
        if (source == null){
            return null;
        }
        BeanUtils.copyProperties(source,targetClass);
        return targetClass.cast(source);
    }

    public static <T> List<T> convertList(List<?> source, Class<T> targetClass) {
        if (CollectionUtils.isEmpty(source) || Objects.isNull(targetClass)) {
            return null;
        }

        return source.stream().map(item -> convert(item, targetClass)).collect(Collectors.toList());
    }
}
