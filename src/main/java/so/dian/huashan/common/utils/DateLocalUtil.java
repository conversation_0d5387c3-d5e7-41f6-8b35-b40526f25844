package so.dian.huashan.common.utils;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.time.DateUtils;
import so.dian.himalaya.util.LocalDateUtils;

import java.text.ParseException;
import java.util.List;

public class DateLocalUtil {
    private static String[] parsePatterns = {"yyyy-MM-dd","yyyy年MM月dd日",
            "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd",
            "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm"};

    public static String parseDate(String string) {
        if (string == null) {
            return null;
        }
        try {
            return JSON.toJSONString(DateUtils.parseDate(string, parsePatterns));
        } catch (ParseException e) {
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(parseDate("387421640"));
        List<Integer> intList = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8);
        List<List<Integer>> subs = ListUtils.partition(intList, 3);
        System.out.println(JSON.toJSONString(subs));
        System.out.println(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
    }
}
