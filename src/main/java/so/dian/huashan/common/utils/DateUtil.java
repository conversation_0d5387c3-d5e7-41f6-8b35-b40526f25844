package so.dian.huashan.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import so.dian.himalaya.util.LocalDateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@SuppressWarnings("deprecation")
public class DateUtil extends cn.hutool.core.date.DateUtil {

	/**
	 * 获取当前日期 ，格式类型yyyyMMdd
	 */
	public static String getCurrentDate() {
		return toMailDateDtPartString(getNow());
	}

	/**
	 * 获取当前日期 ，格式类型yyyyMMddHH
	 */
	public static String getCurrentDateHour() {
		return toMailDateHourDtPartString(getNow());
	}

	/**
	 * 获取当期时间，类型HHmmss
	 * 
	 * @return
	 */
	public static String getCurrentTime() {
		return toMailTimeTmPartString(getNow());
	}

	/**
	 * 获取当期时间MM月dd日HH:mm
	 * 
	 * @return
	 */
	public static String getCurrentMmDdHmTime() {
		return toMailDtmPart(getNow());
	}

	/**
	 * 获取当前日期和时间，格式yyyyMMddHHmmss
	 * 
	 * @return
	 */
	public static String getCurrentDateTime() {
		return toMailDateString(getNow());
	}

	/**
	 * 获取当前日期类型时间
	 */
	public static Date getNow() {
		return new Date();
	}


	// 获得某天最大时间 2017-10-15 23:59:59
	public static Date getEndOfDay(Date date) {
		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
		LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
		return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
	}

	// 获得某天最小时间 2017-10-15 00:00:00
	public static Date getStartOfDay(Date date) {
		LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
		LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
		return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
	}
	/**
	 * 获取当前时间戳
	 */
	public static long getNowTimestamp() {
		return getNow().getTime();
	}

	// ============================1.Date2String=====================================

	/**
	 * 将一个日期型转换为指定格式字串
	 * 
	 * @param aDate
	 * @param formatStr
	 * @return
	 */
	public static final String toFormatDateString(Date aDate, String formatStr) {
		if (aDate == null)
			return StringUtils.EMPTY;
		Assert.hasText(formatStr);
		return new SimpleDateFormat(formatStr).format(aDate);

	}

	/**
	 * 将一个日期型转换为'yyyy-MM-dd'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toShortDateString(Date aDate) {
		return toFormatDateString(aDate, SHORT_DATE_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyy-MM'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toShortMonthString(Date aDate) {
		return toFormatDateString(aDate, SHORT_MONTH_FORMAT);
	}
	/**
	 * 将一个日期型转换为'yyyyMM'格式字串
	 *
	 * @param aDate
	 * @return
	 */
	public static final String toShortMonthV2String(Date aDate) {
		return toFormatDateString(aDate, SHORT_MONTH_FORMATV2);
	}

	/**
	 * 将一个日期型转换为'yyyyMMdd'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toMailDateDtPartString(Date aDate) {
		return toFormatDateString(aDate, MAIL_DATE_DT_PART_FORMAT);
	}

	/**
	 * 将一个日期型字符串（yyyy-MM-dd HH:mm:ss）转换为'yyyyMMdd'格式字串
	 *
	 * @return
	 */
	public static final String toMailStrPartString(String aDateStr) {
		String str = null;
	    Date date = null;
		try {
			date = DateUtil.parseLongDateTmPartString(aDateStr);
		} catch (ParseException e) {
			return null;
		}
		return DateUtil.toMailDateDtPartString(date);
	}


	/**
	 * 将一个日期型转换为'yyyyMMddHH'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toMailDateHourDtPartString(Date aDate) {
		return toFormatDateString(aDate, MAIL_DATE_HOUR_DT_PART_FORMAT);
	}

	/**
	 * 将一个日期型转换为'HHmmss'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toMailTimeTmPartString(Date aDate) {
		return toFormatDateString(aDate, MAIL_TIME_TM_PART_FORMAT);
	}


	/**
	 * 将一个日期型转换为'yyyyMMddHHmmss'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toMailDateString(Date aDate) {
		return toFormatDateString(aDate, MAIL_DATE_FORMAT);
	}

	/**
	 * 
	 */
	/**
	 * 将一个日期型转换为MM月dd日HH:mm格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toMailDtmPart(Date aDate) {
		return toFormatDateString(aDate, MAIL_DATA_DTM_PART_FORMAT);
	}

	/**
	 * 
	 */
	/**
	 * 将一个日期型转换为yyyy.MM.dd格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toPointDtmPart(Date aDate) {
		return toFormatDateString(aDate, POINT_DATA_DTM_PART_FORMAT);
	}

	/**
	 * 将一个日期型转换为yyyy.MM.dd HH:mm:ss格式字串
	 *
	 * @param aDate
	 * @return
	 */
	public static final String toPointLongDtmPart(Date aDate) {
		return toFormatDateString(aDate, POINT_DATA_LONG_DTM_PART_FORMAT);
	}


	/**
	 * 将一个日期型转换为'yyyy-MM-dd HH:mm:ss'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toLongDateString(Date aDate) {
		return toFormatDateString(aDate, LONG_DATE_FORMAT);
	}

	/**
	 * 将一个日期型转换为'HH:mm:ss'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toLongDateTmPartString(Date aDate) {
		return toFormatDateString(aDate, LONG_DATE_TM_PART_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyy年MM月dd日'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toShortDateGBKString(Date aDate) {
		return toFormatDateString(aDate, SHORT_DATE_GBK_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyy年MM月dd日 HH时mm分'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toDateGBKString(Date aDate) {
		return toFormatDateString(aDate, DATE_GBK_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyy年MM月dd日 HH时mm分ss秒'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toLongDateGBKString(Date aDate) {
		return toFormatDateString(aDate, LONG_DATE_GBK_FORMAT);
	}

	/**
	 * 将一个日期型转换为'HH时mm分ss秒'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toLongDateTmPartGBKString(Date aDate) {
		return toFormatDateString(aDate, Long_DATE_TM_PART_GBK_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyy-MM-dd HH:mm:ss:SSS'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toFullDateString(Date aDate) {
		return toFormatDateString(aDate, FULL_DATE_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyy年MM月dd日 HH时mm分ss秒SSS毫秒'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toFullDateGBKString(Date aDate) {
		return toFormatDateString(aDate, FULL_DATE_GBK_FORMAT);
	}

	/**
	 * 将一个日期型转换为'yyyyMMddHHmmssSSS'格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toFullDateCompactString(Date aDate) {
		return toFormatDateString(aDate, FULL_DATE_COMPACT_FORMAT);
	}

	/**
	 * 将一个日期型转换为LDAP格式字串
	 * 
	 * @param aDate
	 * @return
	 */
	public static final String toLDAPDateString(Date aDate) {
		return toFormatDateString(aDate, LDAP_DATE_FORMAT);
	}

	// ============================获取指定时间=====================================

	/**
	 * 获取今天的0点0分0秒的时间
	 * 今天是2019-01-02如2019-01-02 00:00:00
	 * @return
	 */
	public static final Date getToday() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		Date today = calendar.getTime();
		return today;
	}
	/**
	 * 获取昨天的0点0分0秒的时间
	 * 今天是2019-01-02如2019-01-01 00:00:00
	 * @return
	 */
	public static final Date getYesterday() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 1);
		Date yesterday = calendar.getTime();
		return yesterday;
	}
	public static final Date getYesterdayEnd() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - 1);
		Date yesterday = calendar.getTime();
		return yesterday;
	}

	public static final Date getEndDate(Date date) {
		if (Objects.isNull(date)){
			log.error("getEndDate is failed");
			throw new RuntimeException("时间类型转化失败");
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		return calendar.getTime();
	}
	public static final Date getStartDate(Date date) {
		if (Objects.isNull(date)){
			log.error("getStartDate is failed");
			throw new RuntimeException("时间类型转化失败");
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		return calendar.getTime();
	}
	// ============================2.String2Date=====================================

	/**
	 * 将一个符合指定格式的字串解析成日期型
	 * 
	 * @param aDateStr
	 * @param formatter
	 * @return
	 * @throws ParseException
	 */
	public static final Date parser(String aDateStr, String formatter) throws ParseException {
		if (StringUtils.isBlank(aDateStr)){
			return null;
		}
		Assert.hasText(formatter);
		SimpleDateFormat sdf = new SimpleDateFormat(formatter);
		return sdf.parse(aDateStr);

	}
	/**
	 * 将一个符合'yyyy-MM-dd HH:mm:ss'格式的字串解析成日期型
	 * 
	 * @param aDateStr
	 * @return
	 */
	public static final Date parseLongDateString(String aDateStr) throws ParseException {
		return parser(aDateStr, LONG_DATE_FORMAT);

	}

	// /**
	// * 将一个符合'yyyy-MM-dd HH:mm:ss'格式的字串解析成日期型
	// *
	// * @param aDateStr
	// * @return
	// */
	// public static final Date parseLongDateDtPartString(String aDateStr) throws
	// ParseException {
	// return parser(aDateStr, LONG_DATE_FORMAT);
	//
	// }
	//
	/**
	 * 将一个符合'yyyy-MM-dd HH:mm:ss'格式的字串解析成日期型
	 *
	 * @param aDateStr
	 * @return
	 *
	 */
	public static final Date parseLongDateTmPartString(String aDateStr) throws ParseException {
		return parser(aDateStr, LONG_DATE_FORMAT);
	}

	/**
	 * 将一个符合'yyyy-MM-dd'格式的字串解析成日期型
	 * 
	 * @param aDateStr
	 * @return
	 */
	public static final Date parseShortDateString(String aDateStr) throws ParseException {
		return parser(aDateStr, SHORT_DATE_FORMAT);

	}

	/**
	 * 将一个符合'yyyyMMddHHmmss'格式的字串解析成日期型
	 * 
	 * @param aDateStr
	 * @return
	 */
	public static final Date parseMailDateString(String aDateStr) throws ParseException {
		return parser(aDateStr, MAIL_DATE_FORMAT);

	}

	/**
	 * 将一个符合'yyyyMMdd'格式的字串解析成日期型
	 * 
	 * @param aDateStr
	 * @return
	 */
	public static final Date parseMailDateDtPartString(String aDateStr) throws ParseException {
		return parser(aDateStr, MAIL_DATE_DT_PART_FORMAT);
	}

	/**
	 * 将一个符合'yyyy-MM'格式的字串解析成日期型
	 * @param aDateStr
	 * @return
	 * @throws ParseException
     */
	public static final Date parseShortMonthString(String aDateStr) throws ParseException {
		return parser(aDateStr, SHORT_MONTH_FORMAT);
	}

	/**
	 * 将一个符合'yyyyMM'格式的字串解析成日期型
	 * @param aDateStr
	 * @return
	 * @throws ParseException
     */
	public static final Date parseShortMonthV2String(String aDateStr) throws ParseException {
		return parser(aDateStr, SHORT_MONTH_FORMATV2);
	}





	/**
	 * 将一个符合'yyyyMMddHH'格式的字串解析成日期型
	 *
	 * @param aDateStr
	 * @return
	 */
	public static final Date parseMailDateDHPartString(String aDateStr) throws ParseException {
		return parser(aDateStr, MAIL_DATE_HOUR_DT_PART_FORMAT);
	}

	/**
	 * 将一个符合'HHmmss'格式的字串解析成日期型
	 * 
	 * @param aDateStr
	 * @return
	 */
	public static final Date parseMailDateTmPartString(String aDateStr) throws ParseException {
		return parser(aDateStr, MAIL_TIME_TM_PART_FORMAT);
	}

	/**
	 * 将一个符合'yyyy-MM-dd HH:mm:ss:SSS'格式的字串解析成日期型
	 * 
	 * @return
	 */
	public static final Date parseFullDateString(String aDateStr) throws ParseException {
		return parser(aDateStr, FULL_DATE_FORMAT);

	}

	/**
	 * 将一个符合'yyyy-MM-dd'、'yyyy-MM-dd HH:mm:ss'或'EEE MMM dd HH:mm:ss zzz
	 * yyyy'格式的字串解析成日期型， 如果为blank则返回空，如果不为blank又不符合格式则报错
	 * 
	 * @param aDateStr
	 * @return
	 */
	public static Date parseDateString(String aDateStr) {
		Date ret = null;
		if (StringUtils.isNotBlank(aDateStr)) {
			try {
				if (DateUtil.isShortDateStr(aDateStr)) {
					ret = DateUtil.parseShortDateString(aDateStr);
				} else if (DateUtil.isLongDateStr(aDateStr)) {
					ret = DateUtil.parseLongDateString(aDateStr);
				} else if (DateUtil.isDateStrMatched(aDateStr, DateUtil.DEFAULT_DATE_FORMAT)) {
					ret = DateUtil.parser(aDateStr, DateUtil.DEFAULT_DATE_FORMAT);
				} else {
					throw new IllegalArgumentException("date format mismatch");
				}
			} catch (ParseException e) {
				// 不可能到这里
				return null;
			}
		}
		return ret;
	}

	// ============================3.String2String=====================================

	/**
	 * 转换日期格式 yyyy-MM-dd => yyyyMMdd
	 * 
	 * @param dt
	 *            yyyy-MM-dd
	 * @return yyyyMMdd
	 */
	public static String transfer2ShortDate(String dt) {
		if (dt == null || dt.length() != 10) {
			return dt;
		}
		Assert.notNull(dt);
		String[] tmp = StringUtils.split(dt, DASH);
		Assert.isTrue(tmp != null && tmp.length == 3);
		return tmp[0].concat(StringUtils.leftPad(tmp[1], 2, "0")).concat(StringUtils.leftPad(tmp[2], 2, "0"));
	}

	/**
	 * yyyymmdd => yyyy-MM-dd
	 */
	public static Date string2Date(String dt){
        try {
            return parser(transfer2ShortDate(dt), SHORT_DATE_FORMAT);
        } catch (ParseException e) {
			log.error("transfer2ShortDate error", e);
			return null;
        }

    }
	/**
	 * 转换日期格式 yyyyMMdd => yyyy-MM-dd
	 * 
	 * @param dt
	 *            yyyyMMdd
	 * @return yyyy-MM-dd
	 */
	public static String transfer2LongDateDtPart(String dt) {
		if (dt == null || dt.length() != 8) {
			return dt;
		}
		Assert.notNull(dt);
		Assert.isTrue(dt.length() == 8);
		return dt.substring(0, 4).concat(DASH).concat(dt.substring(4, 6)).concat(DASH).concat(dt.substring(6));
	}

	/**
	 * 转换日期格式 HHmmss => HH:mm:ss
	 * 
	 * @param tm
	 *            HHmmss
	 * @return HH:mm:ss
	 */
	public static String transfer2LongDateTmPart(String tm) {
		if (tm == null || tm.length() != 6) {
			return tm;
		}
		Assert.notNull(tm);
		Assert.isTrue(tm.length() == 6);
		return tm.substring(0, 2).concat(COLON).concat(tm.substring(2, 4)).concat(COLON).concat(tm.substring(4));
	}

	/**
	 * 转换日期格式 yyyyMMdd => yyyy年MM月dd日
	 * 
	 * @param dt
	 *            yyyyMMdd
	 * @return yyyy年MM月dd日
	 */
	public static String transfer2LongDateGbkDtPart(String dt) {
		if (dt == null || dt.length() != 8) {
			return dt;
		}
		Assert.notNull(dt);
		Assert.isTrue(dt.length() == 8);
		return dt.substring(0, 4).concat("年").concat(dt.substring(4, 6)).concat("月").concat(dt.substring(6))
				.concat("日");
	}

	/**
	 * 转换日期格式HHmmss => HH时mm分ss秒
	 * 
	 * @param tm
	 *            HHmmss
	 * @return HH时mm分ss秒
	 */
	public static String transfer2LongDateGbkTmPart(String tm) {
		if (tm == null || tm.length() != 6) {
			return tm;
		}
		Assert.notNull(tm);
		Assert.isTrue(tm.length() == 6);
		return tm.substring(0, 2).concat("时").concat(tm.substring(2, 4)).concat("分").concat(tm.substring(4))
				.concat("秒");
	}

	// ============================4.时间加减=====================================

	/**
	 * 为一个日期加上指定年数
	 * 
	 * @param aDate
	 * @param amount
	 *            年数
	 * @return
	 */
	public static final Date addYears(Date aDate, int amount) {
		return addTime(aDate, Calendar.YEAR, amount);
	}

	/**
	 * 为一个日期加上指定月数
	 * 
	 * @param aDate
	 * @param amount
	 *            月数
	 * @return
	 */
	public static final Date addMonths(Date aDate, int amount) {
		return addTime(aDate, Calendar.MONTH, amount);
	}

	/**
	 * 为一个日期加上指定天数
	 * 
	 * @param aDate
	 * @param amount
	 *            天数
	 * @return
	 */
	public static final Date addDays(Date aDate, int amount) {
		return addTime(aDate, Calendar.DAY_OF_YEAR, amount);
	}

	public static String getCurrentDate(int year, int month, int day, int beDay) {
		GregorianCalendar newCal = new GregorianCalendar(year, month, day);
		long milSec = newCal.getTimeInMillis() - (long) beDay * 24 * 3600 * 1000;
		GregorianCalendar other = new GregorianCalendar();
		other.setTimeInMillis(milSec);
		String newYear = String.valueOf(other.get(GregorianCalendar.YEAR));
		String newMonth = String.valueOf(other.get(GregorianCalendar.MONTH) + 1);
		newMonth = newMonth.length() == 1 ? "0" + newMonth : newMonth;
		String newDay = String.valueOf(other.get(GregorianCalendar.DAY_OF_MONTH));
		newDay = newDay.length() == 1 ? "0" + newDay : newDay;
		String date = newYear + "-" + newMonth + "-" + newDay;
		return date;
	}

	/**
	 * 为一个日期减去一个指定天数
	 * 
	 * @param amount
	 *            天数
	 * @return yyyy-mm-dd
	 */
	public static final String minusDays(int amount) {
		Calendar cal = Calendar.getInstance();
		int year = cal.get(Calendar.YEAR);
		int moths = cal.get(Calendar.MONTH);
		int day = cal.get(Calendar.DAY_OF_MONTH);
		return getCurrentDate(year, moths, day, amount - 1);
	}

	/**
	 * 为一个日期减去一个指定天数
	 * 
	 * @param amount
	 *            天数
	 * @param date
	 *            日期
	 * @return yyyy-MM-dd
	 */
	public static final String minusDays(String date, int amount) {
		if (date == null || date.length() != 8) {
			return date;
		}
		try {
			Calendar cal = Calendar.getInstance();
			cal.set(Integer.parseInt(date.substring(0, 4)), Integer.parseInt(date.substring(4, 6)) - 1,
					Integer.parseInt(date.substring(6)));
			int moths = cal.get(Calendar.MONTH);
			int day = cal.get(Calendar.DAY_OF_MONTH);
			int year = cal.get(Calendar.YEAR);
			return getCurrentDate(year, moths, day, amount);
		} catch (Exception e) {
			return null;
		}

	}

	/**
	 * 为一个日期加上指定天数
	 * 
	 * @param aDate
	 *            yyyyMMdd格式字串
	 * @param amount
	 *            天数
	 * @return
	 */
	public static final String addDays(String aDate, int amount) {
		try {
			return DateUtil.toMailDateDtPartString(
					addTime(DateUtil.parseMailDateDtPartString(aDate), Calendar.DAY_OF_YEAR, amount));
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 为一个日期加上指定小时数
	 * 
	 * @param aDate
	 * @param amount
	 *            小时数
	 * @return
	 */
	public static final Date addHours(Date aDate, int amount) {
		return addTime(aDate, Calendar.HOUR, amount);

	}

	/**
	 * 为一个日期加上指定分钟数
	 * 
	 * @param aDate
	 * @param amount
	 *            分钟数
	 * @return
	 */
	public static final Date addMinutes(Date aDate, int amount) {
		return addTime(aDate, Calendar.MINUTE, amount);
	}

	/**
	 * 为一个日期加上指定秒数
	 * 
	 * @param aDate
	 * @param amount
	 *            秒数
	 * @return
	 */
	public static final Date addSeconds(Date aDate, int amount) {
		return addTime(aDate, Calendar.SECOND, amount);

	}

	private static Date addTime(Date aDate, int timeType, int amount) {
		if (aDate == null) {
			return null;
		}
		Calendar cal = Calendar.getInstance();
		cal.setTime(aDate);
		cal.add(timeType, amount);
		return cal.getTime();
	}

	// ======================================5.时间国际化=================================

	/**
	 * 得到当前时间的UTC时间
	 * 
	 * @return
	 */
	public static final String getUTCTime() {
		return getSpecifiedZoneTime(Calendar.getInstance().getTime(), TimeZone.getTimeZone("GMT+0"));
	}

	/**
	 * 得到指定时间的UTC时间
	 * 
	 * @param aDate
	 *            时间戳
	 * @return yyyy-MM-dd HH:mm:ss 格式
	 */
	public static final String getUTCTime(Date aDate) {
		return getSpecifiedZoneTime(aDate, TimeZone.getTimeZone("GMT+0"));
	}

	/**
	 * 得到当前时间的指定时区的时间
	 * 
	 * @param tz
	 * @return
	 */
	public static final String getSpecifiedZoneTime(TimeZone tz) {
		return getSpecifiedZoneTime(Calendar.getInstance().getTime(), tz);

	}

	/**
	 * 得到指定时间的指定时区的时间
	 * 
	 * @param aDate
	 *            时间戳,Date是一个瞬间的long型距离历年的位移偏量，
	 *            在不同的指定的Locale/TimeZone的jvm中，它toString成不同的显示值，
	 *            所以没有必要为它再指定一个TimeZone变量表示获取它时的jvm的TimeZone
	 * 
	 * @param tz
	 *            要转换成timezone
	 * 
	 * @return yyyy-MM-dd HH:mm:ss 格式
	 */
	public static final String getSpecifiedZoneTime(Date aDate, TimeZone tz) {
		if (aDate == null)
			return StringUtils.EMPTY;
		Assert.notNull(tz);
		SimpleDateFormat sdf = new SimpleDateFormat(LONG_DATE_FORMAT);
		sdf.setTimeZone(tz);
		return sdf.format(aDate);
	}

	/**
	 */
	public static final String getZoneTimeDate(Date aDate, Integer tz) {
		if (aDate == null)
			return StringUtils.EMPTY;
		Assert.notNull(tz);
		SimpleDateFormat sdf = new SimpleDateFormat(POINT_DATA_LONG_DTM_PART_FORMAT);
		Date now = DateUtil.addMinutes(aDate,-tz);
		return sdf.format(now);
	}
	public static final Date getZoneTimeDate2(Date aDate, Integer tz) {
		if (aDate == null)
			return null;
		Assert.notNull(tz);
//		SimpleDateFormat sdf = new SimpleDateFormat(POINT_DATA_LONG_DTM_PART_FORMAT);
		Date now = DateUtil.addMinutes(aDate,-tz);
		return now;
	}

	/*************************************************************
	 * function: add bar to a date string , such as if the date string is
	 * "20060912090807" , the return string is "2006-09-12 09:08:07" input : date
	 * string as "20060912090807" output : date string as "2006-09-12 09:08:07" note
	 * : scottie xu
	 *********************************************************/
	public static String addBarAndColonToDateString(String dateStr) {
		String retDateStr = "";

		if (dateStr == null || dateStr.length() != 14) {
			return dateStr;
		}

		retDateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8) + " "
				+ dateStr.substring(8, 10) + ":" + dateStr.substring(10, 12) + ":" + dateStr.substring(12);

		return retDateStr;

	}

	/*************************************************************
	 * function: remove the bar from a date string , such as if the date string is
	 * "2006-09-12" , the return string is "20060912" . input : date string as
	 * "2006-09-12" output : date string as "20060912" note : scottie xu
	 *********************************************************/
	public static String removeBarFromDateString(String dateStr) {
		String retDateStr = "";

		if (dateStr == null || dateStr.length() != 10) {
			return dateStr;
		}

		retDateStr = dateStr.substring(0, 4) + dateStr.substring(5, 7) + dateStr.substring(8);

		return retDateStr;

	}

	// ==================================6. miscellaneous==========================

	/**
	 * 计算两个日期之间相差的月数
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static final int getDifferenceMonths(Date startDate, Date endDate) {
		Assert.notNull(startDate);
		Assert.notNull(endDate);
		Calendar startCal = Calendar.getInstance();
		startCal.setTime(startDate);
		Calendar endCal = Calendar.getInstance();
		endCal.setTime(endDate);

		return Math.abs((startCal.get(Calendar.YEAR) - endCal.get(Calendar.YEAR)) * 12
				+ (startCal.get(Calendar.MONTH) - endCal.get(Calendar.MONTH)));
	}

	/**
	 * 计算两个日期之间相差的月数
	 * 
	 * @param startDateStr
	 *            yyyy-mm-dd
	 * @param endDateStr
	 *            yyyy-mm-dd
	 * @return
	 */
	public static final int getDifferenceMonths(String startDateStr, String endDateStr) {
		DateUtil.checkShortDateStr(startDateStr);
		DateUtil.checkShortDateStr(endDateStr);
		try {
			return getDifferenceMonths(parseShortDateString(startDateStr), parseShortDateString(endDateStr));
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 计算两个日期之间相差的天数
	 * 
	 * @param startDateStr
	 *            yyyy-mm-dd
	 * @param endDateStr
	 *            yyyy-mm-dd
	 * @return
	 */
	public static final int getDifferenceDays(String startDateStr, String endDateStr) {
		return Long.valueOf(getDifferenceMillis(startDateStr, endDateStr) / (NANO_ONE_DAY)).intValue();
	}

	/**
	 * 计算两个日期之间相差的天数
	 * 
	 * @param startDateStr
	 *            yyyymmdd
	 * @param endDateStr
	 *            yyyymmdd
	 * @return
	 */
	public static final int getDifferenceDays2(String startDateStr, String endDateStr) {
		return Long.valueOf(getDifferenceMillis(startDateStr, endDateStr, MAIL_DATE_DT_PART_FORMAT) / (NANO_ONE_DAY))
				.intValue();
	}

	/* ------- start ------------ */
	/**
	 * 两个日期之间相减（存在负数）
	 * 
	 * @param startDateStr
	 *            yyyy-mm-dd
	 * @param endDateStr
	 *            yyyy-mm-dd
	 * @return
	 */
	public static final int getDaysSubtract(String startDateStr, String endDateStr) {
		return Long.valueOf(getDaysSubtractMillis(startDateStr, endDateStr) / (NANO_ONE_DAY)).intValue();
	}

	/**
	 * 两个日期之间相减（存在负数）
	 * 
	 * @param startDateStr
	 *            yyyymmdd
	 * @param endDateStr
	 *            yyyymmdd
	 * @return
	 */
	public static final int getDaysSubtract2(String startDateStr, String endDateStr) {
		return Long.valueOf(getDaysSubtractMillis(startDateStr, endDateStr, MAIL_DATE_DT_PART_FORMAT) / (NANO_ONE_DAY))
				.intValue();
	}

	/**
	 * 两个日期之间相减（存在负数）
	 * 
	 * @param startDateStr
	 *            yyyy-mm-dd
	 * @param endDateStr
	 *            yyyy-mm-dd
	 * @return
	 * @throws ParseException
	 */
	public static final long getDaysSubtractMillis(String startDateStr, String endDateStr) {
		return getDaysSubtractMillis(startDateStr, endDateStr, SHORT_DATE_FORMAT);
	}

	/**
	 * 计算两个日期之间相差的的毫秒数（存在负数）
	 * 
	 * @param startDateStr
	 * @param endDateStr
	 * @param dateFormat
	 * @return
	 */
	public static final long getDaysSubtractMillis(String startDateStr, String endDateStr, String dateFormat) {
		try {
			return getDaysSubtractMillis(parser(startDateStr, dateFormat), parser(endDateStr, dateFormat));
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 计算两个日期之间相差的的毫秒数（存在负数）
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static final long getDaysSubtractMillis(Date startDate, Date endDate) {
		Assert.notNull(startDate);
		Assert.notNull(endDate);
		return endDate.getTime() - startDate.getTime();
	}

	/* ------- end ------------ */

	/**
	 * 计算两个日期之间相差的天数
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static final int getDifferenceDays(Date startDate, Date endDate) {
		return Long.valueOf(getDifferenceMillis(startDate, endDate) / (NANO_ONE_DAY)).intValue();

	}

	/*************************************************************
	 * function: delete the space input : string as " 2 " output : string as "2"
	 * note : richard zhang
	 *********************************************************/
	public static String deleteLRSpace(String a) {
		int i, j;
		int m = 0, n = 0;
		int len = a.length();
		String c = "";
		for (i = 0; i < len; i++) {
			if (a.substring(i, i + 1).equals("　") || a.substring(i, i + 1).equals(" ")) {
				m++;
			} else {
				break;
			}
		}
		if (m < len) {
			for (j = len - 1; j > 0; j--) {
				if (a.substring(j, j + 1).equals("　") || a.substring(j, j + 1).equals(" ")) {
					n++;
				} else {
					break;
				}
			}
			c = a.substring(m, len - n);
		}
		return c;
	}

	/**
	 * 计算两个日期之间相差的的毫秒数
	 * 
	 * @param startDateStr
	 *            yyyy-mm-dd
	 * @param endDateStr
	 *            yyyy-mm-dd
	 * @return
	 * @throws ParseException
	 */
	public static final long getDifferenceMillis(String startDateStr, String endDateStr) {
		return getDifferenceMillis(startDateStr, endDateStr, SHORT_DATE_FORMAT);
	}

	/**
	 * 计算两个日期之间相差的的毫秒数
	 * 
	 * @param startDateStr
	 *            yyyyMMddHHmmss
	 * @param endDateStr
	 *            yyyyMMddHHmmss
	 * @return
	 * @throws ParseException
	 */
	public static final long getDifferenceMillis2(String startDateStr, String endDateStr) {
		return getDifferenceMillis(startDateStr, endDateStr, MAIL_DATE_FORMAT);
	}

	/**
	 * 计算两个日期之间相差的的毫秒数
	 * 
	 * @param startDateStr
	 * @param endDateStr
	 * @param dateFormat
	 * @return
	 */
	public static final long getDifferenceMillis(String startDateStr, String endDateStr, String dateFormat) {
		try {
			return getDifferenceMillis(parser(startDateStr, dateFormat), parser(endDateStr, dateFormat));
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 计算两个日期之间相差的的毫秒数
	 * 
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static final long getDifferenceMillis(Date startDate, Date endDate) {
		Assert.notNull(startDate);
		Assert.notNull(endDate);
		return Math.abs(endDate.getTime() - startDate.getTime());
	}

	/**
	 * 检验 日期是否在指定区间内，如果格式错误，返回false
	 * 
	 * 如果maxDateStr或minDateStr为空则比较时变为正负无穷大，如果都为空，则返回false
	 * 
	 * @param aDate
	 * @param minDateStr
	 *            必须是yyyy-MM-dd格式，时分秒为00:00:00
	 * @param maxDateStr
	 *            必须是yyyy-MM-dd格式，时分秒为00:00:00
	 * @return
	 */
	public static final boolean isDateBetween(Date aDate, String minDateStr, String maxDateStr) {
		Assert.notNull(aDate);
		boolean ret = false;
		try {
			Date dMaxDate = null;
			Date dMinDate = null;
			dMaxDate = DateUtil.parseShortDateString(maxDateStr);
			dMinDate = DateUtil.parseShortDateString(minDateStr);
			switch ((dMaxDate != null ? 5 : 3) + (dMinDate != null ? 1 : 0)) {
			case 6:
				ret = aDate.before(dMaxDate) && aDate.after(dMinDate);
				break;
			case 5:
				ret = aDate.before(dMaxDate);
				break;
			case 4:
				ret = aDate.after(dMinDate);
			}
		} catch (ParseException e) {
			return false;
		}
		return ret;
	}

	/**
	 * 计算某日期所在月份的天数
	 * 
	 * @param aDateStr
	 *            yyyy-mm-dd
	 * @return
	 */
	public static final int getDaysInMonth(String aDateStr) {
		DateUtil.checkShortDateStr(aDateStr);
		try {
			return getDaysInMonth(parseShortDateString(aDateStr));
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 计算某日期所在月份的天数
	 * 
	 * @param aDateStr
	 *            yyyymmdd
	 * @return
	 */
	public static final int getMailDaysInMonth(String aDateStr) {
		DateUtil.isMailDateDtPartStr(aDateStr);
		try {
			return getDaysInMonth(parseMailDateDtPartString(aDateStr));
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 计算某日期所在月份的天数
	 * 
	 * @param aDate
	 * @return
	 */
	public static final int getDaysInMonth(Date aDate) {
		Assert.notNull(aDate);
		Calendar cal = Calendar.getInstance();
		cal.setTime(aDate);
		return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
	}

	/*************************************************************
	 * function: add Chinese to a date string , such as if the date string is
	 * "20060912" , the return string is "2006-09-12" input : date string as
	 * "20060912" output : date string as "2006-09-12" note : man wu
	 *********************************************************/
	public static String toDateString(String dateStr) {
		String retDateStr = "";

		if (dateStr == null || dateStr.length() != 8) {
			return dateStr;
		}

		retDateStr = dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6);

		return retDateStr;

	}

	public static boolean isDate(String dateStr, String formatStr) {
		if (dateStr == null) {
			return false;
		}
		if (formatStr == null) {
			return false;
		}
		SimpleDateFormat format = new SimpleDateFormat(formatStr);
		try {
			format.parse(dateStr);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	/**
	 * 判断字串是否符合yyyy-MM-dd格式
	 * 
	 * @return
	 */
	public static final boolean isShortDateStr(String aDateStr) {
		try {
			if (DateUtil.parseShortDateString(aDateStr) == null) {
				return false;
			}
		} catch (ParseException e) {
			return false;
		}
		return true;
	}

	/**
	 * 判断字串是否符合yyyy-MM-dd HH:mm:ss格式
	 * 
	 * @return
	 */
	public static final boolean isLongDateStr(String aDateStr) {
		try {
			DateUtil.parseLongDateString(aDateStr);
		} catch (ParseException e) {
			return false;
		}
		return true;
	}

	/**
	 * 判断字串是否符合指定的日期格式
	 * 
	 * @return
	 */
	public static final boolean isDateStrMatched(String aDateStr, String formatter) {
		if (StringUtil.isBlank(aDateStr)) {
			return false;
		}
		try {
			DateUtil.parser(aDateStr, formatter);
		} catch (ParseException e) {
			return false;
		}
		return true;
	}

	/**
	 * 检查字串是否符合yyyy-MM-dd格式
	 * 
	 */
	public static final void checkShortDateStr(String aDateStr) {
		Assert.isTrue(isShortDateStr(aDateStr), "The str-'" + aDateStr + "' must match 'yyyy-MM-dd' format.");
	}

	/**
	 * 判断字串是否符合yyyyMMdd格式
	 * 
	 * @return
	 */
	public static final boolean isMailDateDtPartStr(String aDateStr) {
		if (StringUtil.isBlank(aDateStr)) {
			return false;
		}
		try {
			DateUtil.parseMailDateDtPartString(aDateStr);
		} catch (ParseException e) {
			return false;
		}
		return true;
	}

	public static final String SHORT_DATE_FORMAT = "yyyy-MM-dd";

	public static final String SHORT_DATE_FORMAT_START = "yyyy-MM-dd 00:00:00";
	public static final String SHORT_DATE_FORMAT_END = "yyyy-MM-dd 23:59:59";
	public static final String SHORT_MONTH_FORMAT = "yyyy-MM";
	public static final String SHORT_MONTH_FORMATV2 = "yyyyMM";
	public static final String SHORT_DATE_GBK_FORMAT = "yyyy年MM月dd日";
	public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm";
	public static final String DATE_GBK_FORMAT = "yyyy年MM月dd日 HH时mm分";
	public static final String LONG_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
	public static final String LONG_DATE_GBK_FORMAT = "yyyy年MM月dd日 HH时mm分ss秒";
	public static final String MAIL_DATE_FORMAT = "yyyyMMddHHmmss";
	public static final String MAIL_DATE_HHMM_FORMAT = "HH:mm";
	public static final String FULL_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss:SSS";
	public static final String FULL_DATE_GBK_FORMAT = "yyyy年MM月dd日 HH时mm分ss秒SSS毫秒";
	public static final String FULL_DATE_COMPACT_FORMAT = "yyyyMMddHHmmssSSS";
	public static final String LDAP_DATE_FORMAT = "yyyyMMddHHmm'Z'";
	public static final String US_LOCALE_DATE_FORMAT = "EEE MMM dd HH:mm:ss zzz yyyy";

	public static final String MAIL_DATE_DT_PART_FORMAT = "yyyyMMdd";
	public static final String MAIL_DATE_HOUR_DT_PART_FORMAT = "yyyyMMddHH";
	public static final String MAIL_TIME_TM_PART_FORMAT = "HHmmss";
	public static final String MAIL_HOUR_TM_PART_FORMAT = "HH";
	public static final String LONG_DATE_TM_PART_FORMAT = "HH:mm:ss";
	public static final String Long_DATE_TM_PART_GBK_FORMAT = "HH时mm分ss秒";
	public static final String MAIL_DATA_DTM_PART_FORMAT = "MM月dd日HH:mm";
	public static final String POINT_DATA_DTM_PART_FORMAT = "yyyy.MM.dd";
	public static final String POINT_DATA_LONG_DTM_PART_FORMAT = "yyyy.MM.dd HH:mm:ss";

	public static final String DEFAULT_DATE_FORMAT = US_LOCALE_DATE_FORMAT;

	public static long NANO_ONE_SECOND = 1000;
	public static long NANO_ONE_MINUTE = 60 * NANO_ONE_SECOND;
	public static long NANO_ONE_HOUR = 60 * NANO_ONE_MINUTE;
	public static long NANO_ONE_DAY = 24 * NANO_ONE_HOUR;

	public static final String DASH = "-";
	public static final String COLON = ":";

	private DateUtil() {
	}


	/**
	 * 此函数非原创，从网上搜索而来，timeZoneOffset原为int类型，为班加罗尔调整成float类型
	 * timeZoneOffset表示时区，如中国一般使用东八区，因此timeZoneOffset就是8
	 * @param timeZoneOffset
	 * @return
	 */
	public static String getFormatedDateString(float timeZoneOffset){
		if (timeZoneOffset > 13 || timeZoneOffset < -12) {
			timeZoneOffset = 0;
		}

		int newTime=(int)(timeZoneOffset * 60 * 60 * 1000);
		TimeZone timeZone;
		String[] ids = TimeZone.getAvailableIDs(newTime);
		if (ids.length == 0) {
			timeZone = TimeZone.getDefault();
		} else {
			timeZone = new SimpleTimeZone(newTime, ids[0]);
		}

		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		sdf.setTimeZone(timeZone);
		return sdf.format(new Date());
	}

	public static String getUsLocaleDateFormat(String timeZone,String pattern) {

		Date date = new Date(System.currentTimeMillis());
		SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
		dateFormat.setTimeZone(TimeZone.getTimeZone(timeZone));
		String localTime = dateFormat.format(date);
		return localTime;
	}

	public static String getDay( ) {
		Calendar instance = Calendar.getInstance();
		return instance.get(Calendar.DATE)+"";
	}
	
	public static String getYear( ) {
		Calendar instance = Calendar.getInstance();
		return instance.get(Calendar.YEAR)+"";
	}
	public static String getYearWithParam( Date queryDate) {
		Calendar instance = Calendar.getInstance();
		if(null != queryDate) {
			instance.setTime(queryDate);
		}
		return instance.get(Calendar.YEAR)+"";
	}
	public static String getMonth(Date queryDate ) {
		Calendar instance = Calendar.getInstance();
		if(null != queryDate) {
			instance.setTime(queryDate);
		}
		return (instance.get(Calendar.MONTH) + 1)+"";
	}

	/**
	 * 获取上个月,以yyyyMM返回
	 * @return
	 */
	public static String getLastMonth(Date time){
		DateTime dateTime = LocalDateUtils.offsetMonth(time, -1);
		return dateTime.toString("yyyyMM");
	}

	/**
	 * 获取上个月,以yyyyMM返回
	 * @return
	 */
	public static String getCurrentMonth(){
		return DateTime.now().toString("yyyyMM");
	}

	public static Integer parseDateYyyyMMdd2Int(Date date){
		String value = LocalDateUtils.format(date, DatePattern.PURE_DATE_PATTERN);
		if (StrUtil.isBlank(value))
			return null;
		return Integer.parseInt(value);
	}

	public static Integer parseDateYyyyMMdd2IntThrowException(Date date){
		if (Objects.isNull(date)){
			return null;
		}
		String value = LocalDateUtils.format(date, DatePattern.PURE_DATE_PATTERN);
		if (StrUtil.isBlank(value)){
			log.error("parseDateYyyyMMdd2IntThrowException get date:{}",date);
			throw new RuntimeException("时间转化类型失败");
		}
		return Integer.parseInt(value);
	}
	public static Integer parseDateYyyyMM2Int(Date date){
		String value = LocalDateUtils.format(date, "yyyyMM");
		if (StrUtil.isBlank(value))
			return null;
		return Integer.parseInt(value);
	}

	public static DateTime format2DateYyyyMMdd(Integer dateInt){
		Date value = LocalDateUtils.parse(dateInt.toString(), DatePattern.PURE_DATE_PATTERN);
		return new DateTime(value);
	}

	public static DateTime format2DateYyyyMM(Integer dateInt){
		Date value = LocalDateUtils.parse(dateInt.toString(), DatePattern.SIMPLE_MONTH_PATTERN);
		return new DateTime(value);
	}

	public static Date formatDate2DateYyyyMM(Integer dateInt) {
		if (Objects.isNull(dateInt)) {
			return null;
		}
		return  LocalDateUtils.parse(dateInt.toString(), DatePattern.PURE_DATE_FORMAT);
	}

	public static Integer getNextMonth(Integer monthInt){
		Date time = LocalDateUtils.parse(monthInt.toString(), DatePattern.SIMPLE_MONTH_PATTERN);
		DateTime dateTime = LocalDateUtils.offsetMonth(time, 1);
		return Integer.valueOf(dateTime.toString("yyyyMM"));
	}

	public static void main(String[] args) {
		Date timeStr1 = LocalDateUtils.parse("20230111", DatePattern.PURE_DATE_FORMAT);
		Date timeStr2 = LocalDateUtils.parse("20230610", DatePattern.PURE_DATE_FORMAT);

		System.out.println(toLongDateString(new Date()));
		System.out.println(getStartDate(new Date()));
		System.out.println(cn.hutool.core.date.DateUtil.offsetMinute(new Date(),-10).getTime());

		Long maxId =  222222222L;
		Long minId = 0L;
		Long pageSize = 1000000L;
		int i = (int) Math.ceil(maxId * 1.0 / pageSize);
		System.out.println(DateUtil.betweenMonth(timeStr1, timeStr2, true));
	}
}
