package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/23 11:09
 * @description:
 */
@AllArgsConstructor
@Getter
public enum AgentType {
    AGENT_TYPE(0, "代理商"),
    BD_AGENT_TYPE(1, "开户型服务商"),
    RP_AGENT_TYPE(2, "资源型服务商"),
    OP_AGENT_TYPE(3, "运营型服务商"),
    KP_SERVICE(4, "KP服务商"),
    JV_COMPANY_TYPE(5, "合资公司");

    private final Integer id;
    private final String name;

    public static AgentType from(Integer id) {
        return Arrays.stream(values()).filter(type -> type.id.equals(id)).findFirst().orElse(null);
    }

    public static Optional<AgentType> getAgentTypeEnum(Integer id) {
        return Stream.of(values()).filter((item) -> Objects.equals(id, item.getId())).findAny();
    }

    public static String getNameById(Integer id) {
        return getAgentTypeEnum(id).map(AgentType::getName).orElse(null);
    }

}
