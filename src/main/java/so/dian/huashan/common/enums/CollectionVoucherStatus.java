package so.dian.huashan.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/13 17:04
 * @description:
 */
@Getter
@RequiredArgsConstructor
public enum CollectionVoucherStatus {

    INITIAL(0, "初始化"),
    IN_PROCESS(1, "处理中"),
    SUCCESS(2, "成功"),
    FAIL(3, "失败"),
    ;
    private final Integer code;

    private final String desc;

    public static CollectionVoucherStatus from(Byte code) {
        return Arrays.stream(values())
                .filter(status -> status.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
    public Byte getCode() {
        return this.code.byteValue();
    }
}
