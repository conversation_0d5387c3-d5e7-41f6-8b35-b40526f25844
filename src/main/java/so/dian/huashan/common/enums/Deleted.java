package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import so.dian.himalaya.able.EnumInterface;

/**
 * @author: xingba
 * @create: 2023/03/23 19:03
 * @description:
 */
@AllArgsConstructor
public enum Deleted implements EnumInterface<Deleted> {

    INIT(0,  "初始化"),
    DELETE(1,  "删除");

    private final Integer code;
    private final String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }
    @Override
    public Deleted getDefault() {
        return null;
    }
}
