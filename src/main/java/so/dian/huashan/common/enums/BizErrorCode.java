package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.ErrorCodeInterface;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/20 14:37
 * @description:
 */
@Getter
@AllArgsConstructor
public enum BizErrorCode implements ErrorCodeInterface<BizErrorCode> {

    BILL_ALIPAY_CALL_ERROR("5001000","调用支付宝获取账单文件时返回失败"),
    BILL_FILE_CALL_ERROR("5001001", "调用外部接口拉取账单文件时出现异常"),
    ERROR_FOR_SHA256("5001002","SHA256加签失败"),
    UNKNOWN_ENUM("5001003", "未知枚举值"),
    UNKNOWN_CONFIG("5001003", "未知配置"),
    ERROR_BATCH_BILL_CHANNEL("5001004","暂不支持的对账渠道"),
    START_BATCH_JOB_FAIL("5001005", "批量拉取三方账单任务失败"),
    UNKNOWN_BIZ_TYPE("0","未知的渠道业务类型"),
    REMOTE_CALL_ERROR("50010056","远程调用异常"),
    BIZ_EXCEPTION("50010057", "业务异常"),
    SHARD_TASK_FAIL("50010058", "分片任务执行失败")
    ;


    private final String code;

    private final String desc;

}
