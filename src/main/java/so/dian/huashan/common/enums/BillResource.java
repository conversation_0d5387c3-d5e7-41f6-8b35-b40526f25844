package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账单渠道来源 <br/>
 *
 * <AUTHOR>
 * @date 2018-12-12 18:18
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum BillResource {

    Alipay(1, "支付宝"),
    <PERSON><PERSON><PERSON>(3, "微信"),
    <PERSON><PERSON><PERSON>(11, "宝付"),
    <PERSON><PERSON>(12, "微美"),
    ;

    private final Integer key;
    private final String desc;

    public Integer getKey() {
        return key;
    }
}
