package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.ErrorCodeInterface;
import so.dian.himalaya.common.enums.error.BaseErrorCodeEnum;

@Getter
@AllArgsConstructor
public enum BizErrorCodeEnum implements ErrorCodeInterface<BaseErrorCodeEnum> {

    //职员相关
    STAFF_NOT_FOUND(10000, "职员信息不存在"),

    //批处理任务执行
    ERROR_BATCH_TASK_ID(80000,"错误的批次任务id"),

    ERROR_BATCH_BILL_CHANNEL(80001,"暂不支持的对账渠道"),

    BILL_TASK_INSTANCE_ERROR(80002,"账单导入任务实例化异常"),

    BILL_TASK_RULE_ERROR(80003,"批次任务未获取到对账规则"),

    BILL_FILE_ERROR(80004,"账单文件还未生成，不能执行解析操作"),

    BILL_ALIPAY_CALL_ERROR(80005,"调用支付宝获取账单文件时返回失败"),

    DING_TALK_SEND_FAIL(80006, "钉钉发送失败"),

    BILL_TASK_EXECUTE_ERROR(80006, "账单导入任务执行失败"),

    BILL_FILE_CALL_ERROR(80007, "调用外部接口拉取账单文件时出现异常"),

    BILL_FILE_COS_ERROR(80008, "上传文件至腾讯云时出现异常"),

    BILL_DEVICE_BUSY(80009, "机器繁忙"),

    ODPS_BILL_SYNC_ERROR(80010, "账单同步请求至ODPS异常"),

    CONFIG_NOT_COMPLETE(80011, "配置信息不完善"),

    PARAMETER_ERROR(80012, "参数错误"),

    DATA_CONVERTER_ERROR(80013, "数据转换异常"),

    ERROR_BATCH_BILL_GROUP_TYPE(80014,"暂不支持的分组类型"),

    //批处理任务执行
    ERROR_BILL_RULE_CONFIG_ID(80015,"错误的账单规则id"),

    ERROR_FOR_SHA256(80016,"SHA256加签失败"),

    NOTIFY_XIANDU_INCOME_ERROR(80017, "通知仙都核对收入账请求异常"),

    NON_EXISTENT_FILE(80018,"该日无对应账单"),


    UNKNOWN_BIZ_TYPE(0,"未知的渠道业务类型"),


    NOT_FIND_TRADE_ORDER(80019, "没有查询到交易订单数据"),

    NOT_FIND_REFUND_ORDER(80020, "没有查询到退款单数据"),

    SETTLE_ORDER_STATUS_IS_NULL(80030, "订单状态为空"),
    SETTLE_ORDER_STATUS_NOT_EXIST(80031, "订单状态不存在"),
    SETTLE_CHECK_STATUS_ILLEGAL(80032, "不支持的对账状态处理"),

    PIPELINE_PROCESS_FAIL(80040, "管道处理失败"),
    SUMMARY_DATA_GENERATING(80041,"汇总数据生成中，请稍后重试！"),
    NOTES_INCLUDE_PUNCTUATION_MARKS(80042,"备注含标点符号最多可输入200个字符")
    ;


    private Integer code;

    private String desc;

    @Override
    public String getCode() {
        return String.valueOf(code);
    }


    @Override
    public String getDesc() {
        return desc;
    }

}
