package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/17 17:49
 * @description: 第三方渠道支付类型
 * 主要是用来解释这个账号是干嘛用的，一对一
 */
@AllArgsConstructor
@Getter
public enum ThirdpartyPayType {

    WEIXIN_FD("微信前端分账", ThirdpartyChannel.WEIXIN),//Frontend Divide，上游提供
    WEIXIN_H5("微信H5", ThirdpartyChannel.WEIXIN),
    WEIXIN_APP("微信APP", ThirdpartyChannel.WEIXIN),
    WEIXIN_APPLET("微信小程序", ThirdpartyChannel.WEIXIN),
    MANGE_WEIXIN_APPLET("满格超充微信小程序",ThirdpartyChannel.WEIXIN),
    MANGE_WEIXIN_APPLET_NEW("满格超充微信小程序新主体",ThirdpartyChannel.WEIXIN),
    WEIMEI("微美", ThirdpartyChannel.WEIXIN),
    ALIPAY_H5("支付宝H5", ThirdpartyChannel.ALIPAY),
    ALIPAY_APP("支付宝APP", ThirdpartyChannel.ALIPAY),
    ZMKC_ALIPAY_APP("芝麻快充支付宝APP", ThirdpartyChannel.ALIPAY),
    ZMKC_ALIPAY_APP_NEW("芝麻快充支付宝APP_NEW", ThirdpartyChannel.ALIPAY),
    XIYOUKE("僖游客", ThirdpartyChannel.XIYOUKE),
    WEIXIN_PARTNER("微信收付通", ThirdpartyChannel.WEIXINPARTNER),
    CHANNEL_SHARE_ACCOUNT("渠道分账",ThirdpartyChannel.ACCOUNTSHARE),
    ;

    private final String desc;

    private final ThirdpartyChannel channel;

    public static ThirdpartyPayType fromName(String name) {
        if (name == null) {
            return null; // 对 null 输入返回 null
        }
        return Arrays.stream(values())
                .filter(type -> type.name().equals(name))
                .findFirst().orElse(null);
    }
}
