package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum PropagateModel {

    LOCAL("local", "本地"),
    CLUSTERING("clustering", "集群"),
    ;
    private final String code;
    private final String desc;

    public static PropagateModel from(String code) {
        return Arrays.stream(values()).filter(model -> model.code.equals(code)).findFirst().orElse(null);
    }
}
