package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description: 账单类型
 * @Author: liangfang
 * @Date: 2020-09-22 16:20
 */
@Getter
@AllArgsConstructor
public enum BillSourceEnum {


    ALIPAY(1,"alipay","支付宝","3,4,9,21,25,26,34,44,45,46,47,54,100"),
    WECHAT(3,"wechat","微信","1,2,5,8,16,17,18,19,20,30,33,101,52"),
    SUNING(4,"suning","苏宁支付","13,24"),
    QQ(5,"qq","qq支付","10"),
    YINLIAN(6,"yinlian","银联","27,28"),
    HUIYUAN(7,"huiyuan","会员","29，32"),
    YOUHUIQUAN(8,"youhuiquan","优惠券","14"),
    JIFEN(9,"jifen","积分","31"),
    QIANBAO(10,"qianbao","钱包","6,7,15"),
    <PERSON>OFU(11,"baofu","宝付","16"),
    WEIMEI(12,"weimei","微美","16"),
    HUAZHU(13,"huazhu","华柱","41,42,43"),
    OFFLINE_TRANSACTION(14,"线下收款交易","线下进行结算","199"),
    BILLING_ACCOUNT(15,"分成账户支付","分成账户支付","51"),
    AGENT_WALLET(16,"代理商账户支付","代理商账户支付","48,49,50"),
    ;
    private Integer id;

    private String code;

    private String desc;

    private String payType;

    public static BillSourceEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        } else {
            BillSourceEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                BillSourceEnum e = var1[var3];
                if (e.getCode().equals(code)) {
                    return e;
                }
            }

            return null;
        }
    }

    public static BillSourceEnum getByPayType(Integer payType) {
        if (payType == null) {
            return null;
        } else {
            BillSourceEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                BillSourceEnum e = var1[var3];
                String[] split = e.getPayType().split(",");
                for(String type : split){
                    if(type.equals(payType.toString())){
                        return e;
                    }
                }
            }
            return null;
        }
    }

}
