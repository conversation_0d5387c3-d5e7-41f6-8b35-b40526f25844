package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.himalaya.able.EnumInterface;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/23 10:15
 * @description:
 */
@AllArgsConstructor
@Getter
public enum ProductCategory implements EnumInterface<ProductCategory> {

    PACKAGE(1, "充电站套餐"),
    BOX(2, "盒子"),
    CABINET(3, "柜机"),
    RACK(4, "机架"),
    POWERBANK(5, "充电宝"),
    ADAPTER(6, "适配器"),
    DESKTOP(7, "座充"),
    MENU(8, "餐牌"),
    IOT(9, "IOT"),
    ONLINE_PART(10, "联网设备配件"),

    ;

    private final Integer code;
    private final String desc;

    @Override
    public ProductCategory getDefault() {
        return null;
    }
}
