package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum CreditBizTypeEnum {
    TRADE(1,"交易"),
    ORDER(2,"订单"),
    TRANSFER_ORDER(3,"转让单");
    private final Integer code;

    private final String desc;


    public static CreditBizTypeEnum transferType(Integer code){
        if (Objects.isNull(code)){
            return null;
        }
        for (CreditBizTypeEnum bizTypeEnum : values()){
            if (bizTypeEnum.code.equals(code)){
                return bizTypeEnum;
            }
        }
        return null;
    }
}
