package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单业务类型 <br/>
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderBizType {

    ORDER_BOX_ORDER(1, "租借订单"),
    TIMES_CARD_ORDER(2, "次卡订单"),
    TIMES_CARD_VERIFY(3, "次卡核销"),
    ;

    private final Integer key;
    private final String desc;

    public Integer getKey() {
        return key;
    }
}
