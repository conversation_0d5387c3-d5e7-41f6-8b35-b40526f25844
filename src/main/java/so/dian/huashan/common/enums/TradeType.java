package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import so.dian.himalaya.able.EnumInterface;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/20 19:03
 * @description:
 */
@AllArgsConstructor
public enum TradeType implements EnumInterface<TradeType> {
    IN(1, "in", "收入"),
    OUT(2, "out", "支出");

    private final Integer code;
    private final String key;
    private final String desc;

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    public String getKey() {
        return key;
    }

    @Override
    public TradeType getDefault() {
        return null;
    }

    /**
     * 详情
     */
    public static String getKeyByDesc(String desc){
        if (StringUtils.isBlank(desc)){
            return null;
        }
        for (TradeType tradeType:TradeType.values()){
            if (tradeType.getDesc().equals(desc)){
                return tradeType.getKey();
            }
        }
        return null;
    }
}
