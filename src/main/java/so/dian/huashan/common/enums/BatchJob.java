package so.dian.huashan.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/03/15 11:47
 * @description:
 */
@Getter
@AllArgsConstructor
public enum BatchJob {

    WEXIN_BILL_JOB("weixinBillJob","weixinBillParse", "微信账务账单批处理解析任务"),
    ALIPAY_BILL_JOB("alipayBillJob","alipayBillParse", "支付宝账务账单批处理解析任务"),
    XIYOUKE_BILL_JOB("xiyoukeBillJob","xiyoukeBillParse", "僖游客账务账单批处理解析任务"),
    WEXIN_PARTNER_BILL_JOB("weixinPartnerBillJob","weixinPartnerBillParse", "微信账务账单批处理解析任务"),
    SHARE_ACCOUNT_BILL_JOB("shareAccountBillJob","shareAccountParse", "微信渠道分账账单批处理解析任务");

    private final String jobName;

    private final String parseBillName;

    private final String desc;
}
