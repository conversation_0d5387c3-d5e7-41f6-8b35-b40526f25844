package so.dian.huashan.common.service.entity;

import lombok.Builder;
import lombok.Getter;
import so.dian.huashan.common.enums.AgentType;

import java.util.Objects;

import static so.dian.huashan.common.enums.AgentType.AGENT_TYPE;
import static so.dian.huashan.common.enums.AgentType.JV_COMPANY_TYPE;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/23 10:21
 * @description: 门店所属公司
 */
@Getter
@Builder
public class ShopCompanyBO {

    private Long shopId;

    private Long companyId;

    private String companyName;

    private AgentType agentType;

    public String agentTypeName() {
        if (Objects.nonNull(companyId) && (companyId == 0 || companyId == 3))
            return "小电";

        if (Objects.isNull(agentType))
            return null;

        switch (agentType) {
            case BD_AGENT_TYPE:
            case RP_AGENT_TYPE:
            case OP_AGENT_TYPE:
            case KP_SERVICE:
                return "服务商";
            case AGENT_TYPE:
                return AGENT_TYPE.getName();
            case JV_COMPANY_TYPE:
                return JV_COMPANY_TYPE.getName();
            default:
                return null;
        }
    }
}
