package so.dian.huashan.common.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalEnumUtils;
import so.dian.huashan.common.enums.ProductCategory;
import so.dian.huashan.common.mapper.tiantai.PimCspuMapper;
import so.dian.huashan.common.mapper.tiantai.PimSpuMapper;
import so.dian.huashan.common.mapper.tiantai.dos.PimCspuDO;
import so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO;
import so.dian.huashan.common.service.entity.SpuInfoBO;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/10/20 18:31
 * @description:
 */
@Slf4j
@Service
public class SpuService {

    private static final String DEFAULT_SPU_KEY = "spu";

    @Autowired
    private PimSpuMapper pimSpuMapper;

    @Autowired
    private PimCspuMapper pimCspuMapper;

    private LoadingCache<String, List<SpuInfoBO>> spuCache;

    @PostConstruct
    public void init() {

        spuCache = CacheBuilder.newBuilder()
                .concurrencyLevel(1)
                .expireAfterAccess(1, TimeUnit.HOURS)
                .initialCapacity(64)
                .maximumSize(200)
                .recordStats()
                .build(CacheLoader.from(this::loadFromDb));
    }

    public List<SpuInfoBO> list(List<Integer> deviceTypes) {
        if (CollUtil.isEmpty(deviceTypes))
            return Collections.emptyList();

        try {
            List<SpuInfoBO> spuInfos = spuCache.get(DEFAULT_SPU_KEY);
            return spuInfos.stream()
                    .filter(spuInfo -> deviceTypes.contains(spuInfo.getDeviceType()))
                    .collect(Collectors.toList());
        }catch (Exception e) {
            log.info("根据设备类型获取SPU信息出现异常, deviceTypes:{}", JSON.toJSONString(deviceTypes), e);
            return Collections.emptyList();
        }
    }

    public List<SpuInfoBO> loadFromDb() {

        log.info("从供应系统加载SPU相关信息");
        List<PimSpuDO> pimSpuDOS = pimSpuMapper.selectAll();
        Map<String, PimSpuDO> codeAndSpuMap =  pimSpuDOS.stream().collect(Collectors.toMap(PimSpuDO::getSpuCode, Function.identity()));

        List<SpuInfoBO> spuInfos = Lists.newArrayList();
        List<PimCspuDO> pimCspuDOS = pimCspuMapper.selectAll();
        for (PimCspuDO pimCspuDO : pimCspuDOS) {
            List<Integer> cloudModels = JSON.parseArray(pimCspuDO.getCloudModel(), Integer.class);
            if (CollUtil.isEmpty(cloudModels)) {
                continue;
            }

            PimSpuDO spuDO = codeAndSpuMap.getOrDefault(pimCspuDO.getSpuCode(), new PimSpuDO());
            for (Integer cloudModel : cloudModels) {
                SpuInfoBO spuInfo = SpuInfoBO.builder()
                        .deviceType(cloudModel)
                        .spuCode(spuDO.getSpuCode())
                        .spuName(spuDO.getSpuName())
                        .cspuCode(pimCspuDO.getCspuCode())
                        .cspuName(pimCspuDO.getCspuName())
                        .productCategory(LocalEnumUtils.findByCode(ProductCategory.class, pimCspuDO.getProductCategory()))
                        .build();
                spuInfos.add(spuInfo);
            }
        }

        return spuInfos;
    }
}
