package so.dian.huashan.common.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.common.enums.AgentType;
import so.dian.huashan.common.mapper.customer.TenantMapper;
import so.dian.huashan.common.mapper.customer.dos.TenantDO;
import so.dian.huashan.common.mapper.oss.AgentMapper;
import so.dian.huashan.common.mapper.oss.dos.AgentDO;
import so.dian.huashan.common.mapper.shop.TenantShopMapper;
import so.dian.huashan.common.mapper.shop.dos.TenantShopDO;
import so.dian.huashan.common.service.entity.ShopCompanyBO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/10/23 10:20
 * @description:
 */
@Slf4j
@Service
public class ShopService {

    @Autowired
    private TenantShopMapper tenantShopMapper;

    @Autowired
    private TenantMapper tenantMapper;

    @Autowired
    private AgentMapper agentMapper;

    public List<ShopCompanyBO> list(List<Long> shopIds) {
        if (CollUtil.isEmpty(shopIds))
            return Collections.emptyList();

        List<TenantShopDO> tenantShopDOS = tenantShopMapper.selectByShopIds(shopIds);
        if (CollUtil.isEmpty(tenantShopDOS))
            return Collections.emptyList();

        List<Long> tenantIds = tenantShopDOS.stream().map(TenantShopDO::getTenantId).collect(Collectors.toList());
        List<TenantDO> tenantDOS = tenantMapper.selectByIds(tenantIds);
        Map<Long, TenantDO> idAndTenantMap = tenantDOS.stream().collect(Collectors.toMap(TenantDO::getId, Function.identity()));
        if (CollUtil.isEmpty(tenantDOS)) {
            log.warn("获取不到相应的租户数据, tenantId:{}", JSON.toJSONString(tenantIds));
            return Collections.emptyList();
        }

        List<Long> agentIds = tenantDOS.stream().map(TenantDO::getAgentId).collect(Collectors.toList());
        List<AgentDO> agentDOS = agentMapper.selectByIds(agentIds);
        Map<Long, AgentDO> idAndAgentMap = agentDOS.stream().collect(Collectors.toMap(AgentDO::getId, Function.identity()));

        List<ShopCompanyBO> shopCompanies = Lists.newArrayList();
        for (TenantShopDO tenantShopDO : tenantShopDOS) {
            TenantDO tenantDO = idAndTenantMap.get(tenantShopDO.getTenantId());
            if (Objects.isNull(tenantDO))
                continue;

            AgentDO agentDO = idAndAgentMap.get(tenantDO.getAgentId());
            if (Objects.isNull(agentDO))
                continue;

            ShopCompanyBO shopCompany = ShopCompanyBO.builder()
                    .shopId(tenantShopDO.getShopId())
                    .companyId(agentDO.getId())
                    .companyName(agentDO.getName())
                    .agentType(AgentType.from(agentDO.getType()))
                    .build();
            shopCompanies.add(shopCompany);
        }

        return shopCompanies;
    }

}
