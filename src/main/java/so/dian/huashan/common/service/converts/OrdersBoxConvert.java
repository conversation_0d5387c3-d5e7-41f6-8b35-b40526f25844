package so.dian.huashan.common.service.converts;

import so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO;
import so.dian.huashan.common.service.entity.OrdersBoxBO;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/23 13:39
 * @description:
 */
public class OrdersBoxConvert {

    public OrdersBoxBO from(OrdersBoxDO ordersBoxDO) {
        return OrdersBoxBO.builder()
                .orderNo(ordersBoxDO.getOrderNo())
                .payType(ordersBoxDO.getPayType())
                .orderAmount(ordersBoxDO.getOrderAmount())
                .deviceType(ordersBoxDO.getDeviceType())
                .loanShopId(ordersBoxDO.getLoanShopId())
                .returnTime(ordersBoxDO.getReturnTime())
                .loanPriceInfo(ordersBoxDO.getLoanPriceInfo())
                .powerBankNo(ordersBoxDO.getPowerBankNo())
                .loanBoxNo(ordersBoxDO.getLoanBoxNo())
                .build();
    }
}
