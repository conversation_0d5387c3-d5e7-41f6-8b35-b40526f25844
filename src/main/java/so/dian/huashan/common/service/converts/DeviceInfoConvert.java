package so.dian.huashan.common.service.converts;

import so.dian.himalaya.util.LocalEnumUtils;
import so.dian.huashan.common.enums.ProductCategory;
import so.dian.huashan.common.mapper.emei.dto.ProductDeviceInfoDO;
import so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO;
import so.dian.huashan.common.service.entity.DeviceInfoBO;

import java.util.Optional;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/10/24 16:51
 * @description:
 */
public class DeviceInfoConvert {

    public DeviceInfoBO from(ProductDeviceInfoDO deviceInfoDO, PimSpuDO spuDO) {
        return DeviceInfoBO.builder()
                .deviceNo(deviceInfoDO.getDeviceNo())
                .spuCode(deviceInfoDO.getSpuCode())
                .spuName(Optional.ofNullable(spuDO).map(PimSpuDO::getSpuName).orElse(null))
                .productCategory(LocalEnumUtils.findByCode(ProductCategory.class, deviceInfoDO.getProductCategory()))
                .build();
    }
}
