package so.dian.huashan.common.service.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Builder;
import lombok.Getter;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/23 11:46
 * @description:
 */
@Builder
@Getter
public class OrdersBoxBO {

    private String orderNo;
    private Integer payType;
    private Long orderAmount;
    private Integer deviceType;
    private Long loanShopId;
    private Date returnTime;
    private String loanPriceInfo;
    private String powerBankNo;
    private String loanBoxNo;

    public boolean isCappedPrice() {
        if (StrUtil.isBlank(loanPriceInfo))
            return Boolean.FALSE;
        JSONObject jsonObject = JSON.parseObject(loanPriceInfo);
        Long maxCost = jsonObject.getLong("maxCost");
        return orderAmount.equals(maxCost);
    }

    public String cappedPrice() {
        if (isCappedPrice())
            return orderAmount / 100 + "";

        return null;
    }
}
