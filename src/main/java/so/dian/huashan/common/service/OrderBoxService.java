package so.dian.huashan.common.service;

import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.common.mapper.lhc.OrdersBoxMapper;
import so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO;
import so.dian.huashan.common.service.converts.OrdersBoxConvert;
import so.dian.huashan.common.service.entity.OrdersBoxBO;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/10/23 13:37
 * @description:
 */
@Service
public class OrderBoxService {

    @Autowired
    private OrdersBoxMapper ordersBoxMapper;

    public List<OrdersBoxBO> list(List<String> orderNos) {
        if (CollUtil.isEmpty(orderNos))
            return Collections.emptyList();

        List<OrdersBoxDO> ordersBoxDOS = ordersBoxMapper.selectByOrderNos(orderNos);
        OrdersBoxConvert convert = new OrdersBoxConvert();
        return ordersBoxDOS.stream().map(convert::from).collect(Collectors.toList());
    }
}
