package so.dian.huashan.common.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.common.mapper.newyork.dos.TidbTradeOrderDO;
import so.dian.huashan.common.mapper.newyork.tidb.TidbTradeOrderMapper;

import javax.annotation.Resource;
import java.util.concurrent.Future;

import static so.dian.huashan.common.enums.BizErrorCodeEnum.NOT_FIND_TRADE_ORDER;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/10/16 11:51
 * @description:
 */
@Slf4j
@Service
public class TidbTradeOrderService {

    @Resource
    private ThreadPoolTaskExecutor newyorkExecutor;

    @Autowired
    private TidbTradeOrderMapper tidbTradeOrderMapper;

    public TidbTradeOrderDO find(Long id) {
        Future<TidbTradeOrderDO> result = newyorkExecutor.submit(() -> tidbTradeOrderMapper.selectByPrimaryKey(id));
        try {
            return result.get();
        }catch (Exception e) {
            log.error(">>> 非租赁交易信息查询异常, id = {}", id, e);
            throw BizException.create(NOT_FIND_TRADE_ORDER);
        }
    }
}
