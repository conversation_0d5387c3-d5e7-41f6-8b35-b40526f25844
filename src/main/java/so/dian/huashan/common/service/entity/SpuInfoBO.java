package so.dian.huashan.common.service.entity;

import lombok.Builder;
import lombok.Getter;
import so.dian.huashan.common.enums.ProductCategory;

import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/20 18:34
 * @description:
 */
@Builder
@Getter
public class SpuInfoBO {

    private Integer deviceType;

    private String spuCode;

    private String spuName;

    private String cspuCode;

    private String cspuName;

    private ProductCategory productCategory;

    public Integer productCategoryId() {
        return Optional.ofNullable(productCategory).map(ProductCategory::getCode).orElse(null);
    }

    public String productCategoryDesc() {
        return Optional.ofNullable(productCategory).map(ProductCategory::getDesc).orElse(null);
    }
}
