package so.dian.huashan.common.service;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.common.mapper.newyork.dos.ShardingTradeOrderDO;
import so.dian.huashan.common.mapper.newyork.sharding.ShardingTradeOrderMapper;

import java.util.List;
import java.util.Objects;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/10/16 11:51
 * @description:
 */
@Slf4j
@Service
public class ShardingTradeOrderService {

    @Autowired
    private ShardingTradeOrderMapper shardingTradeOrderMapper;

    public ShardingTradeOrderDO find(String bizOrderNo) {
        if (StrUtil.isBlank(bizOrderNo))
            return null;

        List<ShardingTradeOrderDO> tradeOrderDOS = shardingTradeOrderMapper.selectByBizOrderNo(bizOrderNo);
        return tradeOrderDOS.stream()
                .filter(tradeOrder -> Objects.nonNull(tradeOrder.getValidStatus()) && tradeOrder.getValidStatus() == 0)
                .findFirst().orElse(null);
    }
}
