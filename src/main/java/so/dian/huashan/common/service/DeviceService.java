package so.dian.huashan.common.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.common.mapper.emei.ProductDeviceInfoMapper;
import so.dian.huashan.common.mapper.emei.dto.ProductDeviceInfoDO;
import so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO;
import so.dian.huashan.common.service.converts.DeviceInfoConvert;
import so.dian.huashan.common.service.entity.DeviceInfoBO;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/10/24 16:35
 * @description:
 */
@Slf4j
@Service
public class DeviceService {

    @Autowired
    private ProductDeviceInfoMapper productDeviceInfoMapper;

    @Resource
    private LoadingCache<String, PimSpuDO> spuCache;

    public List<DeviceInfoBO> list(List<String> deviceNos) {
        if (CollUtil.isEmpty(deviceNos))
            return Collections.emptyList();

        List<ProductDeviceInfoDO> deviceInfoDOS = productDeviceInfoMapper.selectByDeviceNos(deviceNos);
        if (CollUtil.isEmpty(deviceNos))
            return Collections.emptyList();

        List<String> spuCodes = deviceInfoDOS.stream().map(ProductDeviceInfoDO::getSpuCode).collect(Collectors.toList());
        ImmutableMap<String, PimSpuDO> spuInfos;
        try {
            spuInfos = spuCache.getAll(spuCodes);
        }catch (Exception e) {
            log.info("SPU数据获取异常, spuCodes= {}", JSON.toJSONString(spuCodes), e);
            spuInfos = ImmutableMap.of();
        }

        DeviceInfoConvert convert = new DeviceInfoConvert();
        ImmutableMap<String, PimSpuDO> finalSpuInfos = spuInfos;
        return deviceInfoDOS.stream()
                .map(deviceInfoDO -> convert.from(deviceInfoDO, finalSpuInfos.get(deviceInfoDO.getSpuCode())))
                .collect(Collectors.toList());
    }

}
