package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;

import java.util.Date;

@Data
public class TransferOrderDO {
    private Long id;
    /**
     * 跨主题转让单号
     */
    private String orderNo;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 状态
     */
    private Integer status;

    /**
     *  转出方渠道ID
     */
    private Long outAgentId;

    /**
     * 转入方渠道ID
     */
    private Long inAgentId;
    /**
     * 转出方仓库id
     */
    private Long outPositionId;
    /**
     * 转入方仓库id
     */
    private Long inPositionId;

    /**
     * 收货人名称
     */
    private String receiveName;

    /**
     * 收获联系方式
     */
    private String receivePhone;
    /**
     * 收货地址
     */
    private String receiveAddress;
    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 最后的审核人
     */
    private Long auditor;
    /**
     * 关联单号
     */
    private String relateOrderNo;

    /**
     * 备注
     */
    private String remark;

    private Long creator;

    private Long updator;

    private Date createTime;

    private Date updateTime;

    private Long gmtCreate;

    private Long gmtUpdate;

    /**
     * 最新发货时间
     */
    private Date deliveryTime;
    /**
     * 收获时间
     */
    private Date receiveTime;

    /**
     * 备注
     */
    private String receiveRemark;

    private Integer processVersion;

    private Integer deleted;

    private Integer orderMark;
}
