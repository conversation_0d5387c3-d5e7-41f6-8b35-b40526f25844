package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

import java.util.Date;
@Data
public class CreditTradeDO extends BaseDO {

    /**
     * Database Column Remarks:
     * 授信卡片ID
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.credit_card_id
     *
     * @mbg.generated
     */
    private Long creditCardId;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * Database Column Remarks:
     * 授信卡片版本号
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.version
     *
     * @mbg.generated
     */
    private Integer version;

    /**
     * Database Column Remarks:
     * 外部单据ID
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.out_order_id
     *
     * @mbg.generated
     */
    private String outOrderId;

    /**
     * Database Column Remarks:
     * 授信类型（1-设备采购授信，2-设备采购授信历史导入）
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.credit_type
     *
     * @mbg.generated
     */
    private Integer creditType;

    /**
     * Database Column Remarks:
     * 订单金额（单位：分）
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.trade_amount
     *
     * @mbg.generated
     */
    private Long tradeAmount;

    /**
     * Database Column Remarks:
     * 扩展信息
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.extra_json
     *
     * @mbg.generated
     */
    private String extraJson;

    /**
     * Database Column Remarks:
     * 首次发货使用时间
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.first_shipment_time
     *
     * @mbg.generated
     */
    private Date firstShipmentTime;

    /**
     * Database Column Remarks:
     * 授信交易状态：1-待分期 2-已分期 3-已退款 4-部分分期
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.trade_status
     *
     * @mbg.generated
     */
    private Integer tradeStatus;

    /**
     * 确认状态
     */
    private Integer confirmStatus;

    /**
     * Database Column Remarks:
     * 设备交易单号
     * <p>
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column credit_trade.scm_trade_no
     *
     * @mbg.generated
     */
    private String scmTradeNo;

    /**
     * 业务编号
     */
    private String bizNo;

    /**
     * 业务类型
     */
    private Integer bizType;
}
