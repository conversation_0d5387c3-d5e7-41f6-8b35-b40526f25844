package so.dian.huashan.common.mapper.lhc.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * payment
 * <AUTHOR>
@Data
public class PaymentDO implements Serializable {
    private Long id;

    private Long orderId;

    /**
     * 订单号 座充30前缀 盒子40前缀 41芝麻信用订单
     */
    private String orderNo;

    /**
     * 本系统交易号
     */
    private String tradeNo;

    /**
     * 存放支付网关id
     */
    private String payNo;

    /**
     * 引流相关设备号
     */
    private String deviceNo;

    private Long userId;

    /**
     * 31订单 32押金 33充值34余额还款35押金转余额36押金抵订单
     */
    private Integer bizType;

    /**
     * 1微信支付，2微信app支付，3支付宝，4支付宝app，5微信小程序，6小电余额支付，7押金抵扣，8优惠券
     */
    private Integer payType;

    private Integer payTypeRoute;

    /**
     * 1待支付2已支付3已退款
     */
    private Integer status;

    /**
     * 支付金额
     */
    private Integer payAmount;

    /**
     * 退款金额
     */
    private Integer refundAmount;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 备注
     */
    private String note;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分成百分比
     */
    private Integer calcRatio;

    /**
     * 分成类型 1:订单金额分成 2：实付金额分成
     */
    private Integer calcType;

    /**
     * 业务表id
     */
    private Long bizId;

}