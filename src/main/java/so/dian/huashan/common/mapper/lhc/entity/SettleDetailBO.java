package so.dian.huashan.common.mapper.lhc.entity;

import lombok.Data;

import java.util.Date;

@Data
public class SettleDetailBO {

    private Long id;

    /**
     * 结算单号
     */
    private Long settleNo;
    /**
     * 结算明细单号
     */
    private Long settleDetailNo;
    /**
     * 租户编码
     */
    private String tenantNo;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 业务单据号，如订单号
     */
    private String bizNo;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 计费对象id
     */
    private String billingTargetId;

    /**
     * 结算对象类型
     */
    private String settleTargetType;

    /**
     * 结算对象id
     */
    private String settleTargetId;

    /**
     * 出资方类型
     */
    private String settleSourceType;

    /**
     * 出资方id
     */
    private String settleSourceId;
    /**
     * 计费基数金额
     */
    private Long amountInput;

    /**
     * 结算金额
     */
    private Long amountOutput;

    /**
     * 出资方的第三方id
     */
    private String tpSourceId;

    /**
     * 入资方的第三方id
     */
    private String tpTargetId;

    /**
     * 第三方结算(分账或退分账)单号
     */
    private String tpSettleNo;

    /**
     * 第三方分成完成时间
     */
    private String tpFinishTime;

    /**
     * 结算月份（分表建）
     */
    private String settleMonth;

    /**
     * 扩展字段
     */
    private String extension;

    /**
     * 结算时间
     */
    private Date settleTime;

    /**
     * 是否删除
     */
    private Integer deleted;

    private Long gmtCreate;

    private Long gmtUpdate;

    private Date createTime;

    private Date updateTime;
}
