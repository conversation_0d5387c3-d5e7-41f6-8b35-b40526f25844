package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ShipmentTradeDO {

    private Long id;

    private Long orderNo;

    private Integer biz_type;

    private Integer orderSource;

    private Integer orderPage;

    private Integer status;

    private Integer subStatus;

    private Long tag;

    private Date createTime;

    private Date finishTime;

    private BigDecimal totalAmount;

    private BigDecimal discountAmount;

    private BigDecimal settleAmount;

    private BigDecimal logisticsFee;

    private BigDecimal realPayAmount;

    private Date payTime;

    private Integer payMode;

    private Integer payChannel;

    private Integer payStatus;

    private String payOrderNo;

    private BigDecimal paidAmount;

    private Long buyerId;

    private String buyerName;

    private Integer buyerType;

    private String buyerCity;

    private Long sellerId;

    private String sellerName;

    private Integer sellerType;

    private Integer creatorId;

    private String creator;

    private String modifier;

    private Long gmtCreate;

    private Long gmtUpdate;

    private Integer deleted;

    private Integer buyerDiamondLevel;

    private String approvalProcessId;

    private Date approvalFinishTime;
}
