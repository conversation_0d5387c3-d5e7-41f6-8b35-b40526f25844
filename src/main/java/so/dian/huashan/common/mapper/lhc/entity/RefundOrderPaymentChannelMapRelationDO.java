package so.dian.huashan.common.mapper.lhc.entity;

import java.io.Serializable;
import lombok.Data;

/**
 * refund_order_payment_channel_map_relation
 * <AUTHOR>
@Data
public class RefundOrderPaymentChannelMapRelationDO implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 分片键
     */
    private String shardingKey;

    /**
     * 订单No(orderNo)
     */
    private String bizOrderNo;

    /**
     * 支付主单
     */
    private String tradeOrderNo;

    /**
     * 退款主单No
     */
    private String refundOrderNo;

    /**
     * 支付类型
     */
    private String refundType;

    /**
     * 支付单No(tradeNo)
     */
    private String paymentNo;

    /**
     * 支付渠道
     */
    private String refundChannel;

    /**
     * 发起金额
     */
    private Long orderAmount;

    /**
     * 支付单状态
     */
    private Integer refundStatus;

    /**
     * 实付金额
     */
    private Long refundAmount;

    /**
     * 退款失败原因
     */
    private String failMsg;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    private static final long serialVersionUID = 1L;
}