package so.dian.huashan.common.mapper.ubud;

import so.dian.huashan.common.mapper.ubud.example.BillingResultDetailExample;
import so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO;

import java.util.List;

public interface BillingResultDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BillingResultDetailDO record);

    int insertSelective(BillingResultDetailDO record);

    BillingResultDetailDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillingResultDetailDO record);

    int updateByPrimaryKey(BillingResultDetailDO record);

    List<BillingResultDetailDO> selectByExample(BillingResultDetailExample example);
}