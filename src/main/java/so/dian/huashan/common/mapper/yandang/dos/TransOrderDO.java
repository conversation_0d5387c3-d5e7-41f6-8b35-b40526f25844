package so.dian.huashan.common.mapper.yandang.dos;

import lombok.*;

import java.io.Serializable;

@Data
public class TransOrderDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户订单号，关联付款单流水号
     */
    private String outTradeNo;

    /**
     * 支付流水号
     */
    private String tradeNo;

    /**
     * 第三方支付流水号
     */
    private String outPayNo;

    /**
     * 业务类型
     *
     */
    private Integer bizType;

    /**
     * 支付金额，单位分
     */
    private Long payAmount;

    /**
     * 服务费，单位分
     */
    private Long serviceAmount;

    /**
     * 支付渠道
     *
     */
    private Integer payChannel;

    /**
     * 支付子渠道
     *
     */
    private Integer paySubChannel;

    /**
     * 状态
     *
     */
    private Integer status;

    /**
     * 支付结果下次查询时间
     */
    private Long nextQueryTime;
    /**
     * 支付发起时间
     */
    private Long payStartTime;
    /**
     * 支付完成时间
     */
    private Long payFinishTime;

    /**
     * 付款账户类型
     *
     */
    private Integer payAccType;

    /**
     * 付款渠道ID
     */
    private Long payChannelId;

    /**
     * 付款账号
     */
    private String payAccNo;

    /**
     * 付款人名称
     */
    private String payAccName;

    /**
     * 付款摘要；业务类型+对象类型
     */
    private String payRemark;

    /**
     * 收款账户类型；
     *
     */
    private Integer revAccType;

    /**
     * 银行卡类型
     *
     */
    private Integer revCardType;

    /**
     * 收款人账号
     */
    private String revAccNo;

    /**
     * 收款人名称
     */
    private String revAccName;

    /**
     * 收款人身份证号
     */
    private String revCardId;

    /**
     * 收款人手机号
     */
    private String revMobile;

    /**
     * 收方银行名称
     */
    private String revBankName;

    /**
     * 收方省份
     */
    private String revBankProvince;

    /**
     * 收方城市
     */
    private String revBankCity;

    /**
     * 收方银行支行
     */
    private String revBankBranch;

    /**
     * 收方银行联行号
     */
    private String revBankNo;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    private Long gmtCreate;
    private Long gmtUpdate;
    private Integer deleted;
}
