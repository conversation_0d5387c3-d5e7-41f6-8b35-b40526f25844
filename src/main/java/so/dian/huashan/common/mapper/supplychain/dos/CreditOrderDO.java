package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

@Data
public class CreditOrderDO extends BaseDO {
    private Integer version;

    private Long creditCardId;

    private Long contractId;

    private String outOrderId;

    private Integer outOrderType;

    private Long orderAmount;

    private String extraJson;

    private Integer status;

    private Integer orderStatus;

    private Integer confirmStatus;

    private String bizOrderNo;

    private String purchaseOrderNo;

    private String shipmentSubOrderNo;

    private Long creditTradeId;
}
