package so.dian.huashan.common.mapper.lhc.entity;

import lombok.Data;

import java.util.Date;

/**
 * HeraTransOrderBO
 * <AUTHOR>
 */
@Data
public class HeraTransOrderBO {

    private Long id;
    private String outBizNo;
    private Integer payChannel;
    private Integer payType;
    private Integer tradeStatus;
    private Integer payAcctType;
    private String payAcctName;
    private String payBankName;
    private String payAcctNo;
    private Integer recAcctType;
    private String recBankName;
    private String recAcctName;
    private String recAcctNo;
    private String recAcctMark;
    private String payOpeUser;
    private String failReason;
    private String remark;
    private Date createTime;
    private Date updateTime;
    private Integer deleted;
    private String token;
    private Integer payAccountSubject;
    private Long payAmount;
    private String outPayNo;
    private Date payTime;
    private Integer bizType;
    private String tradeNo;

}