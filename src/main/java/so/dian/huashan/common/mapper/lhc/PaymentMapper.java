package so.dian.huashan.common.mapper.lhc;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.lhc.entity.PaymentDO;

import java.util.List;

public interface PaymentMapper {

    int insert(PaymentDO record);

    PaymentDO selectByPrimaryKey(Long id);

    List<PaymentDO> selectAlipayByPayNo(@Param("payNo") String payNo);

    List<PaymentDO> selectByOrderNo(@Param("orderNo") String orderNo);

    PaymentDO selectByOrderNoAndPayType(@Param("orderNo") String orderNo,@Param("payType")Integer payType);

}