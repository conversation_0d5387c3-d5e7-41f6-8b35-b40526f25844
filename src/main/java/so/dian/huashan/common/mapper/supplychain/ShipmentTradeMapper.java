package so.dian.huashan.common.mapper.supplychain;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.supplychain.dos.ShipmentTradeDO;

import java.util.List;

public interface ShipmentTradeMapper {

    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("maxId") Long maxId);

    List<ShipmentTradeDO> selectByIds(List<Long> ids);


}
