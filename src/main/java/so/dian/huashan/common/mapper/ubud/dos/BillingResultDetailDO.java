package so.dian.huashan.common.mapper.ubud.dos;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * billing_result_detail
 * <AUTHOR>
@Data
public class BillingResultDetailDO implements Serializable {
    private Long id;

    /**
     * 租户编码
     */
    private String tenantNo;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 业务码
     */
    private String bizCode;

    /**
     * 业务方唯一ID
     */
    private String serialNo;

    /**
     * 业务单据
     */
    private String bizNo;

    /**
     * 计费对象ID, 如门店ID，商品ID
     */
    private String billingTargetId;

    /**
     * 交易类型，是1支付 2退款
     */
    private Integer tradeType;

    /**
     * 结算对象id
     */
    private String settleTargetId;

    /**
     * 结算对象类型
     */
    private String settleTargetType;

    private String settleSourceType;

    private String settleSourceId;

    /**
     * 输入金额
     */
    private Long amountInput;

    /**
     * 输出金额
     */
    private Long amountOutput;

    /**
     * 业务方时间
     */
    private Date bizDate;

    /**
     * 费项
     */
    private Long fundItemId;

    private String fundItemCode;

    /**
     * 计费规则ID
     */
    private Long billingRuleId;

    private Byte deleted;

    private Date createTime;

    private Date updateTime;

    private Long gmtCreate;

    private Long gmtUpdate;

    private static final long serialVersionUID = 1L;
}