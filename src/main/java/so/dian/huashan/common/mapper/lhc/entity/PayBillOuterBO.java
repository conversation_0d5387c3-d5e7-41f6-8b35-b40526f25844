package so.dian.huashan.common.mapper.lhc.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayBillOuterBO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务单号
     */
    private String outBizNo;

    /**
     * 付款单流水号
     */
    private String billNo;

    /**
     * 支付单流水号
     */
    private String payNo;

    /**
     * 业务类型：
     *
     */
    private Integer bizType;

    /**
     * 发起时间；业务方单据时间
     */
    private Long bizApplyTime;

    /**
     * 收款对象类型
     *
     */
    private Integer objectType;

    /**
     * 收款对象id
     */
    private Long objectId;

    /**
     * 收款对象名称
     */
    private String objectName;

    /**
     * 支付金额，单位分
     */
    private Long payAmount;

    /**
     * 支付发起时间
     */
    private Long payStartTime;

    /**
     * 支付完成时间
     */
    private Long payFinishTime;

    /**
     * 发票标识
     *
     */
    private Integer hasInvoice;

    /**
     * 城市代码
     */
    private Integer cityCode;

    /**
     * 支付渠道
     *
     */
    private Integer payChannel;

    /**
     * 支付子渠道
     *
     */
    private Integer paySubChannel;

    /**
     * 收款账户类型
     *
     */
    private Integer revAccType;

    /**
     * 收款账户卡类型
     *
     */
    private Integer revCardType;

    /**
     * 收款人账号
     */
    private String revAccNo;

    /**
     * 收款人名称
     */
    private String revAccName;

    /**
     * 收款人身份证号
     */
    private String revCardId;

    /**
     * 收款人手机号
     */
    private String revMobile;

    /**
     * 收方银行名称
     */
    private String revBankName;

    /**
     * 收方省份
     */
    private String revBankProvince;

    /**
     * 收方城市
     */
    private String revBankCity;

    /**
     * 收方银行支行
     */
    private String revBankBranch;

    /**
     * 收方银行联行号
     */
    private String revBankNo;

    /**
     * 交易状态
     *
     */
    private Integer tradeState;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 结算方类型
     *
     */
    private Integer settleSubjectType;

    /**
     * 结算方ID
     *
     */
    private Long settleSubjectId;


    /**
     * 所属方ID
     */
    private Long belongSubjectId;

    /**
     * 所属类型
     */
    private Integer belongSubjectType;

    /**
     * 所属子类型
     *
     */
    private Integer belongSubType;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 用途
     */
    private String purpose;
    /**
     * 事业部ID
     */
    private Integer businessUnitId;

    /**
     * 数据来源
     *
     */
    private Integer dataSource;

    private Boolean agentDirectSign;

    private Integer deleted;
}
