package so.dian.huashan.common.mapper.lhc;

import so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO;

public interface RefundOrderPaymentChannelMapRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(RefundOrderPaymentChannelMapRelationDO record);

    int insertSelective(RefundOrderPaymentChannelMapRelationDO record);

    RefundOrderPaymentChannelMapRelationDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RefundOrderPaymentChannelMapRelationDO record);

    int updateByPrimaryKey(RefundOrderPaymentChannelMapRelationDO record);
}