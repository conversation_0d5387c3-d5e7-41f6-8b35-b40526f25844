package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;

import java.util.Date;

/**
 * 收获
 */
@Data
public class ShipmentOrderDO {

    private Long id;

    /**
     * 发货单编号
     */
    private String shipmentOrderNo;

    /**
     * 发货通知单编号
     */
    private String shipmentNoticeOrderNo;

    /**
     * 上层业务类型（透传）
     */
    private Integer scmBizType;

    private Integer status;

    /**
     * 期望发货内容统计
     */
    private String expectedContentGroupStat;

    /**
     * 实际发货内容分类统计
     */
    private String actualContentGroupStat;

    /**
     * 是否不良品(返修)仓
     */
    private Integer isRejects;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 在途仓ID
     */
    private Long transitId;

    /**
     * 接收方类型1仓库2BD
     */
    private Integer acceptorType;

    /**
     * 接收方主键 仓库ID或用户ID
     */
    private Long acceptorId;

    private Long creatorId;
    /**
     * 关单操作人ID
     */
    private Long closeOperatorId;

    /**
     * 发货时间
     */
    private Date shipmentTime;

    /**
     * 备注
     */
    private String remark;

    private Integer version;
    /**
     * 单据维度:1:SPU;2:CSPU;3:SKU
     */
    private Integer orderDimension;

    /**
     * 波次单号
     */
    private String waveNo;
    /**
     * 是否正常结单:0:正常;1:异常
     */
    private Integer isNormal;

    private Date createTime;

    private Date updateTime;

    private Integer deleted;
}
