package so.dian.huashan.common.mapper.lhc.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * payment
 * <AUTHOR>
@Data
public class OfflinePayDO implements Serializable {
    private Long id;

    /**
     * 订单号 座充30前缀 盒子40前缀 41芝麻信用订单
     */
    private String orderNo;

    /**
     * 存放支付网关id
     */
    private String payNo;

    private Long payAmount;

    private Date payTime;

    private String payPic;

    private Integer payType;

    private Integer csId;

    private Long gmtCreate;

    private Long gmtUpdate;
}