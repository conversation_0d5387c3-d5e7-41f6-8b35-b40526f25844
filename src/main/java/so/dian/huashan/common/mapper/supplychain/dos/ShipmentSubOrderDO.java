package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;

import java.util.Date;

@Data
public class ShipmentSubOrderDO {

    private Long id;

    /**
     * 发货子单
     */
    private String shipmentSubOrderNo;

    /**
     * 发货单号
     */
    private String shipmentOrderNo;

    /**
     * 物料公司id
     */
    private Long expressId;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 内容分类统计
     */
    private String contentGroupStat;

    /**
     * 是否数量发货
     */
    private Integer hasItem;

    /**
     * 创建者ID
     */
    private Long creatorId;

    private Date createTime;

    private Date updateTime;

    private Integer deleted;
}
