package so.dian.huashan.common.mapper.ubud.example;

import cn.hutool.core.util.StrUtil;
import lombok.Builder;
import lombok.Getter;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/15 17:56
 * @description:
 */
@Getter
public class BillingResultDetailExample {

    private final String settleTargetId;

    private final String settleTargetType;

    private final String bizNo;

    private final Integer tradeType;

    @Builder
    BillingResultDetailExample(String settleTargetId,
                               String settleTargetType,
                               String bizNo,
                               Integer tradeType) {
        this.settleTargetId = StrUtil.isEmpty(settleTargetId) ? null : settleTargetId;
        this.settleTargetType = StrUtil.isEmpty(settleTargetType) ? null : settleTargetType;
        this.bizNo = StrUtil.isEmpty(bizNo) ? null : bizNo;
        this.tradeType = tradeType;
    }
}
