package so.dian.huashan.common.mapper.yandang.dos;

import lombok.Data;

@Data
public class ChannelDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 支付渠道
     *
     */
    private Integer payChannel;

    /**
     * 账户
     */
    private String accountNo;

    /**
     * 扩展数据
     */
    private String extData;

    /**
     * 结算方ID
     *
     */
    private Long settleSubjectId;
    /**
     * 结算方类型
     */
    private Integer settleSubjectType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private Integer lockVersion;

    /**
     * 支付子渠道json数组
     */
    private String paySubChannel;

    /**
     * 账号用途
     */
    private String bizType;
}
