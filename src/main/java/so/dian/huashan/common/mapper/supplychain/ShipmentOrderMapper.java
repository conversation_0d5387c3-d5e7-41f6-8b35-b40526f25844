package so.dian.huashan.common.mapper.supplychain;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.supplychain.dos.ShipmentOrderDO;

import java.util.Date;
import java.util.List;

public interface ShipmentOrderMapper {

    List<Long> selectByCheckDateTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("maxId") Long maxId);

    List<ShipmentOrderDO> selectByIds(List<Long> ids);

    List<ShipmentOrderDO> selectByShipmentOrderNos(@Param("shipmentOrderNos") List<String> shipmentOrderNos);
}
