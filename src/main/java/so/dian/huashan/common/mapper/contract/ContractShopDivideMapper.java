package so.dian.huashan.common.mapper.contract;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO;

import java.util.List;

public interface ContractShopDivideMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ContractShopDivideDO record);

    int insertSelective(ContractShopDivideDO record);

    ContractShopDivideDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContractShopDivideDO record);

    int updateByPrimaryKey(ContractShopDivideDO record);

    List<ContractShopDivideDO> selectUsable(@Param("shopId") Long shopId, @Param("time") Long time);
}