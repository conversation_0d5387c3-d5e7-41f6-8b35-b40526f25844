package so.dian.huashan.common.mapper.supplychain;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.supplychain.dos.TransferOrderDO;

import java.util.Date;
import java.util.List;

public interface TransferOrderMapper {

    List<Long> selectByCheckDateTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("maxId") Long maxId);

    List<TransferOrderDO> selectByIds(List<Long> ids);
}
