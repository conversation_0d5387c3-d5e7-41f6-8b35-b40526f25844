package so.dian.huashan.common.mapper.supplychain.dos;

import lombok.Data;
import java.util.Date;

/**
 * 交易订单-代采信息
 * @TableName trade_order_proxy_sale_info
 */
@Data
public class TradeOrderProxySaleInfoDO{
    /**
     * 唯一标识
     */
    private Long id;

    /**
     * 交易单号
     */
    private Long orderNo;

    /**
     * 付款方式
     */
    private String payMode;

    /**
     * 付款日期
     */
    private Date payTime;

    /**
     * 已支付金额(单位:分)
     */
    private Long alreadyPayAmount;

    /**
     * 待支付金额(单位:分)
     */
    private Long waitPayAmount;

    /**
     * 授信信息
     */
    private String creditInfo;

    /**
     * 分期期数
     */
    private Integer stageCount;

    /**
     * 付款截图
     */
    private String payPic;

    /**
     * 付款方式 0、线下结算 1、线上分期
     */
    private Integer payType;

    /**
     * 提成发放方式
     */
    private Integer commissionWay;

    /**
     * 合同快照
     */
    private String contractInfo;

    /**
     * 备注
     */
    private String remark;

    private Long gmtCreate;

    private Long gmtUpdate;

    private Integer deleted;

}