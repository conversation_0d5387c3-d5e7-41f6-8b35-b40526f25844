package so.dian.huashan.common.exception;

import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/15 18:36
 * @description:
 */
public class LogMarkerFactory {
    public final static Marker SYSTEM_ERROR = MarkerFactory.getMarker("[系统异常]");
    public final static Marker BILLING_SETTLE = MarkerFactory.getMarker("[清结算日志]");
    public final static Marker SHARD_JOB_MARKER = MarkerFactory.getMarker("[分片任务日志]");
    public final static Marker COLLECTION_DATA_MARKER = MarkerFactory.getMarker("[数据采集日志]");
    public final static Marker COLLECTION_RETRY_MARKER = MarkerFactory.getMarker("[采集重试日志]");
    public final static Marker BILL_DIFF_MARKER = MarkerFactory.getMarker("[账单差异重对日志]");
    public final static Marker HUAZHU_CHECK_MARKER = MarkerFactory.getMarker("[华住对账日志]");
    public final static Marker CHANNEL_CHECK_MARKER = MarkerFactory.getMarker("[渠道对账日志]");
    public final static Marker CHANNEL_DIFF_CHECK_MARKER = MarkerFactory.getMarker("[渠道差异对账日志]");
    public final static Marker BILLING_PAY = MarkerFactory.getMarker("[付款日志]");

}
