package so.dian.huashan.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/20 14:33
 * @description:
 */
public class ChannelConstant {

    @AllArgsConstructor
    @Getter
    public enum ALIPAY{

        APP_ID("app_id","appId"),

        P_ID("p_id","商户号"),

        ENCRYPT_ALGORITHM("encrypt_algorithm","加密算法"),

        PUBLIC_KEY_PATH("public_key_path","公钥-加密"),

        PRIVATE_KEY_PATH("private_key_path","私钥-解密"),

        PUBLIC_SIGN_KEY_PATH("public_sign_key_path","公钥-验签"),

        PRIVATE_SIGN_KEY_PATH("private_sign_key_path","私钥-加签"),


        APP_PUBLIC_KEY_PATH("app_public_key_path","应用公钥证书路径"),

        ALIPAY_PUBLIC_KEY_PATH("alipay_public_key_path","支付宝公钥证书路径"),

        ROOT_KEY_PATH("root_key_path","支付宝根证书路径");



        private String param;

        private String desc;
    }

    @AllArgsConstructor
    @Getter
    public enum WECHAT{

        APP_ID("app_id","appId"),

        MCH_ID("mch_id","商户号"),

        ENCRYPT_ALGORITHM("encrypt_algorithm","加密算法"),

        SIGN_ALGORITHM("sign_algorithm","签名算法"),

        PUBLIC_KEY_PATH("public_key_path","公钥-加密"),

        PRIVATE_KEY_PATH("private_key_path","私钥-解密"),

        SIGN_KEY("sign_key","加签key"),

        PUBLIC_SIGN_KEY_PATH("public_sign_key_path","公钥-验签"),

        PRIVATE_SIGN_KEY_PATH("private_sign_key_path","私钥-加签");

        private final String param;

        private final String desc;
    }

    public static final String UNKNOWN_WEIXIN_TRADE_TYPE = "unknown";

    // 对账单文件2000一批处理一次
    public static final int commitCount = 500;

    // 文件流格式
    public static final String gbkCharset = "gbk";

    // 微信账务账单文件字段
    public static final String weixinAccountBillColumn = "accountTime,bizNo,seqNo,bizName,bizType,tradeType,tradeAmount,balance,applicantName,remark,bizVoucherNo";

    // 微信账务账单文件字段
    public static final String weixinParterAccountBillColumn = "accountTime,bizNo,seqNo,bizName,bizType,tradeType,tradeAmount,balance,applicantName,remark,bizVoucherNo,mchId,accountType";


    // 支付宝账务账单文件字段
    public static final String alipayAccountBillColumn = "accountSeqNo,bizSeqNo,merchantNo,productName,occurTime,accountNo,inAmount,outAmount,balance,channel,bizType,remark";
    public static final String xiyoukeAccountBillColumn = "outOrderNo,companyName,serviceProvider,name,bankCard,telephone,iDCard,tradeAmount,serviceCharge,outerOrderStatus,tradeType,distributionChannel," +
            "createTime,source,releaseTime,outerPayNo,feedBackResults,receipt,tradeNo,remark";

    public static final String channelShareAccountBillColumn = "splitDate,splitInitiator,splitSourceId,weixinOrderNo,spiltOrderNo,spiltDetailNo,mchSplitOrderNo,orderAmt,spiltReceiveId,spiltAmt,bizType,statusStr,spiltDescription,remark";

    public static final String BILL_PULL_STEP_NAME = "billPullStep";

    public static final String ALIPAY_CONSUME_DONATE = "CONSUME_DONATE";
}
