package so.dian.huashan.common.constant;

import java.util.regex.Pattern;

public class CommonConstants {


    //非数字开头
    public static Pattern START_WITH_ENG_CHARACTER = Pattern.compile("[^0-9](.)*");

    /**
     * redis分隔符
     */
    public static final String REDIS_SEPARATOR = ":";
    /**
     * 服务名
     */
    public static final String APPLICATION_NAME = "huashan";
    // 账户开户
    public static final String TASK_SUB = CommonConstants.APPLICATION_NAME + "_task_sub";

    public static final String TASK_VOUCHER = CommonConstants.APPLICATION_NAME + "_task_voucher";

    public static final String CHANNEL_ACCOUNT = CommonConstants.APPLICATION_NAME + "_channel_account";

    public static final String HUAZHU_BILL = CommonConstants.APPLICATION_NAME + "_huazhu_bill";

    public static final String CHANNEL_INCREMENT_ACCOUNT = CommonConstants.APPLICATION_NAME + "_increment_channel_account";

    public static final String CHANNEL_DIFFERENCE = CommonConstants.APPLICATION_NAME + "_channel_difference";

    public static final String CHANNEL_INCREMENT_DIFFERENCE = CommonConstants.APPLICATION_NAME + "_increment_channel_difference";


    public static final String MCH_ID_NAME = "mch_id";
    public static final String P_ID_NAME = "p_id";

    public static final String CURRENCY_RMB = "CNY";

    public static final String XIAO_DIAN_CODE = "3";
    public static final String XIAO_DIAN_NAME = "杭州小电科技股份有限公司";

    /**
     * 租赁订单业务类型
     */
    public static final Integer LEASE_BIZ_TYPE = 31;

    public static final Integer LEASE_BIZ_TYPE_100 = 101;


    /**
     * 押金充值业务类型
     */
    public static final Integer PAYMENT_DEPOSIT_BIZ_TYPE = 32;
    public static final Integer PAYMENT_HUAZHU_BIZ_TYPE = 89;

    public static final String ORIGINAL_MARK = "original";
    public static final String UNIPAY_MARK = "unipay";

    public static final String COLLECTION_STOP_FLAG = "huashan_stop";
}
