package so.dian.huashan.common.constant;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/27 11:36
 * @description:
 */
public class CacheNameConsts {

    public static final String CACHE_PIPELINE_NAME = "huashan:collection:pipeline";
    public static final String CACHE_TABLE_NAME = "huashan:collection:table";
    public static final String CACHE_DOMAIN_NAME = "huashan:biz:domain";
    public static final String BRAKE_CACHE_NAME = StrUtil.join(StrUtil.COLON, "huashan:kafka:listener:brake", SpringUtil.getApplicationContext().getEnvironment().getProperty("system.env"));
}
