package so.dian.huashan.task.controller;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.task.job.ChannelAccountingBurstJob;
import so.dian.huashan.task.job.ChannelDifferenceBurstJob;
import so.dian.huashan.task.service.ChannelBurstService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version: v 0.1 TestController.java, 2020-08-11 下午3:46 Exp $
 */
@Slf4j
@RequestMapping("/inner")
@RestController
public class InnerController {

    @Autowired
    private ChannelBurstService channelBurstService;

    @Resource
    private ChannelAccountingBurstJob channelAccountingBurstJob;

    @Resource
    private ChannelDifferenceBurstJob channelDifferenceBurstJob;

    @GetMapping("/preload")
    public String preload(HttpServletResponse response) throws IOException {
        return "OK";
    }

    @GetMapping("/insert")
    public String insert(){
        return "OK";
    }

    @GetMapping("/channelAccounting/execute")
    public String execute(@RequestParam(name = "param", required = false) String param){
        channelAccountingBurstJob.execute(param);
        return "OK";
    }

    @GetMapping("/channelDifferenceBurst/execute")
    public String channelDifferenceBurstexecute(@RequestParam(name = "param", required = false) String param){
        channelDifferenceBurstJob.execute(param);
        return "OK";
    }

    @Autowired
    private RedissonClient redissonClient;

    @GetMapping("remove/cache")
    public BizResult<Boolean> removeCache(@RequestParam("key") String key) {
        RBucket<Object> bucket = redissonClient.getBucket(key);
        return BizResult.create(bucket.delete());
    }
}