package so.dian.huashan.task.job;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.util.LocalPatternUtils;
import so.dian.huashan.task.enums.InvokeTypeEnum;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.job.dto.ChannelParamDTO;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.service.ChannelDifferenceBurstService;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * ChannelDifferenceJob 渠道差异任务分片
 *
 * <AUTHOR>
 * @desc
 * @date 2022/8/26 10:09
 */
@Slf4j
@Component
public class ChannelDifferenceBurstJob {

    @Resource
    private TaskRegistryMapper taskRegistryMapper;

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ChannelDifferenceBurstService channelDifferenceBurstService;

    @Resource
    private LushanFacade lushanFacade;

    @XxlJob("ChannelDifferenceBurstJob")
    public ReturnT<String> execute(String param) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("start","渠道差异对账分片任务开始");
//        lushanFacade.reportJob(jsonObject,"ChannelDifferenceBurstJob");
        String checkDate = "";
        String isExecute = "true";
        Boolean isDeletedMaster = false;

        if(StringUtils.isNotBlank(param)){
            ChannelParamDTO channelParamDTO = JSON.parseObject(param, ChannelParamDTO.class);
            checkDate = channelParamDTO.getCheckDate();
            isExecute = StringUtils.isBlank(channelParamDTO.getIsExecute()) ? "true" : channelParamDTO.getIsExecute();
            isDeletedMaster = channelParamDTO.getIsDeletedMaster();
        }
        if (StringUtils.isBlank(checkDate)) {
            checkDate = LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN);
        } else if (!LocalPatternUtils.dateMatches(checkDate)) {
            log.info("ChannelDifferenceBurstJob 日期格式不符合,str:" + param);
            /**
             * 检查是否日期类型
             */
            return new ReturnT<>(ReturnT.FAIL_CODE, "日期格式不符合");
        }
        // 查询注册表
        TaskRegistryDO  taskRegistryDO = taskRegistryMapper.selectByName("ChannelDifferenceBurstJob");
        if(taskRegistryDO == null){
            log.info("ChannelDifferenceBurstJob 没有注册任务,str:{},checkDate:{}",param,checkDate);
            return ReturnT.SUCCESS;
        }
        log.info("ChannelDifferenceBurstJob 日期检查结束,str:{},checkDate:{}",param,checkDate);
        Integer date = Integer.valueOf(checkDate);
        if(isDeletedMaster){
            TaskMasterDO taskMasterDO = taskMasterMapper.selectByInvokeTypeInvokeDate(date, InvokeTypeEnum.CHANNEL_BURST.code());
            if(Objects.nonNull(taskMasterDO)){
                taskMasterMapper.deleteById(taskMasterDO.getId());
            }
        }
        RLock lock = redissonClient.getFairLock(CommonConstants.CHANNEL_DIFFERENCE+":"+ date);
        try {
            if (lock.tryLock(30L, TimeUnit.SECONDS)) {
                TaskMasterDO taskMasterDO = taskMasterMapper.selectByInvokeTypeInvokeDate(date, InvokeTypeEnum.CHANNEL_DIFFER_BURST.code());
                if (taskMasterDO != null &&
                        (taskMasterDO.getStatus().equals(TaskStatusEnum.BURST_SUCCESS.code())
                                || taskMasterDO.getStatus().equals(TaskStatusEnum.PROGRESS.code())
                        || taskMasterDO.getStatus().equals(TaskStatusEnum.EXECUTE_SUCCESS.code()))) {
                    return ReturnT.SUCCESS;
                }
                channelDifferenceBurstService.differenBurst(isExecute,date, taskRegistryDO, InvokeTypeEnum.CHANNEL_DIFFER_BURST.code());
            }
        } catch (Throwable e) {
            log.error(">>>> 创建渠道差异对账分片任务异常 | checkDate:{},ChannelDifferenceBurstJob",checkDate,e);
        } finally {
            lock.unlock();
        }
//        jsonObject.put("end","渠道差异对账分片任务结束");
//        lushanFacade.reportJob(jsonObject,"ChannelDifferenceBurstJob");
        return ReturnT.SUCCESS;
    }

}
