package so.dian.huashan.task.job;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.task.enums.InvokeTypeEnum;
import so.dian.huashan.task.job.dto.AccountingParamDTO;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.service.ChannelDifferenceBurstService;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * ChannelDifferenceJob 渠道差异任务分片
 *
 * <AUTHOR>
 * @desc
 * @date 2022/8/26 10:09
 */
@Slf4j
@Component
public class DifferenceIncrementBurstJob {

    @Resource
    private TaskRegistryMapper taskRegistryMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ChannelDifferenceBurstService channelDifferenceBurstService;

    @Resource
    private LushanFacade lushanFacade;

    @XxlJob("DifferenceIncrementBurstJob")
    public ReturnT<String> execute(String param) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("start","增量渠道差异对账分片任务开始");
//        lushanFacade.reportJob(jsonObject,"DifferenceIncrementBurstJob");

        log.info("DifferenceIncrementBurstJob 渠道差异增量补偿分片,str:{},",param);
        if(StringUtils.isBlank(param)){
            return ReturnT.SUCCESS;
        }
        JSONObject jsonObject = JSON.parseObject(param);
        AccountingParamDTO accountingParamDTO = AccountingParamDTO.builder().build();
        BeanUtil.copyProperties(jsonObject, accountingParamDTO);
        if(accountingParamDTO == null || accountingParamDTO.getCheckDate() == null){
            log.info("DifferenceIncrementBurstJob 执行日期不能为空,accountingParamDTO:{}",JSONObject.toJSONString(accountingParamDTO));
            return ReturnT.SUCCESS;
        }
        // 查询注册表
        TaskRegistryDO  taskRegistryDO = taskRegistryMapper.selectByName("DifferenceIncrementBurstJob");
        if(taskRegistryDO == null){
            log.info("DifferenceIncrementBurstJob 没有注册任务,str:{},checkDate:{}",param,accountingParamDTO.getCheckDate());
            return ReturnT.SUCCESS;
        }
        RLock lock = redissonClient.getFairLock(CommonConstants.CHANNEL_DIFFERENCE+":"+ accountingParamDTO.getCheckDate());
        try {
            if (lock.tryLock(30L, TimeUnit.SECONDS)) {
                channelDifferenceBurstService.differenceIncrementBurst(accountingParamDTO, taskRegistryDO, InvokeTypeEnum.CHANNEL_DIFFER_BURST.code());
            }
        } catch (Exception e) {
            log.error(">>>> 创建增量渠道差异对账任务分片异常 | accountingParamDTO:{},DifferenceIncrementBurstJob = {}",JSONObject.toJSONString(accountingParamDTO),e);
        } finally {
            lock.unlock();
        }
//        jsonObject.put("end","增量渠道差异对账分片任务结束");
//        lushanFacade.reportJob(jsonObject,"DifferenceIncrementBurstJob");
        return ReturnT.SUCCESS;
    }

}
