package so.dian.huashan.task.job;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.util.LocalPatternUtils;
import so.dian.huashan.task.enums.InvokeTypeEnum;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.job.dto.ChannelParamDTO;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.service.ChannelAccountingBurstService;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * ChannelAccountingJob 渠道任务分片
 *
 * <AUTHOR>
 * @desc
 * @date 2022/8/26 10:09
 */
@Slf4j
@Component
public class ChannelAccountingBurstJob {

    @Resource
    private TaskRegistryMapper taskRegistryMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private ChannelAccountingBurstService channelAccountingBurstService;

    @Resource
    private LushanFacade lushanFacade;

    @XxlJob("ChannelAccountingBurstJob")
    public ReturnT<String> execute(String param) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("start","渠道对账任务分片开始");
//        lushanFacade.reportJob(jsonObject,"ChannelAccountingBurstJob");
        String checkDate = "";
        String isExecute = "true";
        Boolean isDeletedMaster = false;
        if(StringUtils.isNotBlank(param)){
            ChannelParamDTO channelParamDTO = JSON.parseObject(param, ChannelParamDTO.class);
            checkDate = channelParamDTO.getCheckDate();
            isExecute = StringUtils.isBlank(channelParamDTO.getIsExecute()) ? "true" : channelParamDTO.getIsExecute();
            isDeletedMaster = channelParamDTO.getIsDeletedMaster();
        }

        if (StringUtils.isBlank(checkDate)) {
            checkDate = LocalDateUtils.format(LocalDateUtils.yesterday(), DatePattern.PURE_DATE_PATTERN);
        } else if (!LocalPatternUtils.dateMatches(checkDate)) {
            log.info("ChannelAccountingJob 日期格式不符合,str:" + checkDate);
            /**
             * 检查是否日期类型
             */
            return new ReturnT<>(ReturnT.FAIL_CODE, "日期格式不符合");
        }
        log.info("ChannelAccountingBurstJob 日期检查结束,str:{},checkDate:{}",param,checkDate);
        Integer date = Integer.valueOf(checkDate);
        if(isDeletedMaster){
            TaskMasterDO taskMasterDO = taskMasterMapper.selectByInvokeTypeInvokeDate(date, InvokeTypeEnum.CHANNEL_BURST.code());
            if(Objects.nonNull(taskMasterDO)){
                taskMasterMapper.deleteById(taskMasterDO.getId());
            }
        }
        // 查询注册表
        TaskRegistryDO  taskRegistryDO = taskRegistryMapper.selectByName("ChannelAccountingBurstJob");
        if(taskRegistryDO == null){
            log.info("ChannelAccountingBurstJob 没有注册任务,str:{},checkDate:{}",param,checkDate);
            return ReturnT.SUCCESS;
        }
        RLock lock = redissonClient.getFairLock(CommonConstants.CHANNEL_ACCOUNT+":"+ date);
        try {
            if (lock.tryLock(30L, TimeUnit.SECONDS)){
                TaskMasterDO taskMasterDO = taskMasterMapper.selectByInvokeTypeInvokeDate(date, InvokeTypeEnum.CHANNEL_BURST.code());
                if(taskMasterDO != null &&
                        (taskMasterDO.getStatus().equals(TaskStatusEnum.BURST_SUCCESS.code())
                                || taskMasterDO.getStatus().equals(TaskStatusEnum.PROGRESS.code())
                        || taskMasterDO.getStatus().equals(TaskStatusEnum.EXECUTE_SUCCESS.code()))){
                    return ReturnT.SUCCESS;
                }
                channelAccountingBurstService.accountingBurst(isExecute,date,taskRegistryDO,InvokeTypeEnum.CHANNEL_BURST.code());
            }
        } catch (Throwable e) {
            log.error(">>>> 创建渠道对账任务分片异常 | checkDate:{},ChannelAccountingBurstJob",checkDate,e);
        } finally {
            lock.unlock();
        }
//        jsonObject.put("end","渠道对账任务分片任务结束");
//        lushanFacade.reportJob(jsonObject,"ChannelAccountingBurstJob");
        return ReturnT.SUCCESS;
    }


}
