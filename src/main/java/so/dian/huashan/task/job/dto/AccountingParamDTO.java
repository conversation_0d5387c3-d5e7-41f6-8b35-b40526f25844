package so.dian.huashan.task.job.dto;

import com.github.pagehelper.Page;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * TaskSubContext
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 16:13
 */
@Data
@Builder
public class AccountingParamDTO {

    private Integer pageNo;

    private Long masterId;

    private Long pages;

    private Page<Long> page;

    /**
     * 核对日期
     */
    private Integer checkDate;

    /**
     * 凭证id
     */
    private List<Long> voucher;

    /**
     * 开始时间
     */
    private Long startDateTime;

    /**
     * 结束时间
     */
    private Long endDateTime;
}
