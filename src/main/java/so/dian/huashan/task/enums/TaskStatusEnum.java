package so.dian.huashan.task.enums;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/01/25 14:18
 * @description:
 */
public enum TaskStatusEnum {
    INIT(0, "初始化"),
    PROGRESS(1, "进行中"),
    BURST_SUCCESS(2, "分片成功"),
    BURST_FAIL(3, "分片失败"),
    EXECUTE_SUCCESS(4, "执行成功"),
    EXECUTE_FAIL(5, "执行失败"),
    IN_EXECUTE(6, "执行中")
    ;
    private final Integer code;
    private final String desc;
    TaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer code() {
        return this.code;
    }
    public String desc() {
        return this.desc;
    }

    public static TaskStatusEnum from(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
