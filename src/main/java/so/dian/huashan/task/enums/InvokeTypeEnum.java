package so.dian.huashan.task.enums;

import java.util.Arrays;

/**
 * @author: xingba
 * @create: 2023/03/21 14:18
 * @description:
 */
public enum InvokeTypeEnum {
    /**
     * 1-渠道对账分片，2-渠道对账，3-渠道差异分片，4-渠道差异对账，5-增量渠道对账分片，6-增量渠道对账，7-增量渠道差异分片，8-增量渠道差异对账
     * 9-支付宝渠道拉取，10-微信渠道拉取
     */
    CHANNEL_BURST(1, "渠道对账分片",2),
    CHANNEL_BILL(2, "渠道对账",2),
    CHANNEL_DIFFER_BURST(3, "渠道差异分片",4),
    CHANNEL_DIFFER_BILL(4, "渠道差异对账",4),
    CHANNEL_INCREMENT_BURST(5, "增量渠道对账分片",0),
    CHANNEL_INCREMENT_BILL(6, "增量渠道对账",6),
    CHANNEL_INCREMENT_DIFFER_BURST(7, "增量渠道差异分片",0),
    CHANNEL_INCREMENT_DIFFER_BILL(8, "增量渠道差异对账",7),
    ALIPAY_BILL(9, "支付宝渠道拉取",0),
    WEIXIN_BILL(10, "微信渠道拉取",0),
    HUAZHU_BURST(11, "华住账单分片", 5),
    HUAZHU_BILL(11, "华住账单", 5),
    ;
    private final Integer code;
    private final String desc;
    private final Integer accountCode;
    InvokeTypeEnum(Integer code, String desc,Integer accountCode) {
        this.code = code;
        this.desc = desc;
        this.accountCode = accountCode;
    }

    public Integer code() {
        return this.code;
    }
    public String desc() {
        return this.desc;
    }
    public Integer accountCode() {
        return this.accountCode;
    }

    public static InvokeTypeEnum from(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
