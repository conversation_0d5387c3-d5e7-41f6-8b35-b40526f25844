package so.dian.huashan.task.enums;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2022/01/25 14:18
 * @description:
 */
public enum TaskSubStatusEnum {
    INIT(0, "初始化"),
    PROGRESS(1, "进行中"),
    SUCCESS(2, "成功"),
    FAIL(3, "失败");
    private final Integer code;
    private final String desc;
    TaskSubStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer code() {
        return this.code;
    }
    public String desc() {
        return this.desc;
    }

    public static TaskSubStatusEnum from(Integer code) {
        return Arrays.stream(values())
                .filter(r -> r.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
