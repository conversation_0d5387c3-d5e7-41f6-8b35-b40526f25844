package so.dian.huashan.task.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.mapper.HuazhuIncomeOriginalInfoMapper;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.task.job.dto.AccountingParamDTO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: miaoshuai
 * @create: 2023/10/20 15:38
 * @description:
 */
@Slf4j
@Service
public class HuazhuBillBurstService extends ChannelBurstService {

    @Resource
    private TaskService taskService;

    @Resource
    private RedissonService redissonService;

    @Autowired
    private HuazhuIncomeOriginalInfoMapper huazhuIncomeOriginalInfoMapper;

    private static final Integer PER_QUERY_SIZE = 10000;

    private static final Integer SHARD_SIZE = 500;

    public void burst(String isExecute, Integer date, TaskRegistryDO taskRegistryDO, Integer invokeType){
        Page<Long> page = PageHelper.startPage(1, PER_QUERY_SIZE)
                .doSelectPage(() -> huazhuIncomeOriginalInfoMapper.selectForBurstJob(DateUtil.format2DateYyyyMMdd(date)));
        Long pages = (long) page.getPages();
        if (CollectionUtils.isEmpty(page.getResult())) {
            log.info("HuazhuBillBurstJob 没有账单数据,checkDate:{},Page:{}",date, page);
            return;
        }

        String batchNo = "HZ" + System.currentTimeMillis();
        Long masterId = taskService.taskMaster(batchNo, taskRegistryDO, date, invokeType, getShardSize(),0, pages);
        try{
            basePages(AccountingParamDTO.builder().pageNo(1).masterId(masterId).pages(pages).checkDate(date).page(page).build(),this::listIds);
            redissonService.setChannelMasterId(batchNo,isExecute);
        }catch (Exception e){
            log.error("创建华住账单任务分片异常,checkDate:{},taskRegistryDO:{},invokeType:{}",date, JSONObject.toJSONString(taskRegistryDO),invokeType,e);
            taskService.deleteTaskMaster(masterId);
        }
    }

    public List<Long> listIds(AccountingParamDTO accountingParam) {
        Page<Long> page = PageHelper.startPage(accountingParam.getPageNo(), PER_QUERY_SIZE, false)
                .doSelectPage(() -> huazhuIncomeOriginalInfoMapper.selectForBurstJob(DateUtil.format2DateYyyyMMdd(accountingParam.getCheckDate())));
        return page.getResult();
    }

    @Override
    protected int getShardSize() {
        return SHARD_SIZE;
    }
}
