package so.dian.huashan.task.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.mapper.ThirdPartyBillMapper;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.task.job.dto.AccountingParamDTO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class ChannelAccountingBurstService extends ChannelBurstService{

    @Resource
    private ThirdPartyBillMapper thirdPartyBillMapper;

    @Resource
    private TaskService taskService;

    @Resource
    private RedissonService redissonService;

    private static final Integer maxSize = 10000;

    private static final Integer size = 500;

    public void accountingBurst(String isExecute,Integer date, TaskRegistryDO taskRegistryDO,Integer invokeType){
        Integer pageNo = 1 ;
        Long pages = pagesCount(pageNo,date,size,null,null,null);
        Page<Long> page = pagesAccounting(pageNo,date,maxSize,null,null,null);
        if (page == null || CollectionUtils.isEmpty(page.getResult())) {
            log.info("ChannelAccountingBurstJob 没有账单数据,checkDate:{},Page:{}",date,page);
            return;
        }
        String batchNo = "CH" + System.currentTimeMillis();
        Long masterId = taskService.taskMaster(batchNo,taskRegistryDO, date, invokeType, size,0,pages);
        try{
            basePages(AccountingParamDTO.builder().pageNo(pageNo).masterId(masterId).pages(pages).checkDate(date).page(page).build(),this::funcPages);
            redissonService.setChannelMasterId(batchNo,isExecute);
        }catch (Exception e){
            log.error("创建渠道对账任务分片异常,checkDate:{},taskRegistryDO:{},invokeType:{},Exception:{}",date, JSONObject.toJSONString(taskRegistryDO),invokeType,e);
            taskService.deleteTaskMaster(masterId);
        }
    }

    public List<Long> funcPages(AccountingParamDTO accountingParamDTO){
        Page<Long> page = PageHelper.startPage(accountingParamDTO.getPageNo(), maxSize)
                .doSelectPage(() -> thirdPartyBillMapper.selectByCheckDate(accountingParamDTO.getCheckDate(),accountingParamDTO.getStartDateTime(),accountingParamDTO.getEndDateTime(),accountingParamDTO.getVoucher()));
        if(page == null){
            return Collections.emptyList();
        }
        return page.getResult();
    }

    public void accountingIncrementBurst(AccountingParamDTO accountingParamDTO, TaskRegistryDO taskRegistryDO, Integer invokeType){
        Integer pageNo = 1 ;
        Long pages = pagesCount(pageNo,accountingParamDTO.getCheckDate(),size,accountingParamDTO.getStartDateTime(),accountingParamDTO.getEndDateTime(),accountingParamDTO.getVoucher());
        Page<Long> page = pagesAccounting(pageNo,accountingParamDTO.getCheckDate(),maxSize,accountingParamDTO.getStartDateTime(),accountingParamDTO.getEndDateTime(),accountingParamDTO.getVoucher());
        if (page == null || CollectionUtils.isEmpty(page.getResult())) {
            log.info("AccountingIncrementBurstJob 没有账单数据,accountingParamDTO:{},Page:{}",JSONObject.toJSONString(accountingParamDTO),page);
            return;
        }
        String batchNo = "CH" + System.currentTimeMillis();
        // 生成分片主表数据
        Long masterId = taskService.taskMaster(batchNo,taskRegistryDO, accountingParamDTO.getCheckDate(), invokeType, size,0,pages);
        try{
            basePages(AccountingParamDTO.builder().pageNo(pageNo).masterId(masterId).pages(pages).checkDate(accountingParamDTO.getCheckDate()).startDateTime(accountingParamDTO.getStartDateTime()).endDateTime(accountingParamDTO.getEndDateTime()).voucher(accountingParamDTO.getVoucher()).page(page).build(),this::funcPages);
            redissonService.setChannelIncrementMasterId(masterId);
        }catch (Exception e){
            log.error("创建增量渠道对账分片任务异常,accountingParamDTO:{},taskRegistryDO:{},invokeType:{},Exception:{}",JSONObject.toJSONString(accountingParamDTO), JSONObject.toJSONString(taskRegistryDO),invokeType,e.getMessage());
            taskService.deleteTaskMaster(masterId);
        }
    }
}
