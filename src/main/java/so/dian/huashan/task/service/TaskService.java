package so.dian.huashan.task.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.common.enums.Deleted;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.handler.AbstractTaskHandler;
import so.dian.huashan.task.handler.context.TaskMasterContext;
import so.dian.huashan.task.handler.context.TaskRegistryContext;
import so.dian.huashan.task.handler.context.TaskSubContext;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class TaskService {

    @Resource
    private TaskMasterMapper taskMasterMapper;

    @Resource
    private Map<String, AbstractTaskHandler> handlerMap;

    public Long taskMaster(String batchNo,TaskRegistryDO taskRegistryDO,Integer date,Integer invokeType,Integer size,Integer model,Long allTimes){
        TaskRegistryContext  taskRegistryContext = JSONObject.parseObject(taskRegistryDO.getExtend(),TaskRegistryContext.class);
        AbstractTaskHandler abstractTaskHandler = null;
        if(taskRegistryContext.getIsSubTask()){
            abstractTaskHandler = handlerMap.get(CommonConstants.TASK_SUB);
        }else{
            abstractTaskHandler = handlerMap.get(CommonConstants.TASK_VOUCHER);
        }
        Long masterId = abstractTaskHandler.taskMaster(TaskMasterContext.builder().batchNo(batchNo).name(taskRegistryDO.getTaskName()).taskRegistryId(taskRegistryDO.getId()).invokeDate(date)
                .invokeType(invokeType).model(model).startTime(new Date()).step(size).allTimes(allTimes).build());
        return masterId;
    }

    public void taskSub(Long masterId, String voucher,Integer pageSize, Integer size, String key){
        AbstractTaskHandler abstractTaskHandler = handlerMap.get(key);
        // 生成分片数据
        TaskSubContext taskSubContext = new TaskSubContext();
        taskSubContext.setTaskMasterId(masterId);
        taskSubContext.setVoucher(voucher);
        if (pageSize < size) {
            taskSubContext.setIsEnd(true);
            taskSubContext.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
        }
        abstractTaskHandler.process(taskSubContext);
    }

    public void taskSubs(Long masterId, List<String> vouchers, Integer pageSize, Integer size, String key){
        AbstractTaskHandler abstractTaskHandler = handlerMap.get(key);
        // 生成分片数据
        List<TaskSubContext> list = new ArrayList<>();
        for(String voucher : vouchers){
            TaskSubContext taskSubContext = new TaskSubContext();
            taskSubContext.setTaskMasterId(masterId);
            taskSubContext.setVoucher(voucher);
            if (pageSize < size) {
                taskSubContext.setIsEnd(true);
                taskSubContext.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
            }
            list.add(taskSubContext);
        }
        abstractTaskHandler.process(list);
    }

    public void deleteTaskMaster(Long id){
        TaskMasterDO taskMasterDO = new TaskMasterDO();
        taskMasterDO.setId(id);
        taskMasterDO.setDeleted(Deleted.DELETE.getCode());
        taskMasterDO.setFinishTime(new Date());
        taskMasterDO.modify();
        taskMasterMapper.updateByPrimaryKeySelective(taskMasterDO);
    }

    public void successTaskMaster(Long id){
        TaskMasterDO taskMasterDO = new TaskMasterDO();
        taskMasterDO.setId(id);
        taskMasterDO.setStatus(TaskStatusEnum.BURST_SUCCESS.code());
        taskMasterDO.setFinishTime(new Date());
        taskMasterDO.modify();
        taskMasterMapper.updateByPrimaryKeySelective(taskMasterDO);
    }
}
