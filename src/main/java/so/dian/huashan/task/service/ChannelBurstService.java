package so.dian.huashan.task.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import so.dian.huashan.check.mapper.ResultChannelBillMapper;
import so.dian.huashan.collection.mapper.ThirdPartyBillMapper;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.task.job.dto.AccountingParamDTO;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class ChannelBurstService {

    @Resource
    private ThirdPartyBillMapper thirdPartyBillMapper;

    @Resource
    private ResultChannelBillMapper resultChannelBillMapper;

    @Resource
    private TaskService taskService;

    @Resource
    private RedissonClient redissonClient;

    private static final Integer maxSize = 10000;

    private static final Integer SHARD_SIZE = 500;

    public void basePages(AccountingParamDTO accountingParamDTO,Function<AccountingParamDTO, List<Long>> findIdsFunc){
        // 生成分片主表数据
        long start = System.currentTimeMillis();
        List<String> list = new ArrayList<>();
        Page<Long> page = accountingParamDTO.getPage();
        Integer pageNo = accountingParamDTO.getPageNo();
        List<Long> pageList = page.getResult();
        int size = getShardSize();
        while (CollectionUtils.isNotEmpty(pageList)) {

            List<List<Long>> subs = ListUtils.partition(pageList, size);
            // 生成分片数据
            Integer sizePage = pageList.size();
            list.addAll(subs.stream().map(Object::toString).collect(Collectors.toList()));

            if (pageList.size() < maxSize) {
                taskService.taskSubs(accountingParamDTO.getMasterId(), list, sizePage, size, CommonConstants.TASK_SUB);
                break;
            }
            if(list.size() >= 1000){
                taskService.taskSubs(accountingParamDTO.getMasterId(), list, sizePage, size, CommonConstants.TASK_SUB);
                list.clear();
            }
            pageList = findIdsFunc.apply(AccountingParamDTO.builder().pageNo(++pageNo).checkDate(accountingParamDTO.getCheckDate())
                    .startDateTime(accountingParamDTO.getStartDateTime()).endDateTime(accountingParamDTO.getEndDateTime()).voucher(accountingParamDTO.getVoucher()).build());
            if (CollectionUtils.isEmpty(pageList)) {
                taskService.taskSubs(accountingParamDTO.getMasterId(), list, sizePage, size, CommonConstants.TASK_SUB);
                break;
            }
        }
        taskService.successTaskMaster(accountingParamDTO.getMasterId());
        RAtomicLong allTimesLong = redissonClient.getAtomicLong("huashan_all_times:" + accountingParamDTO.getMasterId());
        allTimesLong.set(accountingParamDTO.getPages());
        log.info("allTimesLong：{},masterId={},pages.getPages()={}",allTimesLong.get(),accountingParamDTO.getMasterId(),accountingParamDTO.getPages());
    }
    protected Page<Long> pagesAccounting(Integer pageNo,Integer date,Integer size,Long startDateTime,Long endDateTime,List<Long> voucher){
        Page<Long> pages = PageHelper.startPage(pageNo, size)
                .doSelectPage(() -> thirdPartyBillMapper.selectByCheckDate(date,startDateTime,endDateTime,voucher));
        return pages;
    }

    protected Long pagesCount(Integer pageNo,Integer date,Integer size,Long startDateTime,Long endDateTime,List<Long> voucher){
        Page<Long> pages = PageHelper.startPage(pageNo, size)
                .doSelectPage(() -> thirdPartyBillMapper.selectByCheckDate(date,startDateTime,endDateTime,voucher));
        if(pages == null || CollectionUtils.isEmpty(pages.getResult())){
            return 0L;
        }
        return Long.valueOf(pages.getPages());
    }


    protected Page<Long> pagesDifferen(Integer pageNo,Integer date,Integer size,Long startDateTime,Long endDateTime,List<Long> voucher){
        Page<Long> pages = PageHelper.startPage(pageNo, size)
                .doSelectPage(() -> resultChannelBillMapper.selectByCheckDate(date,startDateTime,endDateTime,voucher));
        return pages;
    }

    protected Long pagesDifferenCount(Integer pageNo,Integer date,Integer size,Long startDateTime,Long endDateTime,List<Long> voucher){
        Page<Long> pages = PageHelper.startPage(pageNo, size)
                .doSelectPage(() -> resultChannelBillMapper.selectByCheckDate(date,startDateTime,endDateTime,voucher));
        if(pages == null || CollectionUtils.isEmpty(pages.getResult())){
            return 0L;
        }
        return Long.valueOf(pages.getPages());
    }

    protected int getShardSize() {
        return SHARD_SIZE;
    }
}
