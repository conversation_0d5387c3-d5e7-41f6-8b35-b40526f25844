package so.dian.huashan.task.service;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: xingba
 * @create: 2023/03/22 16:52
 * @description:
 */
@Service
@Slf4j
public class RedissonService {

    @Resource
    private RedissonClient redissonClient;

    private static final String app = "huashan_";

    private static final String key_times = "all_times:";

    private static final String key_master_id = "master_id:";

    public void setTotalPage(Long masterId,Long pages){
        RAtomicLong allTimesLong = redissonClient.getAtomicLong(app + key_times + masterId);
        allTimesLong.set(pages);
    }

    public Long getTotalPage(Long masterId){
        RAtomicLong allTimesLong = redissonClient.getAtomicLong(key_times + masterId);
        return allTimesLong.get();
    }

    public void setChannelMasterId(String batchNo, String isExecute){
        RBucket<Map> bucket = redissonClient.getBucket(app + key_master_id + "channel");
        Map map = new HashMap();
        map.put("batchNo",batchNo);
        map.put("isExecute",isExecute);
        bucket.set(map,12, TimeUnit.HOURS);
    }

    public Map getChannelMasterId(){
        RBucket<Map> bucket = redissonClient.getBucket(app + key_master_id + "channel");
        return bucket.get();
    }

    public void setDifferenceMasterId(String batchNo, String isExecute){
        RBucket<Map> bucket = redissonClient.getBucket(app + key_master_id + "difference");
        Map map = new HashMap();
        map.put("batchNo",batchNo);
        map.put("isExecute",isExecute);
        bucket.set(map,12, TimeUnit.HOURS);
    }

    public Map getDifferenceMasterId(){
        RBucket<Map> bucket = redissonClient.getBucket(app + key_master_id + "difference");
        return bucket.get();
    }

    public void setChannelIncrementMasterId(Long masterId){
        RBucket<Long> bucket = redissonClient.getBucket(app + key_master_id + "channel_increment");
        bucket.set(masterId,2, TimeUnit.MINUTES);
    }

    public Long getChannelIncrementMasterId(){
        RBucket<Long> bucket = redissonClient.getBucket(app + key_master_id + "channel_increment");
        return bucket.get();
    }

    public void setDifferenceIncrementMasterId(Long masterId){
        RBucket<Long> bucket = redissonClient.getBucket(app + key_master_id + "difference_increment");
        bucket.set(masterId,2, TimeUnit.MINUTES);
    }

    public Long getDifferenceIncrementMasterId(){
        RBucket<Long> bucket = redissonClient.getBucket(app + key_master_id + "difference_increment");
        return bucket.get();
    }
}
