package so.dian.huashan.task.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.task.mapper.entity.TaskVoucherDO;

public interface TaskVoucherMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TaskVoucherDO record);

    int insertSelective(TaskVoucherDO record);

    TaskVoucherDO selectByPrimaryKey(Long id);

    TaskVoucherDO selectByTaskMasterId(@Param("taskMasterId") Long taskMasterId);

    int updateByPrimaryKeySelective(TaskVoucherDO record);

    int updateByPrimaryKey(TaskVoucherDO record);
}