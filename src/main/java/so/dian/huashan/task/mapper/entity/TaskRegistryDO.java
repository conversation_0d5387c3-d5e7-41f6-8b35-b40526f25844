package so.dian.huashan.task.mapper.entity;

import lombok.Data;

/**
 * task_registry
 * <AUTHOR>
@Data
public class TaskRegistryDO extends BaseDO {
    /**
     * 任务类型（1-定时任务，2-手动触发）
     */
    private Integer taskType;

    /**
     * 任务名称
     */
    private String taskName;

    private String extend;

    /**
     * 任务执行器
     */
    private String taskActuator;

    /**
     * 开启状态（1-开启，2-关闭）
     */
    private Integer openStatus;

    private Long bizDomainId;
}