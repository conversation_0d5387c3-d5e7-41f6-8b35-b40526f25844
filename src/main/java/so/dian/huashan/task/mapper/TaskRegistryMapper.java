package so.dian.huashan.task.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;

public interface TaskRegistryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TaskRegistryDO record);

    int insertSelective(TaskRegistryDO record);

    TaskRegistryDO selectByPrimaryKey(Long id);

    TaskRegistryDO selectByName(@Param("taskName") String taskName);

    int updateByPrimaryKeySelective(TaskRegistryDO record);

    int updateByPrimaryKey(TaskRegistryDO record);
}