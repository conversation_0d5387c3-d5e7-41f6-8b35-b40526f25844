package so.dian.huashan.task.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.example.TaskMasterExample;

import java.util.List;

public interface TaskMasterMapper {
    int deleteByPrimaryKey(Long id);

    int deleteById(Long id);

    int insert(TaskMasterDO record);

    int insertSelective(TaskMasterDO record);

    TaskMasterDO selectByPrimaryKey(Long id);

    List<TaskMasterDO> selectByPrimaryKeys(@Param("ids") List<Long> ids);

    TaskMasterDO selectByBatchNo(@Param("batchNo")String batchNo);

    List<TaskMasterDO> selectByExample(TaskMasterExample example);

    List<TaskMasterDO> selectByIds(@Param("list") List<Long> list,@Param("invokeType")Integer invokeType);

    TaskMasterDO selectByInvokeTypeInvokeDate(@Param("invokeDate") Integer invokeDate,
            @Param("invokeType")Integer invokeType);

    List<TaskMasterDO> selectByTaskRegistryIdInvokeDate(@Param("invokeDate") Integer invokeDate,
                                              @Param("taskRegistryId")Long taskRegistryId);

    int updateByPrimaryKeySelective(TaskMasterDO record);

    int updateByIdCAS(@Param("id") Long id,@Param("successTimes") Long successTimes,@Param("failTimes") Long failTimes
            ,@Param("oldSuccessTimes") Long oldSuccessTimes,@Param("oldFailTimes") Long oldFailTimes,@Param("gmtUpdate") Long gmtUpdate);

    int updateByPrimaryKey(TaskMasterDO record);

    int batchUpdateStatus(@Param("taskMasters") List<TaskMasterDO> taskMasters);
}