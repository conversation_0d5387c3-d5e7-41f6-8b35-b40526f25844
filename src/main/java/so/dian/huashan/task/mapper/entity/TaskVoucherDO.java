package so.dian.huashan.task.mapper.entity;

import java.util.Date;
import lombok.Data;

/**
 * task_voucher
 * <AUTHOR>
@Data
public class TaskVoucherDO extends BaseDO{
    /**
     * 注册表id
     */
    private Long taskRegistryId;

    /**
     * 任务主表id
     */
    private Long taskMasterId;

    /**
     * 执行的job或者接口的名称
     */
    private String name;

    /**
     * 执行凭证
     */
    private String voucher;

    /**
     * 执行状态（0-初始化，1-进行中，2-成功，3-失败）
     */
    private Integer status;

    /**
     * 调用时间
     */
    private Date invokeTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 执行结果
     */
    private String invokeResult;
}