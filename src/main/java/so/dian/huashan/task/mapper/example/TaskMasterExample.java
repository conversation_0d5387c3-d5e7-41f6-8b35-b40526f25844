package so.dian.huashan.task.mapper.example;

import cn.hutool.core.collection.CollUtil;
import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/14 09:57
 * @description:
 */
@Getter
public class TaskMasterExample {

    private final Long taskRegistryId;

    private final List<Integer> statuses;

    @Builder
    TaskMasterExample(Long taskRegistryId, List<Integer> statuses) {
        this.taskRegistryId = taskRegistryId;
        this.statuses = CollUtil.isEmpty(statuses) ? null : statuses;
    }
}
