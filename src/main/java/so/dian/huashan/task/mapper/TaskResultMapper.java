package so.dian.huashan.task.mapper;

import so.dian.huashan.task.mapper.entity.TaskResultDO;

public interface TaskResultMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TaskResultDO record);

    int insertSelective(TaskResultDO record);

    TaskResultDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TaskResultDO record);

    int updateByPrimaryKey(TaskResultDO record);
}