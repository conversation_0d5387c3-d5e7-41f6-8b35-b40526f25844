package so.dian.huashan.task.mapper.example;

import cn.hutool.core.collection.CollUtil;
import lombok.Builder;
import lombok.Getter;
import so.dian.himalaya.common.entity.PageRequest;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/14 10:19
 * @description:
 */
@Getter
public class TaskSubExample {

    private final Long taskMasterId;

    private final List<Integer> statuses;

    private final IdHashExample idHash;

    private final PageRequest page;

    private final List<Long> ids;

    @Builder
    TaskSubExample(Long taskMasterId,
                   List<Integer> statuses,
                   IdHashExample idHash,
                   PageRequest page,
                   List<Long> ids) {
        this.taskMasterId = taskMasterId;
        this.statuses = CollUtil.isEmpty(statuses) ? null : statuses;
        this.ids = CollUtil.isEmpty(ids) ? null : ids;
        this.idHash = idHash;
        this.page = page;
    }
}
