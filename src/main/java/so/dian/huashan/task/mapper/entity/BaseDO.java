package so.dian.huashan.task.mapper.entity;

import lombok.Data;
import so.dian.himalaya.common.enums.DeletedEnum;

import java.io.Serializable;

/**
 * BaseDO
 *
 * <AUTHOR>
 * @desc
 * @date 2022/8/26 15:53
 */
@Data
public class BaseDO implements Serializable {

    private Long id;

    private Integer deleted;

    private Long gmtCreate;

    private Long gmtUpdate;

    public void init() {
        this.deleted = DeletedEnum.NOT_DELETED.getCode();

        this.gmtCreate = System.currentTimeMillis();

        this.gmtUpdate = System.currentTimeMillis();
    }

    public void modify() {
        this.gmtUpdate = System.currentTimeMillis();
    }

    public void delete() {
        this.deleted = DeletedEnum.DELETED.getCode();
        modify();
    }
}
