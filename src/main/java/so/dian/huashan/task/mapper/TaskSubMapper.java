package so.dian.huashan.task.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.task.job.dto.TaskSubDTO;
import so.dian.huashan.task.mapper.entity.TaskSubDO;
import so.dian.huashan.task.mapper.example.TaskSubExample;

import java.util.List;

public interface TaskSubMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TaskSubDO record);

    int insertSelective(TaskSubDO record);

    int insertBatch(List<TaskSubDO> list);

    TaskSubDO selectByPrimaryKey(Long id);

    List<TaskSubDO> selectByExample(TaskSubExample example);

    List<Long> selectByFailStatus(@Param("gmtUpdate")Long gmtUpdate);

    List<TaskSubDO> selectByTaskMasterIdStatus(@Param("index") Integer index,@Param("total") Integer total,@Param("taskMasterId") Long taskMasterId,@Param("statusList") List<Integer> statusList);

    List<TaskSubDO> selectByTaskMasterIdStatusList(@Param("taskMasterId") Long taskMasterId,@Param("statusList") List<Integer> statusList);

    int updateByPrimaryKeySelective(TaskSubDO record);

    int updateByPrimaryKey(TaskSubDO record);

    List<TaskSubDTO> statistics(@Param("taskMasterId") Long taskMasterId);

    List<TaskSubDTO> statisticsByMasterIds(@Param("taskMasterIds") List<Long> taskMasterIds);

    int updateStatusByPrimaryKey(@Param("id")Long id, @Param("oldStatus") Integer oldStatus, @Param("expectStatus") Integer expectStatus);

    List<Long> selectMasterIdBySubIds(@Param("subIds") List<Long> subIds);
}