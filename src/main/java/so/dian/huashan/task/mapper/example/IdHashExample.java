package so.dian.huashan.task.mapper.example;

import lombok.Getter;

import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/18 11:13
 * @description:
 */
@Getter
public class IdHashExample {

    private final Integer factor;

    private final Integer index;

    public IdHashExample(Integer factor, Integer index) {
        this.factor = Objects.requireNonNull(factor, "factor must not null");
        this.index = Objects.requireNonNull(index, "index must not null");
    }
}

