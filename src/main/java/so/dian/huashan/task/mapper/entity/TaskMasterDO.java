package so.dian.huashan.task.mapper.entity;

import lombok.Data;

import java.util.Date;

/**
 * task_master
 * <AUTHOR>
@Data
public class TaskMasterDO extends BaseDO{

    /**
     * 任务注册id
     */
    private Long taskRegistryId;

    private String batchNo;

    /**
     * 执行日
     */
    private Integer invokeDate;

    /**
     * 执行类型（1-渠道对账，2-渠道差异对账，3-支付宝渠道拉取，4-微信渠道拉取，5-采集补偿）
     */
    private Integer invokeType;

    private Integer model;

    /**
     * 执行状态（0-初始化，1-进行中，2-成功，3-失败）
     */
    private Integer status;

    /**
     * 执行开始时间
     */
    private Date startTime;

    /**
     * 执行结束时间
     */
    private Date finishTime;

    /**
     * 步长
     */
    private Integer step;

    /**
     * 总次数
     */
    private Long allTimes;

    /**
     * 成功次数
     */
    private Long successTimes;

    /**
     * 失败次数
     */
    private Long failTimes;

}