package so.dian.huashan.task.handler.context;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * TaskMasterContext
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 16:13
 */
@Data
@Builder
public class TaskMasterContext {

    /**
     * 任务注册id
     */
    private Long taskRegistryId;

    private String batchNo;

    /**
     * 执行日
     */
    private Integer invokeDate;

    /**
     * 执行类型（1-渠道对账，2-渠道差异对账，3-支付宝渠道拉取，4-微信渠道拉取，5-采集补偿）
     */
    private Integer invokeType;

    private Integer model;

    /**
     * 执行开始时间
     */
    private Date startTime;

    /**
     * 执行步长
     */
    private Integer step;

    private Long allTimes;

    /**
     * 任务名称
     */
    private String name;
}
