package so.dian.huashan.task.handler.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.handler.AbstractTaskHandler;
import so.dian.huashan.task.handler.context.TaskSubContext;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TaskSubHandler
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 10:00
 */
@Slf4j
@Component(value = CommonConstants.TASK_SUB)
public class TaskSubHandler extends AbstractTaskHandler {
    @Resource
    private TaskSubMapper taskSubMapper;

    @Override
    @Transactional
    public void process(TaskSubContext taskSubContext) {
        TaskSubDO taskSubDO = new TaskSubDO();
        taskSubDO.setTaskMasterId(taskSubContext.getTaskMasterId());
        taskSubDO.setVoucher(taskSubContext.getVoucher());
        taskSubDO.setStatus(TaskStatusEnum.INIT.code());
        taskSubDO.init();
        taskSubMapper.insertSelective(taskSubDO);
        // 更新主表为成功状态
        if (taskSubContext.getIsEnd()) {
            result(taskSubContext.getTaskMasterId(), taskSubContext.getStatus(), "sub");
        }
    }

    @Override
    public void process(List<TaskSubContext> list) {
        List<TaskSubDO> taskList = list.stream().map(o -> {
            TaskSubDO taskSubDO = new TaskSubDO();
            taskSubDO.setTaskMasterId(o.getTaskMasterId());
            taskSubDO.setVoucher(o.getVoucher());
            taskSubDO.setStatus(TaskStatusEnum.INIT.code());
            taskSubDO.init();
            return taskSubDO;
        }).collect(Collectors.toList());

        taskSubMapper.insertBatch(taskList);
        // 更新主表为成功状态
        TaskSubContext taskSubContext = list.get(0);
        if (taskSubContext.getIsEnd()) {
            result(taskSubContext.getTaskMasterId(), TaskStatusEnum.BURST_SUCCESS.code(), "sub");
        }
    }
}
