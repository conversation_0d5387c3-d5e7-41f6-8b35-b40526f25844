package so.dian.huashan.task.handler.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huashan.common.constant.CommonConstants;
import so.dian.huashan.task.handler.AbstractTaskHandler;
import so.dian.huashan.task.handler.context.TaskSubContext;

import java.util.List;

/**
 * TaskVoucherHandler
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 10:00
 */
@Slf4j
@Component(value = CommonConstants.TASK_VOUCHER)
public class TaskVoucherHandler extends AbstractTaskHandler {

    @Override
    public void process(TaskSubContext taskSubContext) {
        result(taskSubContext.getTaskMasterId(),taskSubContext.getStatus(),"voucher");
    }

    @Override
    public void process(List<TaskSubContext> taskSubContext) {

    }
}
