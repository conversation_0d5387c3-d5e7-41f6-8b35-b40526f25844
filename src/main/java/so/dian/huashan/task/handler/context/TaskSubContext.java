package so.dian.huashan.task.handler.context;

import lombok.Data;

/**
 * TaskSubContext
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 16:13
 */
@Data
public class TaskSubContext {

    /**
     * 任务主表id
     */
    private Long taskMasterId;

    /**
     * 凭证id
     */
    private String voucher;

    /**
     * 是否结束分片
     */
    private Boolean isEnd = false;

    /**
     * 状态
     */
    private Integer status;
}
