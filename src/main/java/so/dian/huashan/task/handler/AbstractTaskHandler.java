package so.dian.huashan.task.handler;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.task.enums.TaskStatusEnum;
import so.dian.huashan.task.handler.context.TaskMasterContext;
import so.dian.huashan.task.handler.context.TaskResultContext;
import so.dian.huashan.task.handler.context.TaskSubContext;
import so.dian.huashan.task.mapper.TaskMasterMapper;
import so.dian.huashan.task.mapper.TaskRegistryMapper;
import so.dian.huashan.task.mapper.TaskVoucherMapper;
import so.dian.huashan.task.mapper.entity.TaskMasterDO;
import so.dian.huashan.task.mapper.entity.TaskRegistryDO;
import so.dian.huashan.task.mapper.entity.TaskVoucherDO;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * AbstractTaskHandler
 *
 * <AUTHOR>
 * @desc
 * @date 2023/3/15 16:13
 */
@Component
public abstract class AbstractTaskHandler {

   @Resource
   private TaskMasterMapper taskMasterMapper;

   @Resource
   private TaskRegistryMapper taskRegistryMapper;

   @Resource
   private TaskVoucherMapper taskVoucherMapper;

   @Transactional
   public Long taskMaster(TaskMasterContext taskMasterContext){
      TaskRegistryDO taskRegistryDO = taskRegistryMapper.selectByPrimaryKey(taskMasterContext.getTaskRegistryId());
      if(!taskRegistryDO.getOpenStatus().equals(1)){
         throw BizException.create(BizResult.error("10001","没查到具体的任务注册信息"));
      }
      //生成主任务记录
      TaskMasterDO taskMasterDO = new TaskMasterDO();
      taskMasterDO.setTaskRegistryId(taskMasterContext.getTaskRegistryId());
      taskMasterDO.setBatchNo(taskMasterContext.getBatchNo());
      taskMasterDO.setStep(taskMasterContext.getStep());
      taskMasterDO.setStartTime(taskMasterContext.getStartTime());
      taskMasterDO.setInvokeDate(taskMasterContext.getInvokeDate());
      taskMasterDO.setInvokeType(taskMasterContext.getInvokeType());
      taskMasterDO.setModel(taskMasterContext.getModel() == null ? 0 : taskMasterContext.getModel());
      taskMasterDO.setAllTimes(taskMasterContext.getAllTimes());
      taskMasterDO.setSuccessTimes(0L);
      taskMasterDO.setFailTimes(0L);
      taskMasterDO.setStatus(TaskStatusEnum.PROGRESS.code());
      taskMasterDO.init();
      taskMasterMapper.insertSelective(taskMasterDO);
      // 生成对应凭证
      TaskVoucherDO taskVoucherDO = new TaskVoucherDO();
      taskVoucherDO.setTaskMasterId(taskMasterDO.getId());
      taskVoucherDO.setTaskRegistryId(taskMasterContext.getTaskRegistryId());
      taskVoucherDO.setName(taskMasterContext.getName());
      taskVoucherDO.setInvokeTime(new Date());
      taskVoucherDO.setVoucher(JSONObject.toJSONString(taskRegistryDO));
      taskVoucherDO.setStatus(TaskStatusEnum.PROGRESS.code());
      taskVoucherDO.setInvokeResult("progressing");
      taskVoucherDO.init();
      taskVoucherMapper.insertSelective(taskVoucherDO);
      return taskMasterDO.getId();
   }

   public abstract void process(TaskSubContext taskSubContext);

   public abstract void process(List<TaskSubContext> list);

   public void masterResult(TaskResultContext taskResultContext){
      TaskMasterDO taskMasterDO = taskMasterMapper.selectByPrimaryKey(taskResultContext.getTaskMasterId());
      taskMasterDO.modify();
      TaskVoucherDO taskVoucherDO = taskVoucherMapper.selectByTaskMasterId(taskMasterDO.getId());
      if(taskResultContext.getStatus() != null && taskResultContext.getStatus().equals(TaskStatusEnum.EXECUTE_FAIL.code())){
         taskMasterDO.setStatus(TaskStatusEnum.EXECUTE_FAIL.code());
         taskVoucherDO.setStatus(TaskStatusEnum.EXECUTE_FAIL.code());
         taskVoucherDO.setInvokeResult("fail");
      }
      if(taskResultContext.getStatus() == null || taskResultContext.getStatus().equals(TaskStatusEnum.EXECUTE_SUCCESS.code())){
         taskMasterDO.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
         taskVoucherDO.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
         taskVoucherDO.setInvokeResult("success");
      }
      taskMasterDO.setSuccessTimes(taskResultContext.getSuccessTimes());
      taskMasterDO.setFailTimes(taskResultContext.getFailTimes());
      taskMasterMapper.updateByPrimaryKeySelective(taskMasterDO);
      // 更新凭证数据（时间和状态）
      taskVoucherDO.setFinishTime(new Date());
      taskVoucherDO.modify();
      taskVoucherMapper.updateByPrimaryKeySelective(taskVoucherDO);
   }

   @Transactional
   public void result(Long taskMasterId,Integer status,String taskResultType){
      TaskMasterDO taskMasterDO = taskMasterMapper.selectByPrimaryKey(taskMasterId);
      TaskVoucherDO taskVoucherDO = taskVoucherMapper.selectByTaskMasterId(taskMasterDO.getId());

      taskMasterDO.modify();
      if(taskResultType.equals("sub")){
         if(status != null && status.equals(TaskStatusEnum.BURST_FAIL.code())){
            taskMasterDO.setStatus(TaskStatusEnum.BURST_FAIL.code());
            taskVoucherDO.setStatus(TaskStatusEnum.BURST_FAIL.code());
            taskVoucherDO.setInvokeResult("fail");
         }
         if(status == null || status.equals(TaskStatusEnum.BURST_SUCCESS.code())){
            taskMasterDO.setStatus(TaskStatusEnum.BURST_SUCCESS.code());
            taskVoucherDO.setStatus(TaskStatusEnum.BURST_SUCCESS.code());
            taskVoucherDO.setInvokeResult("success");
         }
      }else{
         if(status != null && status.equals(TaskStatusEnum.EXECUTE_FAIL.code())){
            taskMasterDO.setStatus(TaskStatusEnum.EXECUTE_FAIL.code());
            taskVoucherDO.setStatus(TaskStatusEnum.EXECUTE_FAIL.code());
            taskVoucherDO.setInvokeResult("fail");
         }
         if(status == null || status.equals(TaskStatusEnum.EXECUTE_SUCCESS.code())){
            taskMasterDO.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
            taskVoucherDO.setStatus(TaskStatusEnum.EXECUTE_SUCCESS.code());
            taskVoucherDO.setInvokeResult("success");
         }
      }

      taskMasterMapper.updateByPrimaryKeySelective(taskMasterDO);
      // 更新凭证数据（时间和状态）
      taskVoucherDO.setFinishTime(new Date());
      taskVoucherDO.modify();
      taskVoucherMapper.updateByPrimaryKeySelective(taskVoucherDO);
      // TODO 落结果表
   }
}
