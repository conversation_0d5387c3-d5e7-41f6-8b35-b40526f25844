package so.dian.huashan.collection.facade;

import cn.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.mapper.CollectionPipelineMapper;
import so.dian.huashan.collection.mapper.dos.CollectionPipelineDO;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/12/25 10:22
 * @description:
 */
@Component
public class CollectionPipelineFacade {

    @Autowired
    private CollectionPipelineMapper collectionPipelineMapper;

    public CollectionPipelineBO find(Long id) {
        if (Objects.isNull(id))
            return null;

        CollectionPipelineDO pipelineDO = collectionPipelineMapper.selectByPrimaryKey(id);
        return CollectionPipelineBO.from(pipelineDO);
    }

    public CollectionPipelineBO findByTableName(String tableName) {
        if (Objects.isNull(tableName))
            return null;

        List<CollectionPipelineDO> pipelineDOS = collectionPipelineMapper.selectByTableName(tableName);
        return CollectionPipelineBO.from(CollUtil.getFirst(pipelineDOS));
    }

    public List<CollectionPipelineBO> listByDomainId(Long domainId) {
        if (Objects.isNull(domainId))
            return Collections.emptyList();

        List<CollectionPipelineDO> pipelineDOS = collectionPipelineMapper.selectByDomainId(domainId);
        return pipelineDOS.stream().map(CollectionPipelineBO::from).collect(Collectors.toList());
    }

    public Long save(CollectionPipelineDO pipelineDO) {
        if (Objects.isNull(pipelineDO))
            return null;

        if (Objects.isNull(pipelineDO.getId())) {
            collectionPipelineMapper.insertSelective(pipelineDO);
        }else {
            collectionPipelineMapper.updateByPrimaryKeySelective(pipelineDO);
        }
        return pipelineDO.getId();
    }
}
