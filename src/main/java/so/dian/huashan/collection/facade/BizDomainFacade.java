package so.dian.huashan.collection.facade;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.mapper.BizDomainDO;
import so.dian.huashan.collection.mapper.BizDomainMapper;
import so.dian.huashan.collection.service.entity.BizDomainBO;

import java.util.Objects;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/25 10:06
 * @description:
 */
@Component
public class BizDomainFacade {

    @Autowired
    private BizDomainMapper bizDomainMapper;

    public Long save(BizDomainDO domainDO) {
        if (Objects.isNull(domainDO))
            return null;

        if (Objects.isNull(domainDO.getId())) {
            bizDomainMapper.insertSelective(domainDO);
        }else {
            bizDomainMapper.updateByPrimaryKeySelective(domainDO);
        }

        return domainDO.getId();
    }

    public BizDomainBO find(Long id) {
        if (Objects.isNull(id))
            return null;

        BizDomainDO bizDomainDO = bizDomainMapper.selectByPrimaryKey(id);
        return BizDomainBO.from(bizDomainDO);
    }

    public BizDomainBO findByCode(String code) {
        if (StrUtil.isBlank(code))
            return null;

        BizDomainDO bizDomainDO = bizDomainMapper.selectByCode(code);
        return BizDomainBO.from(bizDomainDO);
    }
}
