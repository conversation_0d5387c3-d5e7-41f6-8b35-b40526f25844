package so.dian.huashan.collection.facade;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.collection.mapper.CollectionVoucherMapper;
import so.dian.huashan.collection.mapper.dos.CollectionVoucherDO;
import so.dian.huashan.collection.mapper.example.CollectionVoucherExample;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.common.enums.CollectionVoucherStatus;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/12/25 09:43
 * @description:
 */
@Component
public class CollectionVoucherFacade {

    @Value("${collection.voucher.retry.time:3}")
    private Integer retryTime;

    @Autowired
    private CollectionVoucherMapper collectionVoucherMapper;

    public void update(CollectionVoucherBO voucherBO) {
        if (Objects.isNull(voucherBO))
            return;
        collectionVoucherMapper.updateByPrimaryKeySelective(voucherBO.toDO());
    }

    public int update(Long voucherId, CollectionVoucherStatus status, Integer version) {
        if (Objects.isNull(voucherId) || Objects.isNull(version) || Objects.isNull(status))
            return 0;
        return collectionVoucherMapper.updateStatus(voucherId, status.getCode(), version);
    }

    public void batchSave(List<CollectionVoucherDO>  voucherDOS) {
        if (CollUtil.isEmpty(voucherDOS))
            return;

        collectionVoucherMapper.batchInsert(voucherDOS);
    }

    public void save(CollectionVoucherBO voucherBO) {
        if (Objects.isNull(voucherBO))
            return;

        CollectionVoucherDO voucherDO = voucherBO.toDO();
        collectionVoucherMapper.insertSelective(voucherDO);
        voucherBO.setId(voucherDO.getId());
    }

    public CollectionVoucherBO find(Long id) {
        if (Objects.isNull(id))
            return null;

        CollectionVoucherDO voucherDO = collectionVoucherMapper.selectByPrimaryKey(id);
        return CollectionVoucherBO.from(voucherDO);
    }

    public List<CollectionVoucherBO> listRetryableVoucher(Long minId, Integer queryCount, List<CollectionVoucherStatus> voucherStatuses) {

        if (CollUtil.isEmpty(voucherStatuses))
            return Collections.emptyList();

        CollectionVoucherExample example = CollectionVoucherExample.builder()
                .gtId(minId)
                .statusList(voucherStatuses)
                .limit(queryCount)
                .page(PageRequest.of(1, queryCount))
                .leRetryTime(retryTime)
                .gmtCreate(DateUtil.offsetHour(new Date(),-1).getTime())
                .build();
        List<CollectionVoucherDO> voucherDOS = collectionVoucherMapper.selectForRetry(example);
        return voucherDOS.stream().map(CollectionVoucherBO::from).collect(Collectors.toList());
    }
}
