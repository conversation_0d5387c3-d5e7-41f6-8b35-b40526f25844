package so.dian.huashan.collection.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/18 11:39
 * @description:
 */
@Getter
@Setter
@RefreshScope
@Component
@ConfigurationProperties("huashan.biz")
public class BizProperties {

    private List<Integer> notFindRefundBizTypes;

    /**
     * 走统一支付的业务类型
     */
    private List<Integer> unipayBizTypes;

    private SettleProperties settle;

    private List<Integer> skipBizTypes;

    @Data
    public static class SettleProperties {

        private String dingdingCode;

        private Integer maxLoop;

        private Integer deleteSize;

        private Long paySuccessTime;

        private Long createTime;
    }
}
