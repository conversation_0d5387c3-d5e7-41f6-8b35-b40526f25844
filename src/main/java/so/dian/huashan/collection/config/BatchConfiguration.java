package so.dian.huashan.collection.config;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;
import so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.collection.service.listener.BaseListener;
import so.dian.huashan.collection.service.listener.XiyoukeListener;
import so.dian.huashan.collection.service.thirdparty.BillRequestParam;
import so.dian.huashan.collection.service.thirdparty.PullBillReader;
import so.dian.huashan.collection.service.thirdparty.account.AccountshareBill;
import so.dian.huashan.collection.service.thirdparty.alipay.AlipayBill;
import so.dian.huashan.collection.service.thirdparty.weixin.WeixinBill;
import so.dian.huashan.collection.service.thirdparty.weixin.partner.WeixinpartnerBill;
import so.dian.huashan.collection.service.thirdparty.xiyouke.XiYouKeBill;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.enums.BatchJob;

import java.util.List;

/**
 * @author: miaoshuai
 * @create: 2023/03/15 11:37
 * @description: 批量处理配置
 */
@Configuration
public class BatchConfiguration {

    /**
     * 微信账务账单执行任务
     */
    @Bean
    public Job weixinBillJob(JobBuilderFactory jobs,
                             Step pullAccountFileStep,
                             Step weixinAccountFileParseStep,
                             JobRepository jobRepository, BaseListener baseListener) {
        return jobs.get(BatchJob.WEXIN_BILL_JOB.getJobName())
                .repository(jobRepository)
                .listener(baseListener)
                .flow(pullAccountFileStep)
                .next(weixinAccountFileParseStep)
                .end().build();
    }

    @Bean
    public Job shareAccountBillJob(JobBuilderFactory jobs,
                                   Step pullAccountFileStep,
                                   Step shareAccountFileParseStep,
                                   JobRepository jobRepository, BaseListener baseListener) {
        return jobs.get(BatchJob.SHARE_ACCOUNT_BILL_JOB.getJobName())
                .repository(jobRepository)
                .listener(baseListener)
                .flow(pullAccountFileStep)
                .next(shareAccountFileParseStep)
                .end().build();
    }


    @Bean
    public Job weixinPartnerBillJob(JobBuilderFactory jobs,
                             Step pullAccountFileStep,
                             Step weixinPartnerAccountFileParseStep,
                             JobRepository jobRepository, BaseListener baseListener) {
        return jobs.get(BatchJob.WEXIN_PARTNER_BILL_JOB.getJobName())
                .repository(jobRepository)
                .listener(baseListener)
                .flow(pullAccountFileStep)
                .next(weixinPartnerAccountFileParseStep)
                .end().build();
    }

    @Bean
    public Step weixinPartnerAccountFileParseStep(StepBuilderFactory stepBuilderFactory, @Qualifier("taskExecutor") TaskExecutor taskExecutor,
                                           ItemReader<WeixinpartnerBill> weixinPartnerBillItemReader,
                                           ItemProcessor<WeixinpartnerBill, WxFundBillDetailDO> weixinPartnerFileItemProcessor,
                                           ItemWriter<WxFundBillDetailDO> billAccountWriter,
                                           @Qualifier("stepExceptionListener") StepExecutionListener stepExecutionListener) {
        return stepBuilderFactory.get(BatchJob.WEXIN_PARTNER_BILL_JOB.getParseBillName()).
                        <WeixinpartnerBill, WxFundBillDetailDO>chunk(ChannelConstant.commitCount)
                .faultTolerant().skipLimit(9).skip(FlatFileParseException.class)
                .reader(weixinPartnerBillItemReader).processor(weixinPartnerFileItemProcessor)
                .writer(billAccountWriter).taskExecutor(taskExecutor).throttleLimit(5)
                .listener(stepExecutionListener)
                .build();
    }




    @Bean
    public Step shareAccountFileParseStep(StepBuilderFactory stepBuilderFactory, @Qualifier("taskExecutor") TaskExecutor taskExecutor,
                                                  ItemReader<AccountshareBill> shareAccountBillItemReader,
                                                  ItemProcessor<AccountshareBill, WxSplitBillDetailDO> shareAccountFileItemProcessor,
                                                  ItemWriter<WxSplitBillDetailDO> billAccountWriter,
                                                  @Qualifier("stepExceptionListener") StepExecutionListener stepExecutionListener) {
        return stepBuilderFactory.get(BatchJob.SHARE_ACCOUNT_BILL_JOB.getParseBillName()).
                        <AccountshareBill, WxSplitBillDetailDO>chunk(ChannelConstant.commitCount)
                .faultTolerant().skipLimit(9).skip(FlatFileParseException.class)
                .reader(shareAccountBillItemReader).processor(shareAccountFileItemProcessor)
                .writer(billAccountWriter).taskExecutor(taskExecutor).throttleLimit(5)
                .listener(stepExecutionListener)
                .build();
    }

    /**
     * 批处理第一步：账单拉取文件step1 1、获取配置信息； 2、调用三方渠道api拉取账单文件解压本地； 3、路径保存至任务表；
     */
    @Bean
    public Step pullAccountFileStep(StepBuilderFactory stepBuilderFactory,
                                    PullBillReader pullBillReader,
                                    ItemProcessor<BillRequestParam, List<String>> billPullProcessor,
                                    ItemWriter<List<String>> pullBillWriter,
                                    @Qualifier("stepExceptionListener") StepExecutionListener stepExecutionListener) {
        return stepBuilderFactory.get(ChannelConstant.BILL_PULL_STEP_NAME).
               <BillRequestParam, List<String>>chunk(1).
                reader(pullBillReader).
                processor(billPullProcessor)
                .writer(pullBillWriter)
                .listener(stepExecutionListener)
                .build();
    }

    /**
     * 批处理第二步：微信账务账单文件解析step2
     * <p>
     * Step主要分为三个部分 ItemReader,ItemProcessor,ItemWriter chunk通俗的讲类似于SQL的commit; 处理(processor)2000条后写入(writer)一次。
     * 捕捉5次异常JOB就停止并标志失败
     */
    @Bean
    public Step weixinAccountFileParseStep(StepBuilderFactory stepBuilderFactory, @Qualifier("taskExecutor") TaskExecutor taskExecutor,
                                           ItemReader<WeixinBill> weixinBillFileReader,
                                           ItemProcessor<WeixinBill, ThirdPartyBillDO> weixinFileItemProcessor,
                                           ItemWriter<ThirdPartyBillDO> billAccountWriter,
                                           @Qualifier("stepExceptionListener") StepExecutionListener stepExecutionListener) {
        return stepBuilderFactory.get(BatchJob.WEXIN_BILL_JOB.getParseBillName()).
                <WeixinBill, ThirdPartyBillDO>chunk(ChannelConstant.commitCount)
                .faultTolerant().skipLimit(9).skip(FlatFileParseException.class)
                .reader(weixinBillFileReader).processor(weixinFileItemProcessor)
                .writer(billAccountWriter).taskExecutor(taskExecutor).throttleLimit(5)
                .listener(stepExecutionListener)
                .build();
    }

    /*################################ 支付账单批处理配置开始 ########################################*/

    @Bean
    public Job alipayBillJob(JobBuilderFactory jobs,
                             Step pullAccountFileStep,
                             Step alipayAccountFileParseStep,
                             Step alipayBillFileSftpStep,
                             JobRepository jobRepository,BaseListener baseListener) {
        return jobs.get(BatchJob.ALIPAY_BILL_JOB.getJobName())
                .repository(jobRepository)
                .listener(baseListener)
                .flow(pullAccountFileStep)
                .next(alipayAccountFileParseStep)
                .next(alipayBillFileSftpStep)
                .end().build();
    }


    /**
     * 批处理第二步：支付宝账单文件解析step2
     * <p>
     * Step主要分为三个部分 ItemReader,ItemProcessor,ItemWriter chunk通俗的讲类似于SQL的commit; 处理(processor)2000条后写入(writer)一次。
     *    捕捉5次异常JOB就停止并标志失败
     */
    @Bean
    public Step alipayAccountFileParseStep(StepBuilderFactory stepBuilderFactory, @Qualifier("taskExecutor") TaskExecutor taskExecutor,
                                           ItemReader<AlipayBill> alipayBillFileReader,
                                           ItemProcessor<AlipayBill, ThirdPartyBillDO> alipayFileItemProcessor,
                                           ItemWriter<ThirdPartyBillDO> billAccountWriter,
                                           @Qualifier("stepExceptionListener") StepExecutionListener stepExecutionListener) {
        return stepBuilderFactory.get(BatchJob.ALIPAY_BILL_JOB.getParseBillName()).
                <AlipayBill, ThirdPartyBillDO>chunk(ChannelConstant.commitCount)
                .faultTolerant().skipLimit(15).skip(FlatFileParseException.class)
                .reader(alipayBillFileReader).processor(alipayFileItemProcessor)
                .writer(billAccountWriter).taskExecutor(taskExecutor)
                .throttleLimit(5)
                .listener(stepExecutionListener)
                .build();
    }

    /**
     * 批处理第三步：支付宝前端对账  账单文件上传sftp
     */
    @Bean
    public Step alipayBillFileSftpStep(StepBuilderFactory stepBuilderFactory,
                                       @Qualifier("taskExecutor") TaskExecutor taskExecutor,
                                       ItemReader<AlipayBill> alipayBillFileReader,
                                       ItemProcessor<AlipayBill, AlipayBill> alipayBillFilterProcessor,
                                       ItemWriter<AlipayBill> alipayBillFileSftpWriter,
                                       @Qualifier("alipayBillStepListener") StepExecutionListener alipayBillStepListener) {
        return stepBuilderFactory.get("alipayBillFileSftpStep")
                .<AlipayBill, AlipayBill>chunk(ChannelConstant.commitCount)
                .faultTolerant().skipLimit(15).skip(FlatFileParseException.class)
                .reader(alipayBillFileReader)
                .processor(alipayBillFilterProcessor)
                .writer(alipayBillFileSftpWriter)
                .taskExecutor(taskExecutor)
                .throttleLimit(5)
                .listener(alipayBillStepListener) // 在任务失败时清理临时文件
                .build();
    }

    @Bean
    public Job xiyoukeBillJob(JobBuilderFactory jobs,
                              Step pullAccountFileStep,
                              Step xiyoukeAccountFileParseStep,
                              JobRepository jobRepository, XiyoukeListener xiyoukeListener) {
        return jobs.get(BatchJob.XIYOUKE_BILL_JOB.getJobName())
                .repository(jobRepository)
                .listener(xiyoukeListener)
                .flow(pullAccountFileStep)
                .next(xiyoukeAccountFileParseStep)
                .end().build();
    }
    @Bean
    public Step xiyoukeAccountFileParseStep(StepBuilderFactory stepBuilderFactory, @Qualifier("taskExecutor") TaskExecutor taskExecutor,
                                           ItemReader<XiYouKeBill> xiYouKeBillFileReader,
                                           ItemProcessor<XiYouKeBill, PayBillThirdDO> xiYoukeFileItemProcessor,
                                           ItemWriter<PayBillThirdDO> billAccountWriter,
                                           @Qualifier("stepExceptionListener") StepExecutionListener stepExecutionListener) {
        return stepBuilderFactory.get(BatchJob.XIYOUKE_BILL_JOB.getParseBillName())
                .<XiYouKeBill, PayBillThirdDO>chunk(ChannelConstant.commitCount)
                .faultTolerant().skipLimit(15).skip(FlatFileParseException.class)
                .reader(xiYouKeBillFileReader).processor(xiYoukeFileItemProcessor)
                .writer(billAccountWriter).taskExecutor(taskExecutor)
                .throttleLimit(5)
                .listener(stepExecutionListener)
                .build();
    }


}
