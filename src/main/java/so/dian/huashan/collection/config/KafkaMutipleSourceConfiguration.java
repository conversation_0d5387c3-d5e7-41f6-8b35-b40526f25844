package so.dian.huashan.collection.config;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.kafka.ConcurrentKafkaListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/18 13:42
 * @description:
 */
@Component
public class KafkaMutipleSourceConfiguration {

    @Value("${spring.kafka.control.bootstrap-servers}")
    private String bootstrapServer;

    @Value("${spring.kafka.income.bootstrap-servers}")
    private String incomeServer;

    @Value("${spring.kafka.settle.bootstrap-servers}")
    private String settleServer;
    @Value("${spring.kafka.pay.bootstrap-servers}")
    private String payServer;
    @Value("${spring.kafka.iot.bootstrap-servers}")
    private String iotServer;

    @Bean(value = "bizKafkaContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> bizKafkaContainerFactory(ConcurrentKafkaListenerContainerFactoryConfigurer configurer) {

        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServer);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
//        factory.setConsumerFactory();
//        factory.setBatchListener(true);
//        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        configurer.configure(factory, new DefaultKafkaConsumerFactory<>(props));
        return factory;
    }

    @Bean(value = "incomeKafkaContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> incomeKafkaContainerFactory(ConcurrentKafkaListenerContainerFactoryConfigurer configurer) {

        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, incomeServer);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        configurer.configure(factory, new DefaultKafkaConsumerFactory<>(props));
        return factory;
    }

    @Bean(value = "settleKafkaContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> settleKafkaContainerFactory(ConcurrentKafkaListenerContainerFactoryConfigurer configurer) {

        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, settleServer);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        configurer.configure(factory, new DefaultKafkaConsumerFactory<>(props));
        return factory;
    }

    @Bean(value = "payBillKafkaContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> payBillKafkaContainerFactory(ConcurrentKafkaListenerContainerFactoryConfigurer configurer) {

        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, payServer);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        configurer.configure(factory, new DefaultKafkaConsumerFactory<>(props));
        return factory;
    }

    @Bean(value = "iotKafkaContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<?, ?> iotKafkaContainerFactory(ConcurrentKafkaListenerContainerFactoryConfigurer configurer) {

        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, iotServer);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        ConcurrentKafkaListenerContainerFactory<Object, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        configurer.configure(factory, new DefaultKafkaConsumerFactory<>(props));
        return factory;
    }
}
