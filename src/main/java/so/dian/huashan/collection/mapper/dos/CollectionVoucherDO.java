package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

import java.util.Date;

/**
 * collection_voucher
 */
@Data
public class CollectionVoucherDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 数据来源标识
     */
    private String source;

    /**
     * 执行状态（0-初始化，1-进行中，2-成功，3-失败）
     */
    private Byte status;

    /**
     * 重试次数
     */
    private Integer retryTime;

    /**
     * 凭证内容json
     */
    private String voucher;

    /**
     * 凭证接收时间
     */
    private Date receiveTime;

    /**
     * 备注
     */
    private String remark;

    private Integer version;

    private static final long serialVersionUID = 1L;
}