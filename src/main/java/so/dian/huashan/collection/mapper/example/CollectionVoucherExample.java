package so.dian.huashan.collection.mapper.example;

import lombok.Builder;
import lombok.Getter;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.huashan.common.enums.CollectionVoucherStatus;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/14 11:52
 * @description:
 */
@Getter
public class CollectionVoucherExample {

    private final List<Byte> statusList;

    private final Integer limit;

    private final PageRequest page;

    private final Integer leRetryTime;

    private final Long gmtCreate;

    private final Long gtId;

    @Builder
    public CollectionVoucherExample(PageRequest page,List<CollectionVoucherStatus> statusList,
                                    Integer limit,
                                    Integer leRetryTime,Long gmtCreate, Long gtId) {
        this.page = page;
        this.statusList = statusList.stream().map(o->Optional.ofNullable(o).map(CollectionVoucherStatus::getCode).orElse(null)).collect(Collectors.toList());
        this.limit = limit;
        this.leRetryTime = leRetryTime;
        this.gmtCreate = gmtCreate;
        this.gtId = gtId;
    }
}
