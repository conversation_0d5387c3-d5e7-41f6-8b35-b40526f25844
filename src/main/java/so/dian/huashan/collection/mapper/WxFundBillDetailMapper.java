package so.dian.huashan.collection.mapper;

import so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO;

import java.util.List;

public interface WxFundBillDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxFundBillDetailDO record);

    int insertBatch(List<WxFundBillDetailDO> wxFundBillDetailDOList);

    int insertSelective(WxFundBillDetailDO record);

    WxFundBillDetailDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WxFundBillDetailDO record);

    int updateByPrimaryKey(WxFundBillDetailDO record);
}