package so.dian.huashan.collection.mapper.dos;

import java.util.Date;
import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * pay_bill
 * <AUTHOR>
@Data
public class PayBillDO extends BaseDO {

    /**
     * 凭证id
     */
    private Long voucherId;

    /**
     * 付款主体id
     */
    private Long paySubjectId;

    /**
     * 付款主体类型
     */
    private Integer paySubjectType;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 账单来源（0-中喜，1-工猫，2-招商cbs）
     */
    private Integer source;

    /**
     * 交易单号
     */
    private String tradeNo;

    /**
     * 第三方账单流水号
     */
    private String channelTradeNo;

    /**
     * 交易金额，单位：分
     */
    private Long tradeAmount;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 状态使用二进制表示，目前使用20位1048575，付款对账使用前四位，&15
     */
    private Integer status;
}