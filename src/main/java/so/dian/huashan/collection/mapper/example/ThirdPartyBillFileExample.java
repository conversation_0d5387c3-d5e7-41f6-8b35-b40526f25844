package so.dian.huashan.collection.mapper.example;

import lombok.Builder;
import lombok.Getter;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/20 17:05
 * @description:
 */
@Getter
public class ThirdPartyBillFileExample {

    private final String channelPayType;

    private final Integer billDate;

    @Builder
    public ThirdPartyBillFileExample(ThirdpartyPayType channelPayType, Integer billDate) {
        this.channelPayType = Optional.ofNullable(channelPayType).map(ThirdpartyPayType::name).orElse(null);
        this.billDate = billDate;
    }
}
