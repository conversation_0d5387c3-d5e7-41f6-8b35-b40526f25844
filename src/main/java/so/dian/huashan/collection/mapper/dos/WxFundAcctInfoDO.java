package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * wx_fund_acct_info
 * <AUTHOR>
@Data
public class WxFundAcctInfoDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * pt：20240809
     */
    private Integer pt;

    /**
     * 商户类型：0-小电 1-二级商户
     */
    private Integer mchType;

    /**
     * 微信商户ID
     */
    private String mchId;

    /**
     * 微信账户类型
     */
    private String wxAcctType;

    /**
     * 可用金额（分）
     */
    private Long availableAmt;

    /**
     * 不可用金额（分）
     */
    private Long pendingAmt;
}