package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO;
import so.dian.huashan.collection.mapper.example.TimeRangeExample;

import java.util.List;

public interface BillSettleOrderOriginalMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BillSettleOrderOriginalDO record);

    int insertSelective(BillSettleOrderOriginalDO record);

    BillSettleOrderOriginalDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillSettleOrderOriginalDO record);

    int updateByPrimaryKey(BillSettleOrderOriginalDO record);

    List<Long> selectIdByTimeRange(@Param("maxId") Long maxId, @Param("timeRange") TimeRangeExample timeRangeExample, @Param("pageSize") Integer pageSize);

    List<BillSettleOrderOriginalDO> selectByIds(@Param("ids") List<Long> ids);

    int batchUpdate(@Param("orderOriginalDOS") List<BillSettleOrderOriginalDO> orderOriginalDOS);

    Long selectMaxIdByGmtCreate(Long gmtCreate);

    int deleteByGmtCreateAndMaxId(@Param("gmtCreate") Long gmtCreate, @Param("maxId") Long maxId, @Param("size") Integer size);
}