package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO;
import so.dian.huashan.collection.mapper.example.TimeRangeExample;

import java.util.List;

public interface BillSettleControlOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BillSettleControlOrderDO record);

    int insertSelective(BillSettleControlOrderDO record);

    BillSettleControlOrderDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BillSettleControlOrderDO record);

    int updateByPrimaryKey(BillSettleControlOrderDO record);

    List<BillSettleControlOrderDO> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    List<Long> selectIdsForShard(@Param("maxId") Long maxId, @Param("timeRange") TimeRangeExample timeRangeExample, @Param("pageSize") Integer pageSize);

    List<BillSettleControlOrderDO> selectByIds(@Param("ids") List<Long> ids);
}