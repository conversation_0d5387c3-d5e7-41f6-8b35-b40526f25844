package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO;

import java.util.List;

public interface SettleDetailOriginalMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SettleDetailOriginalDO record);

    int insertSelective(SettleDetailOriginalDO record);

    SettleDetailOriginalDO selectByPrimaryKey(Long id);

    SettleDetailOriginalDO selectBySettleDetailNo(@Param("settleDetailNo") String settleDetailNo);

    int updateByPrimaryKeySelective(SettleDetailOriginalDO record);

    int updateByPrimaryKey(SettleDetailOriginalDO record);

    /**
     * 查询每日对账数据
     * @param startTime
     * @param endTime
     * @param maxId
     * @return
     */
    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("maxId") Long maxId);

    /**
     * 根据id批量查询数据
     * @param ids
     * @return
     */
    List<SettleDetailOriginalDO> selectByIds(@Param("ids") List<Long> ids);

    int batchUpdateStatusByIds(@Param("ids") List<Long> ids,@Param("status") Integer status);

    List<SettleDetailOriginalDO> selectBySettleDetailNos(@Param("settleDetailNos") List<String> settleDetailNos);

}