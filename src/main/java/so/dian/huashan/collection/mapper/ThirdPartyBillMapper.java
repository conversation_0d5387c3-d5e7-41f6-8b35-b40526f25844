package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;

import java.util.List;

public interface ThirdPartyBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ThirdPartyBillDO record);

    int insertSelective(ThirdPartyBillDO record);

    int insertBatch(List<ThirdPartyBillDO> list);

    ThirdPartyBillDO selectByPrimaryKey(Long id);

    List<ThirdPartyBillDO> selectByIds(@Param("ids") List<Long> ids);

    List<Long> selectByCheckDate(@Param("checkDate")Integer checkDate,@Param("startDateTime")Long startDateTime,@Param("endDateTime")Long endDateTime,@Param("ids")List<Long> ids);

    List<Long> selectIdForShard(@Param("checkDate")Integer checkDate, @Param("maxId") Long maxId);

    int updateByPrimaryKeySelective(ThirdPartyBillDO record);

    int updateByPrimaryKey(ThirdPartyBillDO record);
}