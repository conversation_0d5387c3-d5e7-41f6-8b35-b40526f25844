package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.HuazhuResultBillDO;

import java.util.List;

public interface HuazhuResultBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(HuazhuResultBillDO record);

    int insertSelective(HuazhuResultBillDO record);

    HuazhuResultBillDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HuazhuResultBillDO record);

    int updateByPrimaryKey(HuazhuResultBillDO record);

    int batchInsert(@Param("list") List<HuazhuResultBillDO> billDOS);
}