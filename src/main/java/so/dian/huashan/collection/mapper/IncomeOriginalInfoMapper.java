package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;

import java.util.List;

public interface IncomeOriginalInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(IncomeOriginalInfoDO record);

    int insertSelective(IncomeOriginalInfoDO record);

    IncomeOriginalInfoDO selectByPrimaryKey(Long id);

    IncomeOriginalInfoDO selectByIdempotentNo(String idempotentNo);

    List<IncomeOriginalInfoDO> selectBatchByPayNos(@Param("payNos") List<String> payNos);

    int updateByPrimaryKeySelective(IncomeOriginalInfoDO record);

    int updateByPrimaryKey(IncomeOriginalInfoDO record);
}