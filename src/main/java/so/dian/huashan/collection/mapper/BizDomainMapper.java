package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BizDomainMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BizDomainDO record);

    int insertSelective(BizDomainDO record);

    BizDomainDO selectByPrimaryKey(Long id);

    List<BizDomainDO> selectByIds(@Param("ids") List<Long> ids);

    BizDomainDO selectByCode(String code);

    int updateByPrimaryKeySelective(BizDomainDO record);

    int updateByPrimaryKey(BizDomainDO record);

    List<BizDomainDO> selectAll();
}