package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * bill_settle_control_order
 * <AUTHOR>
@Data
public class BillSettleControlOrderDO extends BaseDO {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 结算对象类型
     */
    private Integer mainBizType;

    /**
     * 结算对象ID
     */
    private Long mainBizId;

    /**
     * 出资方类型
     */
    private Integer settleSubjectType;

    /**
     * 出资方ID
     */
    private Long settleSubjectId;

    private Byte controlStatus;
}