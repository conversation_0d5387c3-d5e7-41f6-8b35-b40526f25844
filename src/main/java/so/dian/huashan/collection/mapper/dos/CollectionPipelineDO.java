package so.dian.huashan.collection.mapper.dos;

import lombok.Data;

import java.io.Serializable;

/**
 * collection_pipeline
 * <AUTHOR>
@Data
public class CollectionPipelineDO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务域ID
     */
    private Long bizDomainId;

    /**
     * 管道策略编码
     */
    private String strategyCode;

    /**
     * 收集方式，1-实时，2-定时
     */
    private Byte collectionWay;

    /**
     * 表名，建议用库名.表名
     */
    private String tableName;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    /**
     * 删除标识，1-已删除，0-未删除
     */
    private Integer deleted;

    private static final long serialVersionUID = 1L;
}