package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

import java.util.Date;

/**
 * wx_split_bill_detail
 * <AUTHOR>
@Data
public class WxSplitBillDetailDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 对账状态：0-初始化 1-已处理
     */
    private Integer checkStatus;

    /**
     * 结算明细单号
     */
    private String settleDetailNo;

    /**
     * 分账时间
     */
    private Date splitDate;

    /**
     * 分账发起方
     */
    private String splitInitiator;

    /**
     * 分账方
     */
    private String splitSourceId;

    /**
     * 分账接受方
     */
    private String splitReceiveId;

    /**
     * 渠道分账/回退单号
     */
    private String splitOrderNo;

    /**
     * 分账明细单号
     */
    private String splitDetailNo;

    /**
     * 商户分账/回退单号
     */
    private String mchSplitOrderNo;

    /**
     * 订单金额（分）
     */
    private Long orderAmt;

    /**
     * 分账金额（分）
     */
    private Long splitAmt;

    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 删除状态：0-未删除 1-已删除
     */
    private Integer deleted;
    /**
     *  创建时间
     */
    private Long gmtCreate;
    /**
     *  更新时间
     */
    private Long gmtUpdate;
}