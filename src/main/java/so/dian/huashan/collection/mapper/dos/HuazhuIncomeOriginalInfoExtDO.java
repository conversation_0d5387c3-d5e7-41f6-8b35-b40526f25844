package so.dian.huashan.collection.mapper.dos;

import lombok.Data;

import java.util.Date;

@Data
public class HuazhuIncomeOriginalInfoExtDO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 收入账表ID
     */
    private Long incomeOriginalId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    private Integer payType;

    /**
     * 渠道支付单号
     */
    private String payNo;

    /**
     * 交易金额，单位：分
     */
    private Integer tradeAmount;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 小电支付系统流水号，分别对应payment#trade_no和refund_order#refund_order_no
     */
    private String dianPayNo;
}