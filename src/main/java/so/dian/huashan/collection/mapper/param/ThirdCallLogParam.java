package so.dian.huashan.collection.mapper.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ThirdCallLogParam {
    /**
     * 调用日期
     */
    private Integer callDate;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 请求参数
     */
    private String requestJson;

    /**
     * http状态码
     */
    private Integer httpStatusCode;

    /**
     * 第三方返回码
     */
    private String thirdErrorCode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 调用时间
     */
    private Long thirdCallTime;
}
