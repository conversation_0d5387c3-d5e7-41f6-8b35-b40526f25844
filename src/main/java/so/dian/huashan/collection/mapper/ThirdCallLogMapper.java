package so.dian.huashan.collection.mapper;

import so.dian.huashan.collection.mapper.dos.ThirdCallLogDO;
import so.dian.huashan.collection.mapper.param.ThirdCallLogParam;

import java.util.List;

public interface ThirdCallLogMapper {

    /**
     *  新增
     */
    int insertSelective(ThirdCallLogDO thirdCallLogDO);

    /**
     * 批量查询
     */
    List<ThirdCallLogDO> selectByParam(ThirdCallLogParam thirdCallLogParam);
}
