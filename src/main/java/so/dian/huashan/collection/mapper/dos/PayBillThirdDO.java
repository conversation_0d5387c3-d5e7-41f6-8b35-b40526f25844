package so.dian.huashan.collection.mapper.dos;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * pay_bill_third
 * <AUTHOR>
@Data
public class PayBillThirdDO extends BaseDO {

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 交易单号
     */
    private String tradeNo;
    /**
     * 三方交易单号
     */
    private String channelTradeNo;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * 交易金额，单位：分
     */
    private Long tradeAmount;

    /**
     * 账单来源（0-中喜，1-工猫，2-招商cbs）
     */
    private Integer source;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（0-成功，1-退票，2-支付中，3-失败）
     */
    private Integer thirdStatus;

    /**
     * 状态使用二进制表示，目前使用20位1048575，付款对账使用前四位，&15
     */
    private Integer status;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 付款主体id
     */
    private Long paySubjectId;

    /**
     * 付款主体类型
     */
    private Integer paySubjectType;
}