package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;
@Data
public class ThirdCallLogDO extends BaseDO {
    /**
     * 调用日期
     */
    private Integer callDate;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 请求参数
     */
    private String requestJson;

    /**
     * http状态码
     */
    private Integer httpStatusCode;

    /**
     * 第三方返回码
     */
    private String thirdErrorCode;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 调用时间
     */
    private Long thirdCallTime;

}
