package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.huashan.task.mapper.entity.BaseDO;

/**
 * third_party_bill_file
 */
@Data
public class ThirdPartyBillFileDO extends BaseDO {

    /**
     * 执行状态（0-初始化，1-进行中，2-成功，3-失败）
     */
    private Byte status;

    /**
     * 账单日
     */
    private Integer billDate;

    /**
     * 采集注册id
     */
    private Long collectionRegistryId;

    /**
     * 渠道支付类型
     */
    private String channelPayType;

    /**
     * 文件路径
     */
    private String filePath;

    private static final long serialVersionUID = 1L;
}