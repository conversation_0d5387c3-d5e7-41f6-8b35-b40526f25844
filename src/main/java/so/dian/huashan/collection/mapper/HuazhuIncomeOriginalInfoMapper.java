package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO;
import so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoExtDO;

import java.util.Date;
import java.util.List;

public interface HuazhuIncomeOriginalInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(HuazhuIncomeOriginalInfoDO record);

    int insertSelective(HuazhuIncomeOriginalInfoDO record);

    HuazhuIncomeOriginalInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HuazhuIncomeOriginalInfoDO record);

    int updateByPrimaryKey(HuazhuIncomeOriginalInfoDO record);

    HuazhuIncomeOriginalInfoDO selectByIncomeOriginalId(@Param("incomeOriginalId") Long incomeOriginalId);

    List<Long> selectForBurstJob(@Param("tradeTime") Date tradeTime);

    List<Long> selectIdForShard(@Param("tradeTime") Date tradeTime, @Param("maxId") Long maxId);

    List<HuazhuIncomeOriginalInfoExtDO> selectOriginalByIds(@Param("ids") List<Long> ids);

    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Byte status);
}