package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.PayBillDO;
import so.dian.huashan.collection.mapper.param.PayBillParam;

import java.util.List;

public interface PayBillMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PayBillDO record);

    int insertSelective(PayBillDO record);

    PayBillDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayBillDO record);

    int updateByPrimaryKey(PayBillDO record);

    /**
     * 分片专用
     * @param startTime
     * @param endTime
     * @param maxId
     * @return
     */
    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime, @Param("maxId") Long maxId);

    List<PayBillDO> selectByIds(@Param("ids") List<Long> ids);

    PayBillDO getOne(PayBillParam payBillParam);

    int batchUpdateStatusByIds(@Param("ids") List<Long> ids,@Param("status") Integer status);

    List<PayBillDO> selectByTradeNos(@Param("tradeNos") List<String> tradeNos);

}