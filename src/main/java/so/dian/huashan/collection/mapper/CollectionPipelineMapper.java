package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.CollectionPipelineDO;

import java.util.List;

public interface CollectionPipelineMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CollectionPipelineDO record);

    int insertSelective(CollectionPipelineDO record);

    CollectionPipelineDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CollectionPipelineDO record);

    int updateByPrimaryKey(CollectionPipelineDO record);

    List<CollectionPipelineDO> selectAllRealTime();

    List<CollectionPipelineDO> selectByTableName(@Param("tableName") String tableName);

    List<CollectionPipelineDO> selectByDomainId(@Param("domainId") Long domainId);
}