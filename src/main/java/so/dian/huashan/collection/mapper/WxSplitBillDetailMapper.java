package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.collection.mapper.param.SplitBillParam;

import java.util.List;

import java.util.List;

public interface WxSplitBillDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxSplitBillDetailDO record);

    /**
     * 批量添加
     */
    int insertBatch(List<WxSplitBillDetailDO> list);

    int insertSelective(WxSplitBillDetailDO record);

    WxSplitBillDetailDO selectByPrimaryKey(Long id);

    List<WxSplitBillDetailDO> selectByParam(SplitBillParam splitBillParam);

    int updateByPrimaryKeySelective(WxSplitBillDetailDO record);

    int updateByPrimaryKey(WxSplitBillDetailDO record);

    List<WxSplitBillDetailDO> selectBySettleDetailNo(@Param("settleDetailNos") List<String> settleDetailNos);

    int batchUpdateStatusByIds(@Param("ids") List<Long> ids,@Param("status") Integer status);

    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime, @Param("maxId") Long maxId);

    List<WxSplitBillDetailDO> selectByIds(@Param("ids") List<Long> ids);

}