package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.collection.mapper.param.PayBillParam;
import so.dian.huashan.collection.mapper.param.PayBillThirdParam;

import java.util.List;

public interface PayBillThirdMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PayBillThirdDO record);

    int insertSelective(PayBillThirdDO record);

    PayBillThirdDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayBillThirdDO record);

    int updateByPrimaryKey(PayBillThirdDO record);

    PayBillThirdDO getOne(PayBillParam payBillParam);

    /**
     * 批量查询
     */
    List<PayBillThirdDO> selectListByParam(PayBillThirdParam payBillThirdParam);
    int insertBatch(List<PayBillThirdDO> list);
    int updateBatch(List<PayBillThirdDO> list);
    List<PayBillThirdDO> selectByTradeNos(@Param("channelTradeNos") List<String> channelTradeNos);
    /**
     * 分片专用
     * @param startTime
     * @param endTime
     * @param maxId
     * @return
     */
    List<Long> selectByCheckDateTime(@Param("startTime") Long startTime,@Param("endTime") Long endTime, @Param("maxId") Long maxId);

    int batchUpdateStatusByIds(@Param("ids") List<Long> ids,@Param("status") Integer status);

    List<PayBillThirdDO> selectByIds(@Param("ids") List<Long> ids);

}