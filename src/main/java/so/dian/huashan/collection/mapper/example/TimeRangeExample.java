package so.dian.huashan.collection.mapper.example;

import lombok.Getter;

import java.util.Date;
import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/05/25 13:42
 * @description:
 */
@Getter
public class TimeRangeExample {

    private final Long start;

    private final Long end;

    public TimeRangeExample(Date start, Date end) {
        this.start = Objects.requireNonNull(start, "start must not null").getTime();
        this.end = Objects.requireNonNull(end, "end must not null").getTime();
    }

    public TimeRangeExample(Long start, Long end) {
        this.start = Objects.requireNonNull(start, "start must not null");
        this.end = Objects.requireNonNull(end, "end must not null");
    }

    public Date getStartDate() {
        return new Date(start);
    }

    public Date getEndDate() {
        return new Date(end);
    }
}
