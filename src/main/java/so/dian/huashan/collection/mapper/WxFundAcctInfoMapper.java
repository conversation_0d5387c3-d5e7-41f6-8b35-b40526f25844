package so.dian.huashan.collection.mapper;

import so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO;
import so.dian.huashan.collection.mapper.param.WxFundAcctInfoParam;

import java.util.List;

public interface WxFundAcctInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WxFundAcctInfoDO record);

    int insertSelective(WxFundAcctInfoDO record);

    int insertBatch(List<WxFundAcctInfoDO> wxFundAcctInfoDOList);

    int updateBatch(List<WxFundAcctInfoDO> wxFundAcctInfoDOList);

    WxFundAcctInfoDO selectByPrimaryKey(Long id);

    List<WxFundAcctInfoDO> selectByParam(WxFundAcctInfoParam wxFundAcctInfoParam);

    int updateByPrimaryKeySelective(WxFundAcctInfoDO record);

    int updateByPrimaryKey(WxFundAcctInfoDO record);
}