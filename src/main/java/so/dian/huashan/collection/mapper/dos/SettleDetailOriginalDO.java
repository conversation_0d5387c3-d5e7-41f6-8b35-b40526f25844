package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * settle_detail_original
 * <AUTHOR>
@Data
public class SettleDetailOriginalDO extends BaseDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 对账状态：0-初始化 1-已处理
     */
    private Integer checkStatus;

    /**
     * 结算明细单号
     */
    private String settleDetailNo;

    /**
     * 第三方结算单号
     */
    private String tpSettleNo;

    /**
     * 出资方第三方id
     */
    private String tpSourceId;

    /**
     * 入资方第三方id
     */
    private String tpReceiveId;

    /**
     * 订单金额（分）
     */
    private Long amountOutput;

    /**
     * 是否删除
     */
    private Integer deleted;

    private Long gmtCreate;

    private Long gmtUpdate;
}