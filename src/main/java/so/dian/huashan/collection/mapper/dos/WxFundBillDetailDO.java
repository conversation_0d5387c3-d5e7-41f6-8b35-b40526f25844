package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

/**
 * wx_fund_bill_detail
 * <AUTHOR>
@Data
public class WxFundBillDetailDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 商户类型：0-小电 1-二级商户
     */
    private Integer mchType;

    /**
     * 微信商户ID
     */
    private String mchId;

    /**
     * 记账日期，yyyyMMdd
     */
    private Integer billDate;

    /**
     * 支付业务单号
     */
    private String payBizNo;

    /**
     * 资金流水号
     */
    private String fundNo;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 收支类型
     */
    private String incomeType;

    /**
     * 收支金额（分）
     */
    private Long incomeAmt;

    /**
     * 账户结余（分）
     */
    private Long acctBalance;

    /**
     * 资金变更提交申请人
     */
    private String changeInitiator;

    /**
     * 业务凭证号
     */
    private String bizVoucherNo;

    /**
     * 备注
     */
    private String remark;
}