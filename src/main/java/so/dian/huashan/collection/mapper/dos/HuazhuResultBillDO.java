package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

import java.util.Date;

/**
 * huazhu_result_bill
 * <AUTHOR>
@Data
public class HuazhuResultBillDO extends BaseDO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 收入账表ID
     */
    private Long incomeOriginalId;

    /**
     * 账单日期
     */
    private Integer billDate;

    /**
     * 华住交易单号
     */
    private String tradeNo;

    /**
     * 小电交易单号
     */
    private String dianTradeNo;

    /**
     * 业务单号
     */
    private String bizOrderNo;

    /**
     * 支付流水号
     */
    private String payNo;

    /**
     * 支付类型
     */
    private Integer payType;

    /**
     * 交易金额
     */
    private Long amount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 交易时间
     */
    private Date tradeTime;

    /**
     * SPU编码
     */
    private String spuCode;

    /**
     * SPU名称
     */
    private String spuName;

    /**
     * 产品类型ID
     */
    private Integer productCategory;

    /**
     * 产品类型名称
     */
    private String productCategoryDesc;

    /**
     * 订单名称
     */
    private Long orderAmount;

    /**
     * 设备编码
     */
    private String deviceNo;

    /**
     * 交易类型，in-收入；out-支出
     */
    private String tradeType;

    /**
     * 订单停止计费日期
     */
    private Date orderStopChargTime;

    /**
     * 借出门店所属公司名称
     */
    private String loanShopCompany;

    /**
     * 渠道，即借出门店所属公司类型
     */
    private String loanShopChannel;

    /**
     * 是否封顶价，0-否；1-是
     */
    private Byte isCappedPrice;

    /**
     * 封顶价金额，达到封顶价有值，否则没有值
     */
    private String cappedPrice;

    private static final long serialVersionUID = 1L;
}