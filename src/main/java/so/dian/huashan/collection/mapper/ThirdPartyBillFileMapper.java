package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO;
import so.dian.huashan.collection.mapper.example.ThirdPartyBillFileExample;

import java.util.List;

public interface ThirdPartyBillFileMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ThirdPartyBillFileDO record);

    int insertSelective(ThirdPartyBillFileDO record);

    ThirdPartyBillFileDO selectByPrimaryKey(Long id);

    List<ThirdPartyBillFileDO> selectList(@Param("list") List<Long> idList);

    int updateByPrimaryKeySelective(ThirdPartyBillFileDO record);

    int updateByPrimaryKey(ThirdPartyBillFileDO record);

    List<ThirdPartyBillFileDO> selectByExample(ThirdPartyBillFileExample example);
}