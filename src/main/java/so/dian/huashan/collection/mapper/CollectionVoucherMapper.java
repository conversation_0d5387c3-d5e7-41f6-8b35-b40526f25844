package so.dian.huashan.collection.mapper;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.collection.mapper.dos.CollectionVoucherDO;
import so.dian.huashan.collection.mapper.example.CollectionVoucherExample;

import java.util.List;

public interface CollectionVoucherMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CollectionVoucherDO record);

    int insertSelective(CollectionVoucherDO record);

    CollectionVoucherDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CollectionVoucherDO record);

    List<CollectionVoucherDO> selectByExample(CollectionVoucherExample example);

    List<CollectionVoucherDO> selectForRetry(CollectionVoucherExample example);

    int retryUpdateByIdList(@Param("ids") List<Long> ids);

    int batchInsert(List<CollectionVoucherDO> voucherDOS);

    int updateStatus(@Param("id") Long id, @Param("status") Byte status, @Param("version") Integer version);
}