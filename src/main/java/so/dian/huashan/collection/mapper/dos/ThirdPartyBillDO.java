package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

import java.util.Date;

/**
 * third_party_bill
 * <AUTHOR>
@Data
public class ThirdPartyBillDO extends BaseDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 核对日
     */
    private Integer checkDate;

    /**
     * 交易类型（in-收入，out-支出）
     */
    private String tradeType;

    /**
     * 账单时间
     */
    private Date billTime;

    /**
     * 交易单号
     */
    private String payNo;

    /**
     * 交易金额，单位：分
     */
    private Integer tradeAmount;

    /**
     * 业务类型
     */
    private Integer bizType;

    /**
     * 账单来源（0-支付宝，1-微信，2-宝付，3-微美）
     */
    private Integer billSource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（0-有效，1-失效）
     */
    private Integer status;

    /**
     * 业务编号1，如果是支付宝账单，则对应的是商户订单号；如果是微信账单，则对应的是业务凭证号。
     */
    private String bizNo1;

    /**
     * 业务编号1，如果是支付宝账单，则对应的是账务流水号；如果是微信账单，则对应的是微信支付业务单号。
     */
    private String bizNo2;

    /**
     * 商户号
     */
    private String mchId;

    private static final long serialVersionUID = 1L;
}