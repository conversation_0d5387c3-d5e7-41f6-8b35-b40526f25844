package so.dian.huashan.collection.mapper.dos;

import lombok.Data;
import so.dian.himalaya.common.entity.BaseDO;

import java.util.Date;

/**
 * bill_settle_order_original
 * <AUTHOR>
@Data
public class BillSettleOrderOriginalDO extends BaseDO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 状态，0-初始化，1-一致，2-差异，3-差异一致，4-不需要对账
     */
    private Byte status;

    /**
     * 凭证ID
     */
    private Long voucherId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单金额
     */
    private Long orderAmount;

    /**
     * 交易金额，如果是退款状态则表示退款金额；付款状态表示付款金额
     */
    private Long payAmount;

    /**
     * 交易时间
     */
    private Date payTime;

    /**
     * 借出门店ID
     */
    private Long loanShopId;

    /**
     * 归还盒子编码
     */
    private String returnBoxNo;

    /**
     * json格式，租借价格信息
     */
    private String loanPriceInfo;

    private Integer version;

    /**
     * 支付成功时间
     */
    private Date paySuccessTime;

    /**
     * 业务类型：1-租借订单 2-次卡订单
     */
    private Integer bizType;

}