package so.dian.huashan.collection.manager;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.mapper.ThirdCallLogMapper;
import so.dian.huashan.collection.mapper.dos.ThirdCallLogDO;
import so.dian.huashan.collection.mapper.param.ThirdCallLogParam;

import java.util.List;
import java.util.Objects;
@Slf4j
@Component
public class ThirdCallLogManager {
    @Autowired
    private ThirdCallLogMapper thirdCallLogMapper;
    public void insert(ThirdCallLogDO thirdCallLogDO){
        log.info("插入第三方调用记录,thirdCallLogDO:{}",thirdCallLogDO);
        if (Objects.isNull(thirdCallLogDO)){
            return;
        }
        try {
            thirdCallLogMapper.insertSelective(thirdCallLogDO);
        } catch (Exception e){
            log.error("插入第三方调用记录失败,thirdCallLogDO:{}",thirdCallLogDO);
        }
    }

    /**
     * 批量查询
     * @param thirdCallLogParam
     * @return
     */
    public List<ThirdCallLogDO> selectByParam(ThirdCallLogParam thirdCallLogParam){
        if (Objects.isNull(thirdCallLogParam)){
            return null;
        }
        return thirdCallLogMapper.selectByParam(thirdCallLogParam);
    }

}
