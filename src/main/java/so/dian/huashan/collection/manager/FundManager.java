package so.dian.huashan.collection.manager;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.collection.mapper.WxFundAcctInfoMapper;
import so.dian.huashan.collection.mapper.WxFundBillDetailMapper;
import so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO;
import so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class FundManager {
    @Resource
    private WxFundBillDetailMapper wxFundBillDetailMapper;
    @Resource
    private WxFundAcctInfoMapper wxFundAcctInfoMapper;
    @Transactional
    public  void saveAndUpdateFundBill( List<WxFundAcctInfoDO> infoInsertList,List<WxFundAcctInfoDO> infoUpdateList){
        if (!CollectionUtils.isEmpty(infoInsertList)){
            wxFundAcctInfoMapper.insertBatch(infoInsertList);
        }
        if (!CollectionUtils.isEmpty(infoUpdateList)){
            wxFundAcctInfoMapper.updateBatch(infoUpdateList);
        }

    }

    @Transactional
    public  void xiaodianSaveAndUpdateFundBill(WxFundAcctInfoDO infoInsert,WxFundAcctInfoDO infoUpdate){
        if (Objects.nonNull(infoInsert)){
            wxFundAcctInfoMapper.insert(infoInsert);
        }
        if (Objects.nonNull(infoUpdate)){
            wxFundAcctInfoMapper.updateByPrimaryKeySelective(infoUpdate);
        }

    }
}
