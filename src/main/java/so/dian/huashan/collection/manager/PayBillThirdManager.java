package so.dian.huashan.collection.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.collection.mapper.PayBillThirdMapper;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;

import javax.annotation.Resource;
import java.util.List;
@Slf4j
@Component
public class PayBillThirdManager {
    @Resource
    private PayBillThirdMapper payBillThirdMapper;

    @Transactional
    public void batchSaveAndUpdate(List<PayBillThirdDO> insertList, List<PayBillThirdDO> updateList){
        if (CollectionUtils.isNotEmpty(insertList)){
            payBillThirdMapper.insertBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            payBillThirdMapper.updateBatch(updateList);
        }
    }
}
