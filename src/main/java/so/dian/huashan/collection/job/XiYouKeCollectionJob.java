package so.dian.huashan.collection.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.service.thirdparty.xiyouke.dto.XykBodyDTO;
import so.dian.huashan.collection.service.thirdparty.xiyouke.dto.XykHeaderDTO;
import so.dian.huashan.common.mapper.yandang.ChannelMapper;
import so.dian.huashan.common.mapper.yandang.dos.ChannelDO;
import so.dian.huashan.common.mapper.yandang.example.ChannelExample;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;


@Slf4j
@Component
public class XiYouKeCollectionJob {

    private static final String out_url = "/openApi/v1.0/payment/changeApply";

    private static final String callback_url = "/xiyouke/callback/downloadUrl";

    @Value("${huashan.xiyouke.channel.domain:https://api-dev.xiyk.cn}")
    private String host;
    @Resource
    private ChannelMapper channelMapper;
    @Value("${huashan.xiyouke.notify.domain}")
    private String notifyDomain;
    private static final String ORDER_NO_PREFIX = "XYK_DZ";

    private static CloseableHttpClient httpClient = HttpClients.createDefault();

    @XxlJob("XiYouKeCollectionJob")
    public ReturnT<String> execute(String param) {
        log.info("XiYouKeCollectionJob start host:{},notifyDomain:{}", host, notifyDomain);
        ChannelExample channelExample = new ChannelExample();
        channelExample.setPayChannel(14);
        List<ChannelDO> channelDOList = channelMapper.selectByExample(channelExample);
        Map<String,Long> channelMap = new HashMap<>();
        for (ChannelDO channelDO:channelDOList){
            Long channelId = channelMap.get(channelDO.getAccountNo());
            if (Objects.nonNull(channelId)){
                log.info("重复渠道channelId:{}",channelDO.getId());
                continue;
            }
            applyBill(channelDO,null,null);
            channelMap.put(channelDO.getAccountNo(),channelDO.getId());
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 申请账单
     */

    public void applyBill(ChannelDO channelDO,String startTime,String endTime) {
        if (Objects.isNull(channelDO)){
            return;
        }
        // 默认取上一天的数据
        if (StringUtils.isBlank(startTime)){
            Date beginDate = LocalDateUtils.offsetDay(LocalDateUtils.beginOfDay(DateTime.now()), -1);
            startTime = DateUtil.formatDateTime(beginDate);
        }
        // 默认取上一天的数据
        if (StringUtils.isBlank(endTime)){
            Date endBeginDate = LocalDateUtils.offsetDay(LocalDateUtils.endOfDay(DateTime.now()), -1);
            endTime = DateUtil.formatDateTime(endBeginDate);
        }
        Map<String, String> extraMap = JSON.parseObject(channelDO.getExtData(), HashMap.class);
        String partnerOrderId = ORDER_NO_PREFIX+"_" + System.currentTimeMillis() + RandomUtil.randomLong(1, 5)+"_"+channelDO.getId();
        XykHeaderDTO header = XykHeaderDTO.create(extraMap, partnerOrderId);
        XykBodyDTO xykBodyDTO = new XykBodyDTO();
        // 小电余额共享，所以账户类型为银行类型，会返回所有账单流水
        xykBodyDTO.setAccountType(1);
        xykBodyDTO.setPartnerOrderId(partnerOrderId);
        xykBodyDTO.setStartTime(startTime);
        xykBodyDTO.setEndTime(endTime);
        xykBodyDTO.setNotifyUrl(notifyDomain + callback_url);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("header", header);
        jsonObject.put("body", xykBodyDTO);
        log.info("xiyouke send pay header:{},xykBodyDTO:{}", header, xykBodyDTO);
        try {
            HttpPost httpPost = new HttpPost(host + out_url);
            StringEntity stringEntity = new StringEntity(jsonObject.toJSONString(), StandardCharsets.UTF_8.displayName());
            stringEntity.setContentEncoding(StandardCharsets.UTF_8.displayName());
            stringEntity.setContentType("application/json");
            httpPost.setHeader("Connection", "close");
            httpPost.setEntity(stringEntity);
            log.info("xiyouke send pay httpPost:{}", header, httpPost);
            CloseableHttpResponse response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            String respContent = EntityUtils.toString(entity);
            log.info("xiyouke send pay result:{}", respContent);
            EntityUtils.consume(entity);
            if (!Objects.equals(response.getStatusLine().getStatusCode(),
                    HttpStatus.SC_OK)) {
                log.error("远程调用失败 response:{}",response);
            }
        } catch (Exception e) {
            log.error("xiyouke request build channelDO:{}", channelDO, e);
        }
    }
}
