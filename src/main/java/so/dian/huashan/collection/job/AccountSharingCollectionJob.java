package so.dian.huashan.collection.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.infrastructure.framework.HSTaskGroup;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.val.IncomeCheckParam;

import java.util.Objects;

@Slf4j
@Component
public class AccountSharingCollectionJob {
    @Autowired
    private ThirdpartyPullService thirdpartyPullService;

    @XxlJob("AccountSharingCollectionJob")
    @HSTaskGroup(code = "AccountSharingCollectionJob")
    public ReturnT<String> startIncome(String param) {
        return ReturnT.SUCCESS;
    }

    @HSTaskScheduled(taskActuator = "accountSharingCollectionTaskJob")
    public void execute(TaskBaseParam param) {
        log.info("AccountSharingCollectionJob get param:{}", param);
        IncomeCheckParam checkParam = IncomeCheckParam.from(param);
        if (Objects.isNull(checkParam)){
            log.info("分账失败param:{}",param);
            return;
        }
        checkParam.setPayType(ThirdpartyPayType.CHANNEL_SHARE_ACCOUNT.name());
        thirdpartyPullService.execute(checkParam);
    }
}
