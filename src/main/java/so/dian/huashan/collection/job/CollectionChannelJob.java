package so.dian.huashan.collection.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huangshan.core.executor.val.TaskBaseParam;
import so.dian.huangshan.infrastructure.framework.HSTaskGroup;
import so.dian.huangshan.infrastructure.framework.HSTaskScheduled;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.common.val.IncomeCheckParam;

import javax.annotation.Resource;

/**
 * ChannelAccountingJob
 *
 * <AUTHOR>
 * @desc 渠道对账任务
 * @date 2022/8/26 10:09
 */
@Slf4j
@Component
public class CollectionChannelJob {

    @XxlJob("incomeCheck")
    @HSTaskGroup(code = "incomeCheck")
    public ReturnT<String> startIncome(String param){
        return ReturnT.SUCCESS;
    }

    @Resource
    private ThirdpartyPullService thirdpartyPullService;

//    @XxlJob("CollectionChannelJob")
    @HSTaskScheduled(taskActuator = "CollectionChannelJob")
    public void execute(TaskBaseParam param) {
        IncomeCheckParam checkParam = IncomeCheckParam.from(param);
        thirdpartyPullService.execute(checkParam);
    }

}
