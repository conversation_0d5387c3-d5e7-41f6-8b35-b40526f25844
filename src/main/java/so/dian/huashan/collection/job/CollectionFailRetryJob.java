package so.dian.huashan.collection.job;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.job.entity.CollectionFailParam;
import so.dian.huashan.collection.job.service.CollectionFailRetryService;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * ChannelAccountingJob 渠道对账任务
 *
 * <AUTHOR>
 * @desc
 * @date 2022/8/26 10:09
 */
@Slf4j
@Component
public class CollectionFailRetryJob {

    @Resource
    private CollectionFailRetryService autoIncrementCompensationService;
    @XxlJob("CollectionFailRetryJob")
    public ReturnT<String> execute(String param) {

        CollectionFailParam retryJobParam = JSON.parseObject(param, CollectionFailParam.class);
        if (Objects.isNull(retryJobParam)) {
            retryJobParam = new CollectionFailParam();
        }

        autoIncrementCompensationService.compensation(retryJobParam);
        return ReturnT.SUCCESS;
    }

}
