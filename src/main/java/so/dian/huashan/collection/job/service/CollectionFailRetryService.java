package so.dian.huashan.collection.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.job.entity.CollectionFailParam;
import so.dian.huashan.collection.service.CollectionPipelineService;
import so.dian.huashan.collection.service.CollectionVoucherService;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.pipeline.RetryExecutorCommand;

import java.util.List;
import java.util.Objects;

import static so.dian.huashan.common.exception.LogMarkerFactory.COLLECTION_RETRY_MARKER;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/03/14 11:47
 * @description: 自动增量采集补偿
 */
@Slf4j
@Service
public class CollectionFailRetryService {

    @Value("${collection.voucher.retry.time:3}")
    private Integer retryTime;

    @Value("${collection.voucher.retry.count:100}")
    private Integer queryCount;

    @Value("${collection.voucher.retry.max-loop:10000000}")
    private Integer maxLoop;

    @Autowired
    private CollectionPipelineService pipelineService;

    @Autowired
    private CollectionVoucherService voucherService;

    @Autowired
    private CollectionFailRetryV1Service retryV1Service;

    @Autowired
    private ApplicationContext applicationContext;

    public void compensation(CollectionFailParam retryJobParam) {

        Long minId = 0L;
        List<CollectionVoucherBO> voucherBOS = voucherService.listRetryableVoucher(minId, queryCount, retryJobParam.getStatuses());

        int loopCount = 0;
        while (CollUtil.isNotEmpty(voucherBOS)) {
            if (++ loopCount == maxLoop) {
                log.warn(COLLECTION_RETRY_MARKER, "重试循环次数过高，存在死循环风险");
            }

            minId = CollUtil.getLast(voucherBOS).getId();
            for (CollectionVoucherBO voucherBO : voucherBOS) {
                log.info("收集重试场景，开始重试凭证，凭证ID：{}", voucherBO.getId());
                try {
                    if (StrUtil.isBlank(voucherBO.getVoucher().getTable())) {
                        log.info(COLLECTION_RETRY_MARKER, "即将重试的凭证没有binlog表,凭证ID:{}", voucherBO.getId());
                        retryV1Service.process(voucherBO);
                        continue;
                    }

                    CollectionPipelineBO collectionPipelineBO = pipelineService.find(voucherBO.getVoucher().getTable());
                    if (Objects.isNull(collectionPipelineBO)) {
                        log.warn(COLLECTION_RETRY_MARKER, "采集重试查询不到对应的管道注册信息,凭证ID:{}", voucherBO.getId());
                        continue;
                    }

                    CollectionPipeline collectionPipeline = applicationContext.getBean(collectionPipelineBO.getStrategyCode(), CollectionPipeline.class);
                    RetryExecutorCommand command = new RetryExecutorCommand(collectionPipeline);
                    command.execute(voucherBO);

                    if (voucherBO.getRetryTime() > retryTime) {
                        log.warn(COLLECTION_RETRY_MARKER, "重试的凭证已经达到上限，当前凭证重试次数:{}, 上限:{}", voucherBO.getRetryTime(), retryTime);
                    }
                }catch (Exception e){
                    log.error("采集重试处理失败,凭证ID:{}", voucherBO.getId(), e);
                }
            }
            voucherBOS = voucherService.listRetryableVoucher(minId, queryCount, retryJobParam.getStatuses());
        }

        log.info(COLLECTION_RETRY_MARKER, "采集重试完成");
    }

}
