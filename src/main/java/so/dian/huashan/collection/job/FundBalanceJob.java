package so.dian.huashan.collection.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.genesis.api.payment.dto.PaymentBaseInfo;
import so.dian.genesis.api.payment.enums.PaymentChannelTypeEnum;
import so.dian.genesis.api.payment.req.PaymentAccountPageReq;
import so.dian.huashan.collection.enums.MchTypeEnum;
import so.dian.huashan.collection.manager.FundManager;
import so.dian.huashan.collection.mapper.WxFundAcctInfoMapper;
import so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO;
import so.dian.huashan.collection.mapper.param.WxFundAcctInfoParam;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.facade.IotPaymentFacade;
import so.dian.huashan.common.facade.WeixinFacade;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.model.dto.BalanceDTO;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FundBalanceJob {
    @Autowired
    private WeixinFacade weixinFacade;
    @Autowired
    private FundManager fundManager;
    @Resource
    private WxFundAcctInfoMapper wxFundAcctInfoMapper;
    @Resource
    private IotPaymentFacade iotPaymentFacade;

    @Value("${iot.xiaodian.mch.id:1}")
    private String mchId;
    @XxlJob("FundBalanceJob")
    public ReturnT<String> execute(String param) {
        Integer pt = DateUtil.parseDateYyyyMMdd2IntThrowException(new Date());
        for (MchTypeEnum mchTypeEnum:MchTypeEnum.values()){
            if (MchTypeEnum.XIAODIAN.getCode().equals(mchTypeEnum.getCode())) {
                BalanceDTO balanceDTO = weixinFacade.queryIsvBalance();
                if (Objects.isNull(balanceDTO)) {
                    log.info("微信小电余额查询失败pt:{}",pt);
                    continue;
                }
                WxFundAcctInfoDO wxFundAcctInfoDO = new WxFundAcctInfoDO();
                wxFundAcctInfoDO.setPt(pt);
                wxFundAcctInfoDO.setMchId(mchId);
                wxFundAcctInfoDO.setAvailableAmt(balanceDTO.getAvailableBalance());
                wxFundAcctInfoDO.setPendingAmt(balanceDTO.getUnAvailableBalance());
                wxFundAcctInfoDO.setMchType(mchTypeEnum.getCode());
                wxFundAcctInfoDO.setDeleted(0);
                wxFundAcctInfoDO.setWxAcctType("BASIC");
                Map<String, WxFundAcctInfoDO> fundAcctInfoMap = getWxFundAcctInfoMap(Collections.singletonList(mchId), pt);
                WxFundAcctInfoDO old = fundAcctInfoMap.get(mchId);
                if (fundAcctInfoMap.isEmpty()||Objects.isNull(old)) {
                    wxFundAcctInfoDO.init(new Date());
                    fundManager.xiaodianSaveAndUpdateFundBill(wxFundAcctInfoDO, null);
                } else {
                    wxFundAcctInfoDO.setId(old.getId());
                    wxFundAcctInfoDO.setGmtUpdate(System.currentTimeMillis());
                    fundManager.xiaodianSaveAndUpdateFundBill( null, wxFundAcctInfoDO);
                }

            } else {
                PaymentAccountPageReq paymentAccountPageReq = new PaymentAccountPageReq();
                paymentAccountPageReq.setChannelType(PaymentChannelTypeEnum.微信.getCode());
                int pageNo = 1;
                paymentAccountPageReq.setPageNo(pageNo);
                paymentAccountPageReq.setPageSize(500);
                List<PaymentBaseInfo> allPaymentBaseInfoList = new ArrayList<>();
                List<PaymentBaseInfo> paymentBaseInfoList = iotPaymentFacade.pagePaymentAccount(paymentAccountPageReq);
                while (!CollectionUtils.isEmpty(paymentBaseInfoList)) {
                    allPaymentBaseInfoList.addAll(paymentBaseInfoList);
                    pageNo++;
                    paymentAccountPageReq.setPageNo(pageNo);
                    paymentBaseInfoList = iotPaymentFacade.pagePaymentAccount(paymentAccountPageReq);
                }

                List<List<PaymentBaseInfo>> partner = Lists.partition(allPaymentBaseInfoList, 400);
                for (List<PaymentBaseInfo> paymentBaseInfos : partner) {
                    List<WxFundAcctInfoDO> insertList = new ArrayList<>();
                    List<WxFundAcctInfoDO> updateList = new ArrayList<>();
                    Map<String, WxFundAcctInfoDO> fundAcctInfoMap = getWxFundAcctInfoMap(paymentBaseInfos.stream().map(PaymentBaseInfo::getChannelSubMchNo).collect(Collectors.toList()), pt);
                    for (PaymentBaseInfo paymentBaseInfo : paymentBaseInfos) {
                        if (StringUtils.isEmpty(paymentBaseInfo.getChannelSubMchNo())) {
                            log.info("微信平台商户为空paymentBaseInfo:{}", JSON.toJSON(paymentBaseInfo));
                            continue;
                        }
                        BalanceDTO balanceDTO = weixinFacade.queryBalance(Long.valueOf(paymentBaseInfo.getChannelSubMchNo()));
                        if (Objects.isNull(balanceDTO)) {
                            log.info("微信余额查询失败paymentBaseInfo:{},pt:{}",paymentBaseInfo,pt);
                            continue;
                        }
                        WxFundAcctInfoDO wxFundAcctInfoDO = new WxFundAcctInfoDO();
                        wxFundAcctInfoDO.setPt(pt);
                        wxFundAcctInfoDO.setMchId(balanceDTO.getMerchantId());
                        wxFundAcctInfoDO.setAvailableAmt(balanceDTO.getAvailableBalance());
                        wxFundAcctInfoDO.setPendingAmt(balanceDTO.getUnAvailableBalance());
                        wxFundAcctInfoDO.setMchType(mchTypeEnum.getCode());
                        wxFundAcctInfoDO.setDeleted(0);
                        wxFundAcctInfoDO.setWxAcctType("BASIC");
                        WxFundAcctInfoDO old = fundAcctInfoMap.get(paymentBaseInfo.getChannelSubMchNo());
                        if (fundAcctInfoMap.isEmpty()||Objects.isNull(old)) {
                            wxFundAcctInfoDO.init();
                            insertList.add(wxFundAcctInfoDO);
                        } else {
                            wxFundAcctInfoDO.setId(old.getId());
                            wxFundAcctInfoDO.setGmtUpdate(System.currentTimeMillis());
                            updateList.add(wxFundAcctInfoDO);
                        }
                    }
                    fundManager.saveAndUpdateFundBill(insertList, updateList);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 获取map
     */
    private Map<String,WxFundAcctInfoDO> getWxFundAcctInfoMap (List<String> merchantIdList,Integer pt){
        if (CollectionUtils.isEmpty(merchantIdList)){
            return new HashMap<>();
        }
        WxFundAcctInfoParam wxFundAcctInfoParam = new WxFundAcctInfoParam();
        wxFundAcctInfoParam.setPt(pt);
        wxFundAcctInfoParam.setMerchantIdList(merchantIdList);
        List<WxFundAcctInfoDO> wxFundAcctInfoDOList = wxFundAcctInfoMapper.selectByParam(wxFundAcctInfoParam);
        if (CollectionUtils.isEmpty(wxFundAcctInfoDOList)){
            return new HashMap<>();
        }
        return wxFundAcctInfoDOList.stream().collect(Collectors.toMap(WxFundAcctInfoDO::getMchId, Function.identity(),(v1, v2)->v1));
    }
}
