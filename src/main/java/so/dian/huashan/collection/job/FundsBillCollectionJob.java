package so.dian.huashan.collection.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.genesis.api.payment.dto.PaymentBaseInfo;
import so.dian.genesis.api.payment.enums.PaymentChannelTypeEnum;
import so.dian.genesis.api.payment.req.PaymentAccountPageReq;
import so.dian.huashan.collection.enums.MchTypeEnum;
import so.dian.huashan.collection.manager.FundManager;
import so.dian.huashan.collection.mapper.WxFundAcctInfoMapper;
import so.dian.huashan.collection.mapper.dos.WxFundAcctInfoDO;
import so.dian.huashan.collection.mapper.param.WxFundAcctInfoParam;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.facade.IotPaymentFacade;
import so.dian.huashan.common.facade.WeixinFacade;
import so.dian.huashan.common.facade.remote.IotPaymentClient;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.model.dto.BalanceDTO;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FundsBillCollectionJob {
    @Autowired
    private ThirdpartyPullService thirdpartyPullService;

    @Value("${iot.xiaodian.mch.id:1}")
    private String mchId;
    @XxlJob("FundsBillCollectionJob")
    public ReturnT<String> execute(String param) {
        for (MchTypeEnum mchTypeEnum:MchTypeEnum.values()){
            try {
                thirdpartyPullService.pullFundJob(ThirdpartyPayType.WEIXIN_PARTNER,param, mchTypeEnum.getCode(),mchId);
            } catch (Exception e){
                log.error("微信资金流水失败mchType:{}",mchTypeEnum.getDesc(),e);
            }
        }
        return ReturnT.SUCCESS;
    }
}
