package so.dian.huashan.collection.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.service.CollectionVoucherService;
import so.dian.huashan.collection.service.entity.BinlogBO;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.pipeline.RetryExecutorCommand;
import so.dian.huashan.common.enums.CollectionVoucherStatus;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2024/01/05 09:29
 * @description: 用于处理旧的binlog凭证，以后这个可以下线掉
 */
@Slf4j
@Service
public class CollectionFailRetryV1Service {

    private final Map<String, String> tableAndPipelineBeanMap = Maps.newHashMap();

    @Autowired
    private CollectionVoucherService voucherService;

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        tableAndPipelineBeanMap.put("payment", "paymentPipeline");
        tableAndPipelineBeanMap.put("offline_pay", "offlinePayPipeline");
        tableAndPipelineBeanMap.put("hera_trans_order", "heraTransOrderPipeline");
    }

    public void process(CollectionVoucherBO voucherBO) {
        if (Objects.isNull(voucherBO))
            return;

        List<String> sources = Lists.newArrayList(voucherBO.getSource().split("\\."));
        String tableName = CollUtil.getLast(sources);
        if (StrUtil.isBlank(tableName))
            return;

        String pipelineBean = tableAndPipelineBeanMap.get(tableName);
        if (StrUtil.isBlank(pipelineBean)) {
            directSucess(voucherBO.getId(), voucherBO.getRetryTime());
            return;
        }

        try {

            CollectionVoucherBO rebuildVocher = rebuildVocher(voucherBO);
            CollectionPipeline pipeline = applicationContext.getBean(pipelineBean, CollectionPipeline.class);
            RetryExecutorCommand command = new RetryExecutorCommand(pipeline);
            command.execute(rebuildVocher);
        }catch (Exception e) {
            log.info("老版本的binlog日志处理异常, 凭证ID:{}", voucherBO.getId(), e);
        }
    }

    private CollectionVoucherBO rebuildVocher(CollectionVoucherBO voucherBO) {
        BinlogBO binlogBO = voucherBO.getVoucher();
        List<String> sources = Lists.newArrayList(voucherBO.getSource().split("#"));
        BinlogBO binlogV1 = new BinlogBO();
        binlogV1.setOriginalContent(binlogBO.getOriginalContent());
        binlogV1.setAfter(binlogBO.getOriginalContent());
        binlogV1.setBefore("{}");
        binlogV1.setTable(CollUtil.getLast(sources));
        binlogV1.setDbName(CollUtil.getFirst(sources));

        voucherBO.setVoucher(binlogV1);

        return voucherBO;
    }

    private void directSucess(Long voucherId, Integer retryTime) {

        CollectionVoucherBO voucherBO = CollectionVoucherBO.builder()
                .id(voucherId)
                .retryTime(Optional.ofNullable(retryTime).orElse(0) + 1)
                .status(CollectionVoucherStatus.SUCCESS)
                .remark(CollectionVoucherStatus.SUCCESS.getDesc())
                .build();
        voucherService.modify(voucherBO);
    }
}
