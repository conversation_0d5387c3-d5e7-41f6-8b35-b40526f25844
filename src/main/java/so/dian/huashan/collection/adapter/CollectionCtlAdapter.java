package so.dian.huashan.collection.adapter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.service.CollectionCtlService;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2024/01/04 18:19
 * @description:
 */
@Slf4j
@Component
public class CollectionCtlAdapter {

    private final ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    @Autowired
    private CollectionCtlService collectionCtlService;

    public void batchAsyncVoucherProcess(List<Long> voucherIds) {

        executorService.submit(() -> {
            for (Long voucherId : voucherIds) {
                try {
                    collectionCtlService.directProcess(voucherId);
                }catch (Exception e) {
                    log.error("直接处理凭证失败,凭证ID:{}", voucherId, e);
                    throw e;
                }
            }
            log.info("直接处理凭证本次处理完成");
        });
    }
}
