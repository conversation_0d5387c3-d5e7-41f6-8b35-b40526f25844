package so.dian.huashan.collection.enums;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/25 10:24
 * @description:
 */

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@AllArgsConstructor
@Getter
public enum CollectionWay {

    REAL_TIME((byte) 1, "实时"),
    REGULAR_TIME((byte) 2, "定时"),
    ;
    private final Byte code;
    private final String desc;

    public static CollectionWay from(Byte code) {
        return Arrays.stream(values())
                .filter(way -> way.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
