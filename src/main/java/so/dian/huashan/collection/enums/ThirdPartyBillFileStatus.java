package so.dian.huashan.collection.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/24 15:37
 * @description:
 */
@Getter
@AllArgsConstructor
public enum ThirdPartyBillFileStatus {

    INITIALIZATION(0, "初始化"),
    PROCESSING(1, "处理中"),
    SUCCESS(2, "成功"),
    FAILURE(3, "失败"),
    ;

    private final Integer code;

    private final String desc;

    public static ThirdPartyBillFileStatus from(Byte code) {
        return Arrays.stream(values())
                .filter(v -> v.code == code.intValue())
                .findFirst().orElse(null);
    }
}
