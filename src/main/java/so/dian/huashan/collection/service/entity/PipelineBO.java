package so.dian.huashan.collection.service.entity;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/13 14:26
 * @description:
 */
@Slf4j
@Getter
public class PipelineBO {

    private final Long voucherId;

    private final BinlogBO binLog;

    public PipelineBO(BinlogBO binLog, Long voucherId) {
        this.binLog = Objects.requireNonNull(binLog, "binlog must not null");
        this.voucherId = Objects.requireNonNull(voucherId, "voucherId must not null");
    }

    public boolean verify() {
        return binLog.verify();
    }
}
