package so.dian.huashan.collection.service.listener;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import jodd.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.listener.JobExecutionListenerSupport;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.collection.mapper.ThirdPartyBillFileMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO;
import so.dian.huashan.collection.service.entity.ThirdPartyBillFileBO;
import so.dian.huashan.collection.service.thirdparty.ChannelBillApiService;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.utils.QCloudHelper;
import so.dian.huashan.framework.sftp.SftpService;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;

/**
 * 批处理job监听实现类
 *
 * @author: shenhe
 * @Date: 2019-10-29 00:16
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Slf4j
@Component
public class BaseListener extends JobExecutionListenerSupport {

    protected static final String      normalColor = "#0000EE";

    protected static final String      errorColor  = "#dd0000";

    private long                       startTime;

    @Value("${huashan.thirdparty.bill.file:#{systemProperties['user.home']}}")
    private String defaultDirectory;

    @Resource
    private ChannelBillApiService channelBillApiService;

    @Resource
    private ThreadPoolTaskExecutor commonExecutor;

    @Resource
    private ThirdPartyBillFileMapper thirdPartyBillFileMapper;

    @Value("${huashan.thirdparty.bill.upload:true}")
    private Boolean upload;

    @Resource
    private SftpService sftpService;

    @Value("${sftp.remote.weixin.directory}")
    private String weixinDirectory;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        startTime = System.currentTimeMillis();
        log.info("批处理任务开始执行，jobExecution: {} ", JSON.toJSON(jobExecution.getJobParameters()));
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        JobParameters jobParameters = jobExecution.getJobParameters();
        String payType = jobParameters.getString("payType");
        String billDate = jobParameters.getString("billDate");
        // 将文件上传至cos，更新filePath
        try {
            if (upload)
                uploadFile(payType,billDate);
        }catch (Exception e){
            log.error("上传异常,payType={},billDate={}",payType,billDate);
        }
        log.info("payType:{}, billDate:{},jobExecution={}, Job Cost Time : {}ms", payType,billDate, jobExecution.getStatus(),
                 (System.currentTimeMillis() - startTime));
    }

    public void uploadFile(String payType,String billDate) {
        ThirdpartyPayType thirdpartyPayType = ThirdpartyPayType.fromName(payType);
        Integer billDateInt = Integer.valueOf(DateUtil.format(DateUtil.parseDate(billDate), DatePattern.PURE_DATE_PATTERN));

        List<ThirdPartyBillFileBO> fileList = channelBillApiService.listBillFile(thirdpartyPayType,billDateInt);
        if (CollectionUtils.isEmpty(fileList)) {
            log.info("任务未查询到账单文件记录，不继续上传操作，payType:{},billDate:{}", payType,billDate);
            return;
        }

        for (ThirdPartyBillFileBO filePath : fileList) {
            if (StringUtils.isBlank(filePath.getFilePath())) {
                continue;
            }
            if (filePath.getFilePath().startsWith(QCloudHelper.COS_QCLOUD)) {
                continue;
            }
            if (!new File(filePath.getFilePath()).exists()) {
                log.info("uploadFile文件不存在：thirdpartyPayType:{},billDate:{}, filepath:{}",payType, billDate, filePath.getFilePath());
                continue;
            }

            log.info("开始处理本地账单文件上传：filePathId:{}, filepath:{}", filePath.getId(), filePath.getFilePath());

            commonExecutor.execute(new FileUploadThread(filePath.getId()));
        }
    }

    class FileUploadThread extends Thread {

        private Long filePathId;

        public FileUploadThread(Long filePathId){
            this.filePathId = filePathId;
        }

        public void run() {

            ThirdPartyBillFileDO filePathDo = thirdPartyBillFileMapper.selectByPrimaryKey(filePathId);
            if (ThirdpartyPayType.WEIXIN_FD.name().equals(filePathDo.getChannelPayType())) {
                log.info("微信前端分账文件上传sftp, filePathId:{}", filePathId);
                try {
                    sftpService.uploadFile(new File(filePathDo.getFilePath()), weixinDirectory);
                } catch (Exception e) {
                    log.error("微信前端分账文件上传sftp失败, filePathId:{}", filePathId);
                }
            }
            String localFilePath = filePathDo.getFilePath();

            String objectName = filePathDo.getFilePath().replace(defaultDirectory, "");

            BizResult<String> result = QCloudHelper.uploadSliceFile(filePathDo.getFilePath(), objectName);

            log.info("上传腾讯云返回结果, filePathId:{}, result:{}", filePathId, JSON.toJSONString(result));
            if (result.isSuccess()) {
                try {
                    FileUtil.delete(localFilePath);
                } catch (Exception e) {
                    log.error("上传账单文件至腾讯云成功，删除本地文件时出现异常，filePathId:{}, localFilePath:{}", filePathDo.getId(),
                            localFilePath);
                }
                filePathDo.setFilePath(result.getData());
                int count = thirdPartyBillFileMapper.updateByPrimaryKeySelective(filePathDo);
                log.info("上传腾讯云完成，成功发送消息提醒，filePathId:{}, result:{}", filePathId, JSON.toJSONString(result));
            }else{
                try {
                    FileUtil.delete(localFilePath);
                    thirdPartyBillFileMapper.deleteByPrimaryKey(filePathId);
                } catch (Exception e) {
                    log.error("上传账单文件至腾讯云失败，删除本地文件时出现异常，filePathId:{}, localFilePath:{}", filePathDo.getId(),
                            localFilePath);
                }
                log.info("上传腾讯云s失败，发送消息提醒，filePathId:{}, result:{}", filePathId, JSON.toJSONString(result));

            }
        }
    }
}
