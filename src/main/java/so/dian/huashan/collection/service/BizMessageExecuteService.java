package so.dian.huashan.collection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.mapper.BillSettleControlOrderMapper;
import so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO;
import so.dian.huashan.collection.service.entity.SettleControlOrderMessage;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/12/18 14:11
 * @description:
 */
@Slf4j
@Service
public class BizMessageExecuteService {

    @Autowired
    private BillSettleControlOrderMapper settleControlOrderMapper;

    public void executeControlOrder(String message) {
        if (StrUtil.isBlank(message))
            return;

        SettleControlOrderMessage controlOrderMessage = JSON.parseObject(message, SettleControlOrderMessage.class);
        boolean isNeedSave = Boolean.TRUE;
        List<BillSettleControlOrderDO> controlOrderDOS = settleControlOrderMapper.selectByOrderNos(Collections.singletonList(controlOrderMessage.getOrderNo()));
        if (CollUtil.isNotEmpty(controlOrderDOS)) {
            for (BillSettleControlOrderDO controlOrderDO : controlOrderDOS) {
                if (controlOrderDO.getMainBizId().equals(controlOrderMessage.getMainBizId())
                        && controlOrderDO.getMainBizType().equals(controlOrderMessage.getMainBizType())
                        && controlOrderDO.getSettleSubjectId().equals(controlOrderMessage.getSettleSubjectId())
                        && controlOrderDO.getSettleSubjectType().equals(controlOrderMessage.getSettleSubjectType())) {
                    if (!Objects.equals(controlOrderDO.getControlStatus(), controlOrderMessage.getStatus())) {
                        controlOrderDO.setControlStatus(controlOrderMessage.getStatus());
                        controlOrderDO.modify();
                        settleControlOrderMapper.updateByPrimaryKeySelective(controlOrderDO);
                    }
                    isNeedSave = Boolean.FALSE;
                    break;
                }
            }
        }

        if (isNeedSave) {
            save(controlOrderMessage);
        }
    }

    private void save(SettleControlOrderMessage controlOrderMessage) {
        BillSettleControlOrderDO controlOrderDO = new BillSettleControlOrderDO();
        controlOrderDO.setOrderNo(controlOrderMessage.getOrderNo());
        controlOrderDO.setMainBizType(controlOrderMessage.getMainBizType());
        controlOrderDO.setMainBizId(controlOrderMessage.getMainBizId());
        controlOrderDO.setSettleSubjectId(controlOrderMessage.getSettleSubjectId());
        controlOrderDO.setSettleSubjectType(controlOrderMessage.getSettleSubjectType());
        controlOrderDO.setControlStatus(controlOrderMessage.getStatus());
        controlOrderDO.init();
        settleControlOrderMapper.insertSelective(controlOrderDO);
    }
}
