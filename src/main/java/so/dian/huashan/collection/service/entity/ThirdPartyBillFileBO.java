package so.dian.huashan.collection.service.entity;

import lombok.Data;
import so.dian.huashan.collection.enums.ThirdPartyBillFileStatus;
import so.dian.huashan.common.enums.ThirdpartyPayType;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/24 15:41
 * @description:
 */
@Data
public class ThirdPartyBillFileBO {

    private Long id;

    /**
     * 执行状态（0-初始化，1-进行中，2-成功，3-失败）
     */
    private ThirdPartyBillFileStatus status;

    /**
     * 账单日
     */
    private Integer billDate;

    /**
     * 采集注册id
     */
    private Long collectionRegistryId;

    /**
     * 渠道支付类型
     */
    private ThirdpartyPayType channelPayType;

    /**
     * 文件路径
     */
    private String filePath;

}
