package so.dian.huashan.collection.service.thirdparty;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.mapper.ThirdPartyBillFileMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: miaoshuai
 * @create: 2023/03/21 16:47
 * @description:
 */
@Service
@StepScope
public class PullBillWriter implements ItemWriter<List<String>> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    @Value("#{jobParameters[collectionRegistryId]}")
    private Long collectionRegistryId;

    @Value("#{jobParameters[billDate]}")
    private String billDateStr;

    @Resource
    private ThirdPartyBillFileMapper billFileMapper;

    @Override
    public void write(List<? extends List<String>> items) {
        Integer billDate = Integer.valueOf(DateUtil.format(DateUtil.parseDate(billDateStr), DatePattern.PURE_DATE_PATTERN));

        for (List<String> item : items) {
            if (CollectionUtils.isEmpty(item)){
                continue;
            }
            item.forEach(billFilePath -> {
                ThirdPartyBillFileDO billFile = new ThirdPartyBillFileDO();
                billFile.setFilePath(billFilePath);
                billFile.setBillDate(billDate);
                billFile.setCollectionRegistryId(collectionRegistryId);
                billFile.setChannelPayType(payTypeName);
                billFile.setStatus((byte) 0);
                billFile.init();
                billFileMapper.insertSelective(billFile);
            });
        }
    }
}
