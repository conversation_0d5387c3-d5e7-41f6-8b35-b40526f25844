package so.dian.huashan.collection.service.entity;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;
import java.util.concurrent.Future;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/09/28 11:12
 * @description:
 */
@Getter
public class CollectionProcessResult {

    private final Boolean success;

    private Long voucherId;

    private List<Long> incomeOriginalIds;

    private CollectionProcessResult(boolean success) {
        this.success = success;
    }

    public static CollectionProcessResult success() {
        return new CollectionProcessResult(Boolean.TRUE);
    }

    public static CollectionProcessResult fail() {
        return new CollectionProcessResult(Boolean.FALSE);
    }

    public CollectionProcessResult voucherId(Long voucherId) {
        this.voucherId = voucherId;
        return this;
    }

    public CollectionProcessResult addIncomeOriginalId(Long incomeOriginalId) {
        if (this.incomeOriginalIds == null)
            this.incomeOriginalIds = Lists.newArrayList();
        this.incomeOriginalIds.add(incomeOriginalId);
        return this;
    }

    public Future<CollectionProcessResult> FutureImmediately() {
        return new FutureImmediately<>(this);
    }
}
