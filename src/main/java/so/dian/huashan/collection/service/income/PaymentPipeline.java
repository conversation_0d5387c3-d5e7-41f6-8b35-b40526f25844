package so.dian.huashan.collection.service.income;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.config.BizProperties;
import so.dian.huashan.collection.mapper.HuazhuIncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.dos.HuazhuIncomeOriginalInfoDO;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.common.enums.BillSourceEnum;
import so.dian.huashan.common.mapper.lhc.PaymentMapper;
import so.dian.huashan.common.mapper.lhc.RefundOrderMapper;
import so.dian.huashan.common.mapper.lhc.entity.PaymentDO;
import so.dian.huashan.common.mapper.lhc.entity.RefundOrderDO;
import so.dian.huashan.common.mapper.newyork.dos.ShardingRefundOrderDO;
import so.dian.huashan.common.mapper.newyork.dos.ShardingTradeOrderDO;
import so.dian.huashan.common.mapper.newyork.dos.TidbTradeOrderDO;
import so.dian.huashan.common.mapper.newyork.sharding.ShardingRefundOrderMapper;
import so.dian.huashan.common.service.ShardingTradeOrderService;
import so.dian.huashan.common.service.TidbTradeOrderService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static so.dian.huashan.common.constant.CommonConstants.*;
import static so.dian.huashan.common.enums.BizErrorCodeEnum.NOT_FIND_REFUND_ORDER;
import static so.dian.huashan.common.enums.BizErrorCodeEnum.NOT_FIND_TRADE_ORDER;

/**
 * @author: miaoshuai
 * @create: 2023/03/21 15:18
 * @description:
 */
@Slf4j
@Service
public class PaymentPipeline extends CollectionPipeline {

    private static final List<Integer> ALLOW_STATUSES = Lists.newArrayList(2, 3);

    @Resource
    private PaymentMapper paymentMapper;

    @Resource
    private IncomeOriginalInfoMapper incomeOriginalInfoMapper;

    @Autowired
    private ShardingTradeOrderService shardingTradeOrderService;

    @Autowired
    private ShardingRefundOrderMapper shardingRefundOrderMapper;

    @Autowired
    private RefundOrderMapper refundOrderMapper;

    @Resource
    private TidbTradeOrderService tradeOrderService;

    @Autowired
    private BizProperties bizProperties;

    @Autowired
    private HuazhuIncomeOriginalInfoMapper huazhuIncomeOriginalInfoMapper;

    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        if(StringUtils.isBlank(pipelineBO.getBinLog().getAfter())){
            return true;
        }
        PaymentDO paymentDO = JSON.parseObject(pipelineBO.getBinLog().getAfter(), PaymentDO.class);
        if (CollUtil.isNotEmpty(bizProperties.getSkipBizTypes())
                && bizProperties.getSkipBizTypes().stream().anyMatch(bizType -> bizType.equals(paymentDO.getBizType())))
            return true;

        return ALLOW_STATUSES.stream().noneMatch(status -> status.equals(paymentDO.getStatus()));
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        PaymentDO paymentDO = JSON.parseObject(pipelineBO.getBinLog().getAfter(), PaymentDO.class);
        if(StringUtils.isBlank(paymentDO.getPayNo())){
//            log.warn("支付单号为空，不处理这样的数据,id = {}",id);
            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId());
        }

        // 状态为3表示是退款订单
        if (isNeedFindRefundOrder(paymentDO)) {
            List<RefundOrderDO> refundOrderDOS = refundOrderMapper.selectByPayOrderNo(paymentDO.getTradeNo());
            if (CollUtil.isEmpty(refundOrderDOS))
                throw BizException.create(NOT_FIND_REFUND_ORDER);

            CollectionProcessResult processResult = CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId());
            for (RefundOrderDO refundOrderDO : refundOrderDOS) {
                IncomeOriginalInfoDO incomeOriginalInfoDO = buildIncomeOriginalInfo(paymentDO);
                incomeOriginalInfoDO.setTradeAmount(refundOrderDO.getRefundAmount().intValue());
                incomeOriginalInfoDO.setVoucherId(pipelineBO.getVoucherId());
                incomeOriginalInfoDO.setTradeNo(refundOrderDO.getBizRefundNo());
                incomeOriginalInfoDO.setDianPayNo(refundOrderDO.getRefundOrderNo());
                String key  = incomeOriginalInfoDO.getDataSource() + "_" + incomeOriginalInfoDO.getTradeNo() + "_" + incomeOriginalInfoDO.getDianPayNo()  + "_" + incomeOriginalInfoDO.getTradeType();
                incomeOriginalInfoDO.setIdempotentNo(key);
                saveOrUpdate(incomeOriginalInfoDO);
                processResult.addIncomeOriginalId(incomeOriginalInfoDO.getId());
            }

            return processResult;
        }else {
            IncomeOriginalInfoDO incomeOriginalInfoDO = buildIncomeOriginalInfo(paymentDO);
            incomeOriginalInfoDO.setVoucherId(pipelineBO.getVoucherId());
            incomeOriginalInfoDO.setTradeNo(paymentDO.getOrderNo());
            incomeOriginalInfoDO.setDianPayNo(paymentDO.getTradeNo());
            String key  = incomeOriginalInfoDO.getDataSource() + "_" + incomeOriginalInfoDO.getTradeNo() + "_" + incomeOriginalInfoDO.getDianPayNo()  + "_" + incomeOriginalInfoDO.getTradeType();
            incomeOriginalInfoDO.setIdempotentNo(key);
            saveOrUpdate(incomeOriginalInfoDO);

            if (PAYMENT_HUAZHU_BIZ_TYPE.equals(paymentDO.getBizType()))
                saveHuazhuIncomeInfo(paymentDO, incomeOriginalInfoDO.getId());

            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(incomeOriginalInfoDO.getId());
        }
    }

    private boolean isNeedFindRefundOrder(PaymentDO paymentDO) {
        if (3 != paymentDO.getStatus())
            return Boolean.FALSE;

        if (bizProperties.getUnipayBizTypes().stream().anyMatch(bizType -> bizType.equals(paymentDO.getBizType())))
            return Boolean.TRUE;

        if (bizProperties.getNotFindRefundBizTypes().stream().anyMatch(bizType -> bizType.equals(paymentDO.getBizType())))
            return Boolean.FALSE;

        String oldOrderMark;
        if (LEASE_BIZ_TYPE.equals(paymentDO.getBizType()) || LEASE_BIZ_TYPE_100.equals(paymentDO.getBizType())) {
            List<PaymentDO> paymentDOS = paymentMapper.selectByOrderNo(paymentDO.getOrderNo());
            if (paymentDOS.stream().anyMatch(payment -> payment.getPayType() == 6))
                return Boolean.FALSE;

            List<ShardingRefundOrderDO> refundOrderDOS = shardingRefundOrderMapper.selectByBizOrderNo(paymentDO.getOrderNo());
            if (refundOrderDOS.stream().allMatch(refundOrder -> refundOrder.getRefundType() != 1)) {
                return Boolean.FALSE;
            }
            ShardingTradeOrderDO shardingTradeOrderDO = shardingTradeOrderService.find(paymentDO.getOrderNo());
            if (Objects.isNull(shardingTradeOrderDO))
                throw BizException.create(NOT_FIND_TRADE_ORDER);
            oldOrderMark = shardingTradeOrderDO.getExt6();
            return !ORIGINAL_MARK.equals(oldOrderMark);
        }else {

            TidbTradeOrderDO tidbTradeOrderDO = tradeOrderService.find(Long.parseLong(paymentDO.getOrderNo()));
            if (Objects.isNull(tidbTradeOrderDO))
                throw BizException.create(NOT_FIND_TRADE_ORDER);
            oldOrderMark = tidbTradeOrderDO.getExt6();
            return UNIPAY_MARK.equals(oldOrderMark);
        }
    }

    private IncomeOriginalInfoDO buildIncomeOriginalInfo(PaymentDO paymentDO) {
        IncomeOriginalInfoDO incomeOriginalInfoDO = new IncomeOriginalInfoDO();
        incomeOriginalInfoDO.setDataSource(1);
        incomeOriginalInfoDO.setPayType(paymentDO.getPayType());
        incomeOriginalInfoDO.setBizType(paymentDO.getBizType());
        Integer happenDate = paymentDO.getPayTime() != null ? Integer.valueOf(LocalDateUtils.format(paymentDO.getPayTime(), DatePattern.PURE_DATE_PATTERN)) : Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
        incomeOriginalInfoDO.setPayNo(paymentDO.getPayNo());
        if(paymentDO.getPayType().equals(16) && paymentDO.getPayTypeRoute().equals(1)){
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.BAOFU.getId());
        }else if(paymentDO.getPayType().equals(16) && paymentDO.getPayTypeRoute().equals(2)){
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.WEIMEI.getId());
        }else{
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.getByPayType(paymentDO.getPayType()).getId());
        }
        incomeOriginalInfoDO.setStatus(1);
        if(paymentDO.getStatus().equals(2)){
            incomeOriginalInfoDO.setTradeType("in");
            incomeOriginalInfoDO.setTradeAmount(paymentDO.getPayAmount());
            if(paymentDO.getPayTime() == null){
                incomeOriginalInfoDO.setTradeTime(paymentDO.getCreateTime() == null ? new Date() : paymentDO.getCreateTime());
            }else{
                incomeOriginalInfoDO.setTradeTime(paymentDO.getPayTime());
            }
        }else if(paymentDO.getStatus().equals(3)){
            incomeOriginalInfoDO.setTradeType("out");
            incomeOriginalInfoDO.setTradeAmount(paymentDO.getRefundAmount());
            incomeOriginalInfoDO.setTradeTime(paymentDO.getRefundTime());
            happenDate = paymentDO.getRefundTime() != null ? Integer.valueOf(LocalDateUtils.format(paymentDO.getRefundTime(), DatePattern.PURE_DATE_PATTERN)) : Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
        }

        incomeOriginalInfoDO.setHappenDate(happenDate);

        return incomeOriginalInfoDO;
    }

    private void saveOrUpdate(IncomeOriginalInfoDO incomeOriginalInfo) {
        // 查询数据库
        IncomeOriginalInfoDO incomeOriginalInfoDO = incomeOriginalInfoMapper.selectByIdempotentNo(incomeOriginalInfo.getIdempotentNo());
        if(incomeOriginalInfoDO == null){
            incomeOriginalInfo.init();
            incomeOriginalInfoMapper.insertSelective(incomeOriginalInfo);
        }else{
            incomeOriginalInfo.setId(incomeOriginalInfoDO.getId());
            incomeOriginalInfo.setGmtUpdate(System.currentTimeMillis());
            incomeOriginalInfoMapper.updateByPrimaryKeySelective(incomeOriginalInfo);
        }
    }

    private void saveHuazhuIncomeInfo(PaymentDO paymentDO, Long incomeOriginalId) {
        HuazhuIncomeOriginalInfoDO huazhuIncomeOriginalInfoDO = huazhuIncomeOriginalInfoMapper.selectByIncomeOriginalId(incomeOriginalId);
        if (Objects.isNull(huazhuIncomeOriginalInfoDO)) {
            huazhuIncomeOriginalInfoDO = new HuazhuIncomeOriginalInfoDO();
            huazhuIncomeOriginalInfoDO.setIncomeOriginalId(incomeOriginalId);
            huazhuIncomeOriginalInfoDO.setStatus((byte) 0);
            huazhuIncomeOriginalInfoDO.setOrderNo(paymentDO.getOrderNo());
            huazhuIncomeOriginalInfoDO.init();
            huazhuIncomeOriginalInfoMapper.insertSelective(huazhuIncomeOriginalInfoDO);
        }else {
            huazhuIncomeOriginalInfoDO.setOrderNo(paymentDO.getOrderNo());
            huazhuIncomeOriginalInfoMapper.updateByPrimaryKeySelective(huazhuIncomeOriginalInfoDO);
        }
    }
}
