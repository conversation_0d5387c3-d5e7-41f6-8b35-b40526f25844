package so.dian.huashan.collection.service.thirdparty.weixin.partner;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import so.dian.huashan.collection.service.thirdparty.weixin.WeixinBill;
import so.dian.huashan.common.constant.ChannelConstant;

/**
 * 微信账务账单文件账单csv文件字段映射
 *
 */
@Slf4j
public class WeixinpartnerBillFieldSetMapper extends DefaultLineMapper<WeixinpartnerBill> implements FieldSetMapper<WeixinpartnerBill> {

    public WeixinpartnerBillFieldSetMapper() {
        DelimitedLineTokenizer lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setDelimiter(",");
        lineTokenizer.setStrict(false);
        lineTokenizer.setNames(ChannelConstant.weixinParterAccountBillColumn.split(","));

        setLineTokenizer(lineTokenizer);
        setFieldSetMapper(this);
    }

    @Override
    @NonNull
    public WeixinpartnerBill mapFieldSet(@NonNull FieldSet fieldSet) {

        if (!fieldSet.readString("accountTime").contains("-")) {
            log.info("跳过行：" + JSON.toJSONString(fieldSet));
            throw new FlatFileParseException(fieldSet.readString("accountTime"), StrUtil.EMPTY);
        }

        WeixinpartnerBill result = new WeixinpartnerBill();

        //记账时间
        result.setAccountTime(getTextString(fieldSet, "accountTime"));
        //资金变更提交申请人
        result.setApplicantName(getTextString(fieldSet, "applicantName"));
        //账户结余
        result.setBalance(getTextString(fieldSet, "balance"));
        //业务名称
        result.setBizName(getTextString(fieldSet, "bizName"));
        //微信支付业务单号
        result.setBizNo(getTextString(fieldSet, "bizNo"));
        //业务凭证号
        result.setBizVoucherNo(getTextString(fieldSet, "bizVoucherNo"));
        //业务类型
        result.setBizType(getTextString(fieldSet, "bizType"));
        //备注
        result.setRemark(getTextString(fieldSet, "remark"));
        //资金流水单号
        result.setSeqNo(getTextString(fieldSet, "seqNo"));
        //收支金额
        result.setTradeAmount(getTextString(fieldSet, "tradeAmount"));
        //收支类型
        result.setTradeType(getTextString(fieldSet, "tradeType"));
        //二级商户号
        result.setMchId(getTextString(fieldSet, "mchId"));
        //账户类型
        result.setAccountType(getTextString(fieldSet, "accountType"));

        return result;
    }

    private String getTextString(FieldSet fieldSet, String key) {
        String replace = fieldSet.readString(key).replace("`", "");
        return StrUtil.isBlank(replace) ? null : replace;
    }

}
