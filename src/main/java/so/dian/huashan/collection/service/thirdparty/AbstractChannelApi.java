package so.dian.huashan.collection.service.thirdparty;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import so.dian.huashan.common.utils.ThirdPartyBillPathUtil;
import so.dian.huashan.common.utils.ZipUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 抽象渠道api实现
 */
@Slf4j
public abstract class AbstractChannelApi implements ChannelBillApi {

    /**
     * 第三方账单目录
     */
    @Value("${huashan.thirdparty.bill.file:#{systemProperties['user.home']}}")
    protected String defaultDirectory;

    // 账单csv文件扩展名
    protected static final String fileCsvExt = ".csv";

    // 账单压缩文件扩展名
    protected static final String fileZipExt = ".zip";

    protected abstract List<String> toPullBillFile(BillRequestParam requestParam) throws Exception;

    @Override
    public List<String> pullBillFile(BillRequestParam requestParam) throws Exception {

        // 账单文件路径
        String billFile = ThirdPartyBillPathUtil.getCsvFilePath(defaultDirectory, requestParam.getBillDate(), requestParam.getPayType());

        List<String> billFiles = Lists.newArrayList();
        if (new File(billFile).exists()) {
            log.info("账单文件在本机已经拉取过账单文件不再重新拉取,requestParam:{}", JSON.toJSONString(requestParam));
        } else {
            billFiles = toPullBillFile(requestParam);
        }

        return billFiles;
    }

    /**
     * 当前任务目录下的文件list
     */
    protected List<File> loadFiles(BillRequestParam requestParam) {
        List<File> fileResult = new ArrayList<>();
        String directory = getDirectory(requestParam);
        List<File> fileList = FileUtil.loopFiles(directory);
        if (CollectionUtils.isNotEmpty(fileList)) {
            for (File file : fileList) {
                if (fileCsvExt.equals(ZipUtil.getFileExtName(file.getName()))
                    || fileZipExt.equals(ZipUtil.getFileExtName(file.getName()))) {
                    fileResult.add(file);
                }
            }
        }
        return fileResult;
    }

    protected String getDirectory(BillRequestParam requestParam) {
        return ThirdPartyBillPathUtil.getDirectory(defaultDirectory, requestParam.getBillDate(),requestParam.getPayType());
    }

    /**
     * 构建账单目录及文件名
     */
    protected String getDestFilePath(BillRequestParam requestParam) {
        return ThirdPartyBillPathUtil.getCsvFilePath(defaultDirectory, requestParam.getBillDate(), requestParam.getPayType());
    }

    protected String getZipDestFilePath(BillRequestParam requestParam) {
        return ThirdPartyBillPathUtil.getZipFilePath(defaultDirectory, requestParam.getBillDate(), requestParam.getPayType());
    }
}
