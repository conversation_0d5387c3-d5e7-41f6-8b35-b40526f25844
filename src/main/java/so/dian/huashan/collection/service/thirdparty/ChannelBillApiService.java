package so.dian.huashan.collection.service.thirdparty;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.enums.ThirdPartyBillFileStatus;
import so.dian.huashan.collection.mapper.ThirdPartyBillFileMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO;
import so.dian.huashan.collection.mapper.example.ThirdPartyBillFileExample;
import so.dian.huashan.collection.service.entity.ThirdPartyBillFileBO;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 渠道api实现
 *
 * <AUTHOR>
 * @date 2010-10-19 17:49
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Slf4j
@Service
public class ChannelBillApiService implements ChannelBillApi {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ThirdPartyBillFileMapper thirdPartyBillFileMapper;

    /**
     * 拉取账单文件
     */
    public List<String> pullBillFile(BillRequestParam requestParam) throws Exception {

        String channelName = requestParam.getConfig().getPayType().getChannel().name().toLowerCase();

        ChannelBillApi channelApi = applicationContext.getBean(channelName + "ChannelApi", ChannelBillApi.class);

        return channelApi.pullBillFile(requestParam);
    }

    public List<ThirdPartyBillFileBO> listBillFile(ThirdpartyPayType payType, Integer billDate) {

        ThirdPartyBillFileExample example = ThirdPartyBillFileExample.builder().channelPayType(payType)
                .billDate(billDate).build();
        List<ThirdPartyBillFileDO> thirdPartyBillFileDOS = thirdPartyBillFileMapper.selectByExample(example);
        return thirdPartyBillFileDOS.stream()
                .map(fileDO -> {
                    ThirdPartyBillFileBO billFileBO = new ThirdPartyBillFileBO();
                    BeanUtil.copyProperties(fileDO, billFileBO);

                    billFileBO.setChannelPayType(ThirdpartyPayType.fromName(fileDO.getChannelPayType()));
                    billFileBO.setStatus(ThirdPartyBillFileStatus.from(fileDO.getStatus()));
                    return billFileBO;
                }).collect(Collectors.toList());
    }

    public List<ThirdPartyBillFileBO> xiyoukelistBillFile(ThirdpartyPayType payType, Integer billDate, String channelId) {

        ThirdPartyBillFileExample example = ThirdPartyBillFileExample.builder().channelPayType(payType)
                .billDate(billDate).build();
        List<ThirdPartyBillFileDO> thirdPartyBillFileDOS = thirdPartyBillFileMapper.selectByExample(example);
        List<ThirdPartyBillFileBO> thirdPartyBillFileBOList = new ArrayList<>();
        for (ThirdPartyBillFileDO thirdPartyBillFileDO : thirdPartyBillFileDOS) {
            if (StringUtils.isBlank(thirdPartyBillFileDO.getFilePath())) {
                log.error("xiyouke对账文件路径为空 billFile:{}", JSON.toJSON(thirdPartyBillFileDO));
                continue;
            }
            String[] channelIdString = thirdPartyBillFileDO.getFilePath().split("_");
            String fileChannelId = CollUtil.get(Arrays.asList(channelIdString), 1);
            if (!channelId.equals(fileChannelId)) {
                continue;
            }
            ThirdPartyBillFileBO billFileBO = new ThirdPartyBillFileBO();
            BeanUtil.copyProperties(thirdPartyBillFileDO, billFileBO);
            billFileBO.setChannelPayType(ThirdpartyPayType.fromName(thirdPartyBillFileDO.getChannelPayType()));
            billFileBO.setStatus(ThirdPartyBillFileStatus.from(thirdPartyBillFileDO.getStatus()));
            thirdPartyBillFileBOList.add(billFileBO);
        }
        return thirdPartyBillFileBOList;
    }
}
