package so.dian.huashan.collection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.collection.controller.request.CollectionPipelineReq;
import so.dian.huashan.collection.enums.CollectionWay;
import so.dian.huashan.collection.facade.BizDomainFacade;
import so.dian.huashan.collection.facade.CollectionPipelineFacade;
import so.dian.huashan.collection.service.entity.BizDomainBO;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static so.dian.huashan.common.constant.CacheNameConsts.CACHE_PIPELINE_NAME;
import static so.dian.huashan.common.constant.CacheNameConsts.CACHE_TABLE_NAME;
import static so.dian.huashan.common.enums.BizErrorCode.BIZ_EXCEPTION;

/**
 * @author: miaoshuai
 * @create: 2023/12/21 17:51
 * @description:
 */
@Slf4j
@Service
public class CollectionPipelineService {

    @Autowired
    private CollectionPipelineFacade collectionPipelineFacade;

    @Autowired
    private BizDomainFacade bizDomainFacade;

    @Autowired
    private CollectionPipelineService self;

    @Autowired
    private RedissonClient redissonClient;

//    @Cacheable(value = {CACHE_PIPELINE_NAME}, sync = true, key = "#tableName")
//    @CacheExpire(ttl = 60, unit = TimeUnit.DAYS)
    public CollectionPipelineBO find(String tableName) {

        if (StrUtil.isBlank(tableName))
            return null;
//
//        if ("lhc.payment".equals(tableName)) {
//            return CollectionPipelineBO.builder()
//                    .id(1L)
//                    .bizDomainId(1L)
//                    .strategyCode("paymentPipeline")
//                    .collectionWay(CollectionWay.REAL_TIME)
//                    .tableName("lhc.payment")
//                    .build();
//        }
//        if ("lhc.offline_pay".equals(tableName)) {
//            return CollectionPipelineBO.builder()
//                    .id(2L)
//                    .bizDomainId(1L)
//                    .strategyCode("offlinePayPipeline")
//                    .collectionWay(CollectionWay.REAL_TIME)
//                    .tableName("lhc.offline_pay")
//                    .build();
//        }
//        if ("lhc.hera_trans_order".equals(tableName)) {
//            return CollectionPipelineBO.builder()
//                    .id(3L)
//                    .bizDomainId(1L)
//                    .strategyCode("heraTransOrderPipeline")
//                    .collectionWay(CollectionWay.REAL_TIME)
//                    .tableName("lhc.hera_trans_order")
//                    .build();
//        }
//        if ("lhc.orders_box".equals(tableName)) {
//            return CollectionPipelineBO.builder()
//                    .id(4L)
//                    .bizDomainId(30001L)
//                    .strategyCode("billSettleOrdersBoxPipeline")
//                    .collectionWay(CollectionWay.REAL_TIME)
//                    .tableName("lhc.orders_box")
//                    .build();
//        }

        RBucket<CollectionPipelineBO> pipelineRBucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, CACHE_PIPELINE_NAME, tableName));
        CollectionPipelineBO pipelineBO = pipelineRBucket.get();
        if (Objects.nonNull(pipelineBO)) {
            return pipelineBO;
        }
        log.info("开始加载管道信息，表名:{}", tableName);
        pipelineBO = collectionPipelineFacade.findByTableName(tableName);
        if (Objects.nonNull(pipelineBO))
            pipelineRBucket.set(pipelineBO, 60, TimeUnit.DAYS);
        return pipelineBO;
//        return collectionPipelineFacade.findByTableName(tableName);
    }

//    @CacheEvict(value = {CACHE_PIPELINE_NAME}, key = "#tableName")
//    public void evictPipeline(String tableName) {}

//    @Cacheable(value = {CACHE_TABLE_NAME}, key = "#code", sync = true)
//    @CacheExpire(ttl = 60, unit = TimeUnit.DAYS)
    public List<String> loadTables(String code) {
        if (StrUtil.isBlank(code))
            return null;

//        List<String> incomeTable = Lists.newArrayList("lhc.payment", "lhc.offline_pay","lhc.hera_trans_order");
//
//        List<String> settleTable = Lists.newArrayList("lhc.orders_box");
//        if ("income_bill".equals(code)) {
//            return incomeTable;
//        }else if ("bill_settle".equals(code)) {
//            return settleTable;
//        }else {
//            log.error("获取不到表列表, code:{}", code);
//            return null;
//        }

        RBucket<List<String>> tableBucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, CACHE_TABLE_NAME, code));
        List<String> tables = tableBucket.get();
        if (CollUtil.isNotEmpty(tables))
            return tables;

        log.info("开始加载业务域对应的采集表，code:{}", code);
        BizDomainBO bizDomainBO = bizDomainFacade.findByCode(code);
        if (Objects.isNull(bizDomainBO))
            return null;

        List<CollectionPipelineBO> collectionPipelineBOS = collectionPipelineFacade.listByDomainId(bizDomainBO.getId());
        tables = collectionPipelineBOS.stream().map(CollectionPipelineBO::getTableName).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tables))
            tableBucket.set(tables, 60, TimeUnit.DAYS);
        return tables;
    }

//    @CacheEvict(value = {CACHE_TABLE_NAME}, key = "#code")
//    public void evictTable(String code) {}

    public Long savePipeline(CollectionPipelineReq req) {
        if (Objects.isNull(req))
            return null;

        List<CollectionPipelineBO> pipelineBOS = collectionPipelineFacade.listByDomainId(req.getBizDomainId());
        if (CollUtil.isNotEmpty(pipelineBOS)) {
            for (CollectionPipelineBO pipelineBO : pipelineBOS) {
                if (pipelineBO.getStrategyCode().equals(req.getStrategyCode()))
                    throw BizException.create(BIZ_EXCEPTION, "策略编码已存在");

                if (pipelineBO.getTableName().equals(req.getTableName()))
                    throw BizException.create(BIZ_EXCEPTION, "采集的表名已存在");
            }
        }

        CollectionPipelineBO pipelineBO = CollectionPipelineBO.builder()
                .id(req.getId())
                .bizDomainId(req.getBizDomainId())
                .strategyCode(req.getStrategyCode())
                .collectionWay(CollectionWay.from(req.getCollectionWay()))
                .tableName(req.getTableName())
                .build();
        return collectionPipelineFacade.save(pipelineBO.toDO());
    }

    public List<String> loadTablesTest(String code) {
        if (StrUtil.isBlank(code))
            return null;

//        List<String> incomeTable = Lists.newArrayList("lhc.payment", "lhc.offline_pay","lhc.hera_trans_order");
//
//        List<String> settleTable = Lists.newArrayList("lhc.orders_box");
//        if ("income_bill".equals(code)) {
//            return incomeTable;
//        }else if ("bill_settle".equals(code)) {
//            return settleTable;
//        }else {
//            log.error("获取不到表列表, code:{}", code);
//            return null;
//        }

        RBucket<List<String>> tableBucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, CACHE_TABLE_NAME, code));
//        List<String> tables = tableBucket.get();
//        if (CollUtil.isNotEmpty(tables))
//            return tables;

        log.info("开始加载业务域对应的采集表，code:{}", code);
        BizDomainBO bizDomainBO = bizDomainFacade.findByCode(code);
        if (Objects.isNull(bizDomainBO))
            return null;

        List<CollectionPipelineBO> collectionPipelineBOS = collectionPipelineFacade.listByDomainId(bizDomainBO.getId());
        List<String> tables = collectionPipelineBOS.stream().map(CollectionPipelineBO::getTableName).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tables))
            tableBucket.set(tables, 60, TimeUnit.DAYS);
        return tables;
    }
}
