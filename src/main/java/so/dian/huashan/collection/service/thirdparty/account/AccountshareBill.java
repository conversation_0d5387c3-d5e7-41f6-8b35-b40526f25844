package so.dian.huashan.collection.service.thirdparty.account;

import lombok.Data;

@Data
public class AccountshareBill {
    /**
     * 分账日期
     */
    private String splitDate;
    /**
     * 分账发起方
     */
    private String splitInitiator;
    /**
     * 分账来源单号
     */
    private String splitSourceId;
    /**
     * 微信订单号
     */
    private String weixinOrderNo;
    /**
     * 分账单号
     */
    private String spiltOrderNo;
    /**
     * 分账明细单号
     */
    private String spiltDetailNo;
    /**
     * 商户分账单号
     */
    private String mchSplitOrderNo;
    /**
     * 订单金额
     */
    private String orderAmt;
    /**
     * 分账接收方
     */
    private String spiltReceiveId;
    /**
     * 分账金额
     */
    private String spiltAmt;
    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 分账状态
     */
    private String statusStr;
    /**
     * 分账描述
     */
    private String spiltDescription;
    /**
     * 备注
     */
    private String remark;

    private Long billFileId;
}
