package so.dian.huashan.collection.service.thirdparty;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.collection.mapper.WxSplitBillDetailMapper;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.collection.mapper.param.SplitBillParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@StepScope
public class AccountShareBillWriter  implements ItemWriter<WxSplitBillDetailDO> {

    @Resource
    private WxSplitBillDetailMapper wxSplitBillDetailMapper;

    private static final List<String> BIZ_TYPE_LIST = Arrays.asList("分出","分账回退");
    @Override
    public void write(List<? extends WxSplitBillDetailDO> list) throws Exception {
        List<String> settleDetailNoList = list.stream().map(WxSplitBillDetailDO::getSettleDetailNo).collect(Collectors.toList());
        Map<String, WxSplitBillDetailDO> splitBillDetailDOMap = getSplitBillDetailMap(settleDetailNoList);
        List<WxSplitBillDetailDO> insertList = new ArrayList<>();
        for (WxSplitBillDetailDO wxSplitBillDetailDO : list) {
            if (!BIZ_TYPE_LIST.contains(wxSplitBillDetailDO.getBizType())){
                continue;
            }
            if ("分账回退".equals(wxSplitBillDetailDO.getBizType())){
                wxSplitBillDetailDO.setSplitAmt(wxSplitBillDetailDO.getSplitAmt()*-1);
            }
            wxSplitBillDetailDO.setGmtUpdate(System.currentTimeMillis());
            if (Objects.nonNull(splitBillDetailDOMap.get(wxSplitBillDetailDO.getSettleDetailNo()))) {
                log.info("分账数据结算单号有重复数据 settleDetailNo:{}",wxSplitBillDetailDO.getSettleDetailNo());
                continue;
            }
            wxSplitBillDetailDO.setGmtCreate(System.currentTimeMillis());
            insertList.add(wxSplitBillDetailDO);
        }
        if (!CollectionUtils.isEmpty(insertList)){
            wxSplitBillDetailMapper.insertBatch(insertList);
        }
    }

    private Map<String, WxSplitBillDetailDO> getSplitBillDetailMap(List<String> settleDetailNoList) {
        Map<String, WxSplitBillDetailDO> splitBillDetailDOMap = new HashMap<>();
        if (CollectionUtils.isEmpty(settleDetailNoList)){
            return splitBillDetailDOMap;
        }
        SplitBillParam splitBillParam = new SplitBillParam();
        splitBillParam.setSettleDetailNoList(settleDetailNoList);
        List<WxSplitBillDetailDO> wxSplitBillDetailDOList = wxSplitBillDetailMapper.selectByParam(splitBillParam);
        if (!CollectionUtils.isEmpty(wxSplitBillDetailDOList)){
            wxSplitBillDetailDOList.forEach(k-> splitBillDetailDOMap.put(k.getSettleDetailNo(), k));
        }
        return splitBillDetailDOMap;
    }
}
