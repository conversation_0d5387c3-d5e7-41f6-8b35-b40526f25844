package so.dian.huashan.collection.service.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.stereotype.Component;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/11/15 11:05
 * @description:
 */
@Slf4j
@Component
public class StepExceptionListener implements StepExecutionListener {

    @Override
    public void beforeStep(StepExecution stepExecution) {


    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        if (!stepExecution.getExitStatus().getExitCode().equals(ExitStatus.COMPLETED.getExitCode())) {

            log.error("拉取三方账单批处理任务执行失败，任务名称：{}, 步骤名称:{}, 任务实例ID：{}, 执行实例ID：{}",
                    stepExecution.getJobExecution().getJobInstance().getJobName(),
                    stepExecution.getStepName(),
                    stepExecution.getJobExecution().getJobInstance().getInstanceId(),
                    stepExecution.getJobExecutionId());
            log.error("{}",stepExecution.getExitStatus().getExitDescription());
        }
        return stepExecution.getExitStatus();
    }
}
