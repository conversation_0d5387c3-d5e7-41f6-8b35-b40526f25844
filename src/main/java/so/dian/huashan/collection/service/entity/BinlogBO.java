package so.dian.huashan.collection.service.entity;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/13 15:42
 * @description:
 */
@Slf4j
@Data
public class BinlogBO {

    private String dbName;

    private String table;

    private String before;

    private String after;

    private String originalContent;

    public boolean verify() {
        if (StrUtil.isBlank(dbName)) {
            log.warn("完整性校验，库名为空，校验不通过");
            return Boolean.FALSE;
        }

        if (StrUtil.isBlank(table)) {
            log.warn("完整性校验，表名为空，校验不通过");
            return Boolean.FALSE;
        }

        if (StrUtil.isBlank(after)) {
            log.warn("完整性校验，内容为空，校验不通过");
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }
}
