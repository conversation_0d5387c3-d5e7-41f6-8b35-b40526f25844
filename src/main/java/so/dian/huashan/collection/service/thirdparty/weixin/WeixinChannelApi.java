package so.dian.huashan.collection.service.thirdparty.weixin;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.collection.service.thirdparty.AbstractChannelApi;
import so.dian.huashan.collection.service.thirdparty.BillRequestParam;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.enums.BizErrorCode;
import so.dian.huashan.common.utils.HutoolPostUtil;
import so.dian.huashan.common.utils.WxUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 微信渠道api
 *
 */
@Slf4j
@Service
public class WeixinChannelApi extends AbstractChannelApi {

    private static final String dateFormat = "yyyyMMdd";

    @Override
    public List<String> toPullBillFile(BillRequestParam requestParam) throws Exception {


        String billFilePath = getDestFilePath(requestParam);
        List<String> billFiles = Lists.newArrayList();

        Map<String, String> map = getParamsMap(requestParam);
        String requestXml = WxUtil.toXml(map);

        log.info("微信 requestXml = " + requestXml);
        long fileSize = HutoolPostUtil.postDownload(requestParam.getConfig().getRequestUrl(), requestXml, billFilePath, map.get("mch_id"));
        billFiles.add(billFilePath);

        log.info("微信获取账期文件完成，requestParam:{}, 文件路径:{}, 获取到的账单文件大小:{}", JSON.toJSONString(requestParam), billFilePath,
                 fileSize);

        return billFiles;
    }

    /**
     * 获得请求参数
     */
    private Map<String,String> getParamsMap(BillRequestParam requestParam){
        Map<String, String> paramMap = requestParam.getConfig().getParams();

        Map<String, String> map = new HashMap<>();
        Random r = new Random();
        String nonceStr = Long.toHexString(r.nextLong());

        map.put("appid", paramMap.get(ChannelConstant.WECHAT.APP_ID.getParam()));
        map.put("mch_id", paramMap.get(ChannelConstant.WECHAT.MCH_ID.getParam()));
        map.put("nonce_str", nonceStr);

        String billDate = DateUtil.format(requestParam.getBillDate(), dateFormat);

        map.put("bill_date", billDate);
        map.put("account_type", "Basic");
        try {
            map.put("sign", WxUtil.signSHA256(map, paramMap.get(ChannelConstant.WECHAT.SIGN_KEY.getParam())));
        } catch (Exception e) {
            throw BizException.create(BizErrorCode.ERROR_FOR_SHA256);
        }
        return map;
    }

}
