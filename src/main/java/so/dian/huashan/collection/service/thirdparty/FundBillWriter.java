package so.dian.huashan.collection.service.thirdparty;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.collection.enums.MchTypeEnum;
import so.dian.huashan.collection.mapper.WxFundAcctInfoMapper;
import so.dian.huashan.collection.mapper.WxFundBillDetailMapper;
import so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
@StepScope
public class FundBillWriter implements ItemWriter<WxFundBillDetailDO> {
    @Value("#{jobParameters[mchType]}")
    private String mchType;

    @Value("#{jobParameters[mchId]}")
    private String mchId;
    @Resource
    private WxFundAcctInfoMapper wxFundAcctInfoMapper;

    @Resource
    private WxFundBillDetailMapper wxFundBillDetailMapper;

    @Override
    public void write(List<? extends WxFundBillDetailDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<WxFundBillDetailDO> wxFundBillDetailDOList = (List<WxFundBillDetailDO>) list;
        if (MchTypeEnum.XIAODIAN.getCode().equals(Integer.valueOf(mchType))) {
            wxFundBillDetailDOList.forEach(k -> k.setMchId(mchId));
        }
        wxFundBillDetailMapper.insertBatch(wxFundBillDetailDOList);
    }
}
