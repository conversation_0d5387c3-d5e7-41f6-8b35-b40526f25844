package so.dian.huashan.collection.service.pipeline;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.facade.CollectionVoucherFacade;
import so.dian.huashan.collection.service.BizDomainService;
import so.dian.huashan.collection.service.CollectionPipelineService;
import so.dian.huashan.collection.service.CollectionVoucherService;
import so.dian.huashan.collection.service.entity.BizDomainBO;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.enums.CollectionVoucherStatus;

import java.util.List;
import java.util.Objects;

import static so.dian.huashan.common.exception.LogMarkerFactory.COLLECTION_DATA_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/12/13 10:18
 * @description:
 */
@Slf4j
@Component
public class PipelineExecutor {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private BizDomainService bizDomainService;

    @Autowired
    private CollectionPipelineService pipelineService;

    @Autowired
    private CollectionVoucherFacade voucherFacade;

    @Autowired
    private CollectionVoucherService voucherService;

    public void execute(List<Long> voucherIds) {
        if (CollUtil.isEmpty(voucherIds))
            return;

        for (Long voucherId : voucherIds) {
            log.debug("开始处理凭证，凭证ID:{}", voucherId);
            CollectionVoucherBO voucherBO = voucherFacade.find(voucherId);
            CollectionPipelineBO collectionPipelineBO = pipelineService.find(voucherBO.getVoucher().getTable());
            if (Objects.isNull(collectionPipelineBO)) {
                log.warn(COLLECTION_DATA_MARKER, "数据采集处理,没有获取到对应的管道注册信息,表名{}", voucherBO.getVoucher().getTable());
                return;
            }

            BizDomainBO bizDomainBO = bizDomainService.find(collectionPipelineBO.getBizDomainId());
            if (Objects.isNull(bizDomainBO)) {
                log.warn(COLLECTION_DATA_MARKER, "数据采集处理,没有获取到对应的业务域信息,表名{}", voucherBO.getVoucher().getTable());
                return;
            }

            try {
                AsyncTaskExecutor threadExecutor = applicationContext.getBean(bizDomainBO.getConfig().getCollectionThreadPool(), AsyncTaskExecutor.class);
                CollectionPipeline collectionPipeline = applicationContext.getBean(collectionPipelineBO.getStrategyCode(), CollectionPipeline.class);
                ExecutorCommand command = new ExecutorCommand(collectionPipeline);
                threadExecutor.submit(() -> command.execute(voucherBO));
            }catch (Exception e){
                log.error(COLLECTION_DATA_MARKER, "处理消息异常，voucherId={}", voucherBO.getId(), e);
                CollectionVoucherBO updateVoucherBO = CollectionVoucherBO.builder()
                        .id(voucherId)
                        .status(CollectionVoucherStatus.FAIL)
                        .remark(CollectionVoucherStatus.FAIL.getDesc())
                        .build();
                voucherFacade.update(updateVoucherBO);
            }
        }
    }

    private void doExecute(CollectionVoucherBO voucherBO, CollectionPipeline pipeline) {
        boolean seized = voucherService.seizeVoucher(voucherBO.getId(), voucherBO.getVersion(), CollectionVoucherStatus.IN_PROCESS);
        if (!seized)
            return;

        CollectionProcessResult process = null;
        try {
            PipelineBO pipelineBO = new PipelineBO(voucherBO.getVoucher(), voucherBO.getId());
            process = pipeline.process(pipelineBO);
        }catch (Exception e) {
            log.error(COLLECTION_DATA_MARKER, "管道处理失败, 凭证ID:{}", voucherBO.getId(), e);
            process = CollectionProcessResult.fail();
            throw e;
        }finally {
            if (Objects.nonNull(process) && process.getSuccess()) {
                voucherBO.setStatus(CollectionVoucherStatus.SUCCESS);
                voucherService.modify(voucherBO);
            }else {
                voucherBO.setStatus(CollectionVoucherStatus.FAIL);
                voucherService.modify(voucherBO);
            }
        }
    }
}
