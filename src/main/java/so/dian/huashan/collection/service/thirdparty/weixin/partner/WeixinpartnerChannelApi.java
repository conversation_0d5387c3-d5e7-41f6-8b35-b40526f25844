package so.dian.huashan.collection.service.thirdparty.weixin.partner;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.service.thirdparty.AbstractChannelApi;
import so.dian.huashan.collection.service.thirdparty.BillRequestParam;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.io.File;
import java.util.Date;
import java.util.List;
@Component
public class WeixinpartnerChannel<PERSON>pi extends AbstractChannelApi {

    @Autowired
    private WeixinpartnerBillApi weixinPartnerBillApi;

    protected String getDestFilePath(BillRequestParam requestParam) {
        return getDirectory(requestParam) +"/"
                + getFileName(requestParam.getConfig().getPayType(), requestParam.getBillDate(),requestParam.getConfig().getMchType());
    }

    protected String getFileName(ThirdpartyPayType payType, Date billDate,Integer mchType) {
        String billDateStr = LocalDateUtils.format(billDate, DatePattern.PURE_DATE_FORMAT);
        return payType.name().toLowerCase() + "_" +mchType+"_" + billDateStr + fileCsvExt;
    }

    @Override
    protected List<String> toPullBillFile(BillRequestParam requestParam) throws Exception {
        return weixinPartnerBillApi.downloadFundBill(requestParam.getBillDate(), getDestFilePath(requestParam),requestParam.getConfig().getMchType());

    }
}
