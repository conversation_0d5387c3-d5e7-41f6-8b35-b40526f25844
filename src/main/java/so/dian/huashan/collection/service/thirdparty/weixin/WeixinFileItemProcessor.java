package so.dian.huashan.collection.service.thirdparty.weixin;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.himalaya.able.EnumInterface;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalEnumUtils;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.enums.BillResource;
import so.dian.huashan.common.enums.BizErrorCode;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.enums.TradeType;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 微信账务账单文件处理器
 */
@Slf4j
@Service
@StepScope
public class WeixinFileItemProcessor implements ItemProcessor<WeixinBill, ThirdPartyBillDO> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    private static final String refundRemark = "退款总金额";

    private static final String refundRemarkYuan = "元";

    private static final String BAOFU_MCH_ID_REAL = "**********";
    private static final String BAOFU_MCH_ID_DEV = "**********";

    private static final String WEIMEI_MCH_ID_REAL = "**********";
    private static final String WEIMEI_MCH_ID_DEV = "**********";

    @Override
    public ThirdPartyBillDO process(@NonNull WeixinBill weixinAccountBill) {

        String mchId = getMchId();
        ThirdPartyBillDO billDO = new ThirdPartyBillDO();
        try {
            billDO.init();
            billDO.setFileId(weixinAccountBill.getBillFileId());
            billDO.setBizNo1(weixinAccountBill.getBizVoucherNo());
            billDO.setBizNo2(weixinAccountBill.getBizNo());
            billDO.setMchId(mchId);

            if(BAOFU_MCH_ID_REAL.equals(mchId) || BAOFU_MCH_ID_DEV.equals(mchId)){
                billDO.setBillSource(BillResource.Baofu.getKey());
            }else if(WEIMEI_MCH_ID_REAL.equals(mchId) || WEIMEI_MCH_ID_DEV.equals(mchId)) {
                billDO.setBillSource(BillResource.Weimei.getKey());
            }else {
                billDO.setBillSource(BillResource.Wechat.getKey());

            }
            billDO.setBillTime(DateUtil.parseDateTime(weixinAccountBill.getAccountTime()));
            billDO.setPayNo(weixinAccountBill.getSeqNo());
            billDO.setTradeType(getTradeType(weixinAccountBill.getTradeType()));
            billDO.setBizType(getBizType(weixinAccountBill.getBizType()));

            /*
             * 微信退款业务类型退款金额设为未扣除手续费金额
             */
            if(Objects.equals(BizTypeEnum.REFUND.getCode(),billDO.getBizType())){
                String remark = weixinAccountBill.getRemark();
                if(StringUtils.isEmpty(remark)){
                    billDO.setTradeAmount(multiply100(new BigDecimal(weixinAccountBill.getTradeAmount())).intValue());
                }else {
                    String remarkReplace = remark.replace(refundRemark,"");
                    if(remarkReplace.contains(refundRemarkYuan)){
                        String refundAmount = remarkReplace.substring(0,remarkReplace.indexOf(refundRemarkYuan));
                        billDO.setTradeAmount(multiply100(new BigDecimal(refundAmount)).intValue());
                    }else{
                        billDO.setTradeAmount(multiply100(new BigDecimal(weixinAccountBill.getTradeAmount())).intValue());
                    }
                }

            }else{
                billDO.setTradeAmount(multiply100(new BigDecimal(weixinAccountBill.getTradeAmount())).intValue());
            }
            billDO.setRemark(weixinAccountBill.getRemark());
        } catch (Exception e) {
            log.error("微信账务账单文件映射持久化对象时出现异常，weixinAccountBill:{}", weixinAccountBill, e);
            throw e;
        }

        return billDO;
    }

    /**
     * 获取交易类型（收入/支出）
     */
    private String getTradeType(String tradeType) {
        TradeType tradeTypeEnum = LocalEnumUtils.findByDesc(TradeType.class,tradeType);
        if(Objects.nonNull(tradeTypeEnum)){
            return tradeTypeEnum.getKey();
        }else {
            log.error("未知的微信交易类型tradeType = {}",tradeType);
            return ChannelConstant.UNKNOWN_WEIXIN_TRADE_TYPE;
        }
    }

    /**
     * 金额转为分
     */
    private BigDecimal multiply100(BigDecimal amount) {
        if(amount != null) {
            return amount.multiply(new BigDecimal("100"));
        }
        return null;
    }

    private Integer getBizType(String bizType) {

        BizTypeEnum bizTypeEnum = LocalEnumUtils.findByDesc(BizTypeEnum.class,bizType);
        if(Objects.nonNull(bizTypeEnum)){
            return bizTypeEnum.getCode();
        }else {
            log.error("未知的微信业务类型bizType = {}",bizType);
            return Integer.valueOf(BizErrorCode.UNKNOWN_BIZ_TYPE.getCode());
        }

    }

    private String getMchId() {
        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (Objects.isNull(payType)) {
            throw BizException.create(BizErrorCode.UNKNOWN_ENUM);
        }

        String mchId = StringUtils.EMPTY;
        ApiConfig config = ApiConfig.getConfig(payType);
        for (String key : config.getParams().keySet()) {
            if (ChannelConstant.WECHAT.MCH_ID.getParam().equals(key)) {
                mchId = config.getParams().get(key);
                break;
            }
        }
        return mchId;
    }

    @Getter
    @AllArgsConstructor
    public enum BizTypeEnum implements EnumInterface<BizTypeEnum> {
        DEAL(1, "交易"),
        REFUND(2, "退款"),
        FEE(3, "扣除交易手续费"),
        CHANGE(4, "企业付款到零钱"),
        WITHDRAWAL(5, "提现"),
        ;

        private final Integer code;
        private final String desc;

        @Override
        public BizTypeEnum getDefault() {
            return null;
        }
    }


}
