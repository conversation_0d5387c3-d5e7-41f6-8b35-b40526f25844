package so.dian.huashan.collection.service.thirdparty.alipay;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.MultiResourceItemReader;
import org.springframework.batch.item.support.SynchronizedItemStreamReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.service.entity.ThirdPartyBillFileBO;
import so.dian.huashan.collection.service.thirdparty.ChannelBillApiService;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.enums.ThirdpartyChannel;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: miaoshuai
 * @create: 2023/03/24 14:07
 * @description:
 * 支付宝账单文件读取器，支持多文件解析，记录表头和表尾信息。
 * 继承自 SynchronizedItemStreamReader 以支持线程安全操作。
 */
@Slf4j
@Service
@StepScope
public class AlipayBillFileReader extends SynchronizedItemStreamReader<AlipayBill> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    @Value("#{jobParameters[billDate]}")
    private String billDate;

    @Autowired
    @Qualifier("channelBillApiService")
    private ChannelBillApiService billApiService;

    private boolean isLoadFile;

    private final Map<String, ThirdPartyBillFileBO> pathAdFile = new HashMap<>();

    private final MultiResourceItemReader<AlipayBill> multiResourceReader = new MultiResourceItemReader<>();

    @Override
    public synchronized AlipayBill read() throws Exception {
        if (!isLoadFile) {
            log.warn("支付宝账单文件没有加载");
            return null;
        }

        AlipayBill alipayBill = super.read();
        if (!Objects.isNull(alipayBill)) {
            String filename = Optional.ofNullable(multiResourceReader.getCurrentResource()).map(Resource::getFilename).orElse(null);
            alipayBill.setBillFileId(pathAdFile.get(filename).getId());
        }
        return alipayBill;
    }

    @Override
    public void afterPropertiesSet() {

        FlatFileItemReader<AlipayBill> flatFileItemReader = new FlatFileItemReader<>();
        multiResourceReader.setResources(new Resource[0]);
        multiResourceReader.setDelegate(flatFileItemReader);
        setDelegate(multiResourceReader);

        // 支付宝的不能去掉
        flatFileItemReader.setEncoding(ChannelConstant.gbkCharset);
        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (!Objects.isNull(payType) && ThirdpartyChannel.ALIPAY.equals(payType.getChannel())) {

            AlipayBillFieldSetMapper alipayBillMapper = new AlipayBillFieldSetMapper();
            flatFileItemReader.setLineMapper(alipayBillMapper);

            Integer billDateInt = Integer.valueOf(DateUtil.format(DateUtil.parseDate(billDate), DatePattern.PURE_DATE_PATTERN));
            List<ThirdPartyBillFileBO> billFiles = billApiService.listBillFile(payType, billDateInt);
            if (CollectionUtils.isNotEmpty(billFiles)) {
                List<Resource> resources = Lists.newArrayList();
                for (ThirdPartyBillFileBO billFile : billFiles) {
                    //不处理汇总文件，只加载了明细
                    if (billFile.getFilePath().contains("total")) {
                        continue;
                    }
                    Resource resource = new FileSystemResource(billFile.getFilePath());
                    resources.add(resource);
                    pathAdFile.put(resource.getFilename(), billFile);
                }
                multiResourceReader.setResources(resources.toArray(new Resource[]{}));
                isLoadFile = true;
                log.info("支付宝账单文件加载完成");
            }
        }
    }
}
