package so.dian.huashan.collection.service.thirdparty.weixin.partner;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.himalaya.able.EnumInterface;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalEnumUtils;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;
import so.dian.huashan.collection.mapper.dos.WxFundBillDetailDO;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.collection.service.thirdparty.weixin.WeixinBill;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.enums.BillResource;
import so.dian.huashan.common.enums.BizErrorCode;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.enums.TradeType;
import so.dian.huashan.common.utils.DateUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 微信账务账单文件处理器
 */
@Slf4j
@Service
@StepScope
public class WeixinpartnerFileItemProcessor implements ItemProcessor<WeixinpartnerBill, WxFundBillDetailDO> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;
    @Value("#{jobParameters[mchType]}")
    private String mchType;

    private static final String refundRemark = "退款总金额";

    private static final String refundRemarkYuan = "元";

    private static final String BAOFU_MCH_ID_REAL = "**********";
    private static final String BAOFU_MCH_ID_DEV = "**********";

    private static final String WEIMEI_MCH_ID_REAL = "**********";
    private static final String WEIMEI_MCH_ID_DEV = "**********";

    @Override
    public WxFundBillDetailDO process(@NonNull WeixinpartnerBill weixinAccountBill) {
        try {
            WxFundBillDetailDO wxFundBillDetailDO = new WxFundBillDetailDO();
            wxFundBillDetailDO.setBillDate(DateUtil.parseDateYyyyMMdd2IntThrowException(DateUtil.parseLongDateString(weixinAccountBill.getAccountTime())));
            wxFundBillDetailDO.setMchType(Integer.valueOf(mchType));
            wxFundBillDetailDO.setMchId(weixinAccountBill.getMchId());
            wxFundBillDetailDO.setPayBizNo(weixinAccountBill.getBizNo());
            wxFundBillDetailDO.setFundNo(weixinAccountBill.getSeqNo());
            wxFundBillDetailDO.setBizName(weixinAccountBill.getBizName());
            wxFundBillDetailDO.setBizType(weixinAccountBill.getBizType());
            wxFundBillDetailDO.setIncomeType(getTradeType(weixinAccountBill.getTradeType()));
            BigDecimal bigDecimal = new BigDecimal(weixinAccountBill.getTradeAmount()).multiply(new BigDecimal(100));
            wxFundBillDetailDO.setIncomeAmt(bigDecimal.longValue());
            BigDecimal acctBalance = new BigDecimal(weixinAccountBill.getBalance()).multiply(new BigDecimal(100));
            wxFundBillDetailDO.setAcctBalance(acctBalance.longValue());
            wxFundBillDetailDO.setChangeInitiator(weixinAccountBill.getApplicantName());
            wxFundBillDetailDO.setBizVoucherNo(weixinAccountBill.getBizVoucherNo());
            wxFundBillDetailDO.setRemark(weixinAccountBill.getRemark());
            wxFundBillDetailDO.setDeleted(0);
            return wxFundBillDetailDO;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取交易类型（收入/支出）
     */
    private String getTradeType(String tradeType) {
        TradeType tradeTypeEnum = LocalEnumUtils.findByDesc(TradeType.class,tradeType);
        if(Objects.nonNull(tradeTypeEnum)){
            return tradeTypeEnum.getKey();
        }else {
            log.error("未知的微信交易类型tradeType = {}",tradeType);
            return ChannelConstant.UNKNOWN_WEIXIN_TRADE_TYPE;
        }
    }

    /**
     * 金额转为分
     */
    private BigDecimal multiply100(BigDecimal amount) {
        if(amount != null) {
            return amount.multiply(new BigDecimal("100"));
        }
        return null;
    }

    private Integer getBizType(String bizType) {

        BizTypeEnum bizTypeEnum = LocalEnumUtils.findByDesc(BizTypeEnum.class,bizType);
        if(Objects.nonNull(bizTypeEnum)){
            return bizTypeEnum.getCode();
        }else {
            log.error("未知的微信业务类型bizType = {}",bizType);
            return Integer.valueOf(BizErrorCode.UNKNOWN_BIZ_TYPE.getCode());
        }

    }

    private String getMchId() {
        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (Objects.isNull(payType)) {
            throw BizException.create(BizErrorCode.UNKNOWN_ENUM);
        }

        String mchId = StringUtils.EMPTY;
        ApiConfig config = ApiConfig.getConfig(payType);
        for (String key : config.getParams().keySet()) {
            if (ChannelConstant.WECHAT.MCH_ID.getParam().equals(key)) {
                mchId = config.getParams().get(key);
                break;
            }
        }
        return mchId;
    }

    @Getter
    @AllArgsConstructor
    public enum BizTypeEnum implements EnumInterface<BizTypeEnum> {
        DEAL(1, "交易"),
        REFUND(2, "退款"),
        FEE(3, "扣除交易手续费"),
        CHANGE(4, "企业付款到零钱"),
        WITHDRAWAL(5, "提现"),
        ;

        private final Integer code;
        private final String desc;

        @Override
        public BizTypeEnum getDefault() {
            return null;
        }
    }


}
