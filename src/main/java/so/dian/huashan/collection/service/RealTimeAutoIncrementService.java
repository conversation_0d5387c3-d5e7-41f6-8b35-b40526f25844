//package so.dian.huashan.collection.service;
//
//import cn.hutool.core.util.StrUtil;
//import com.alibaba.fastjson.JSON;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import so.dian.huashan.collection.mapper.CollectionVoucherMapper;
//import so.dian.huashan.collection.mapper.dos.CollectionVoucherDO;
//import so.dian.huashan.collection.service.entity.BinlogBO;
//import so.dian.huashan.collection.service.entity.CollectionProcessResult;
//import so.dian.huashan.collection.service.entity.PipelineBO;
//import so.dian.huashan.collection.service.pipeline.PipelineExecutor;
//import so.dian.huashan.common.enums.CollectionVoucherStatus;
//
//import java.util.Date;
//import java.util.concurrent.Future;
//
///**
// * @author: mi<PERSON><PERSON><PERSON>
// * @create: 2023/03/14 16:52
// * @description:
// */
//@Service
//public class RealTimeAutoIncrementService {
//
//    @Autowired
//    private CollectionVoucherMapper collectionVoucherMapper;
//
//    @Autowired
//    private PipelineExecutor pipelineExecutor;
//
//    public Future<CollectionProcessResult> execute(String binlog) {
//        if (StrUtil.isBlank(binlog))
//            return CollectionProcessResult.success().FutureImmediately();
//
//        BinlogBO binlogBO = JSON.parseObject(binlog, BinlogBO.class);
//        Long voucherId = saveVoucher(binlogBO.getDbName(), binlogBO.getTable(), binlog);
//
//        PipelineBO pipelineBO = new PipelineBO(binlogBO);
//        pipelineBO.setVoucherId(voucherId);
//        return pipelineExecutor.execute(pipelineBO);
//    }
//
//    private Long saveVoucher(String dbName, String tableName, String binlog) {
//
//        CollectionVoucherDO voucherDO = new CollectionVoucherDO();
//        voucherDO.setStatus(CollectionVoucherStatus.INITIAL.getCode());
//        voucherDO.setSource(dbName + "#" + tableName);
//        voucherDO.setRetryTime(0);
//        voucherDO.setVoucher(binlog);
//        voucherDO.setReceiveTime(new Date());
//        voucherDO.setRemark(CollectionVoucherStatus.INITIAL.getDesc());
//        voucherDO.init();
//        collectionVoucherMapper.insertSelective(voucherDO);
//
//        return voucherDO.getId();
//    }
//}
