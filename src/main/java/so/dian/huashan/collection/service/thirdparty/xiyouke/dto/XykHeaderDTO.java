package so.dian.huashan.collection.service.thirdparty.xiyouke.dto;

import lombok.Data;
import so.dian.huashan.common.utils.DateUtil;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Date;
import java.util.Map;

@Data
public class XykHeaderDTO {
    private static char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};

    private String company_no;

    private String sign;

    private String sign_time;

    public static XykHeaderDTO create (Map<String, String> extraMap,String partnerOrderNo) {
        XykHeaderDTO xykHeaderDTO = new XykHeaderDTO();
        xykHeaderDTO.setCompany_no(extraMap.get("company-no"));
        xykHeaderDTO.setSign_time(DateUtil.format(new Date(), DateUtil.MAIL_DATE_FORMAT));
        xykHeaderDTO.setSign(xykHeaderDTO.toSign(extraMap.get("secret-key"), partnerOrderNo));
        return xykHeaderDTO;
    }

    private String toSign(String securityKey, String partnerOrderNo) {
        String sign = String.format("%s|%s|%s|%s", company_no, sign_time, securityKey, partnerOrderNo);
        return MD5Encrpytion(sign.getBytes(StandardCharsets.UTF_8));
    }
    private String MD5Encrpytion(byte[] source) {
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("MD5");
            mdTemp.update(source);
            byte[] md = mdTemp.digest();
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; ++i) {
                byte byte0 = md[i];
                str[(k++)] = hexDigits[(byte0 >>> 4 & 0xF)];
                str[(k++)] = hexDigits[(byte0 & 0xF)];
            }
            for (int m = 0; m < str.length; ++m) {
                if ((str[m] >= 'a') && (str[m] <= 'z')) {
                    str[m] = (char) (str[m] - ' ');
                }
            }
            return new String(str);
        } catch (Exception e) {
            throw new RuntimeException("僖游客signMd5加密失败");
        }
    }

}
