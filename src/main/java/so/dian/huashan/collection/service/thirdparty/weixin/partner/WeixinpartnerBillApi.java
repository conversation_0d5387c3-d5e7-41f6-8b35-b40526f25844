
package so.dian.huashan.collection.service.thirdparty.weixin.partner;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.http.AbstractHttpClient;
import com.wechat.pay.java.core.util.IOUtil;
import com.wechat.pay.java.service.billdownload.BillDownloadService;
import com.wechat.pay.java.service.billdownload.model.AccountType;
import com.wechat.pay.java.service.billdownload.model.Algorithm;
import com.wechat.pay.java.service.billdownload.model.EncryptBillEntity;
import com.wechat.pay.java.service.billdownload.model.GetAllSubMchFundFlowBillRequest;
import com.wechat.pay.java.service.billdownload.model.GetFundFlowBillRequest;
import com.wechat.pay.java.service.billdownload.model.QueryBillEntity;
import com.wechat.pay.java.service.billdownload.model.QueryEncryptBillEntity;
import com.wechat.pay.java.service.billdownload.model.TarType;
import com.wechat.pay.java.service.profitsharing.ProfitsharingService;
import com.wechat.pay.java.service.profitsharing.model.SplitBillRequest;
import com.wechat.pay.java.service.profitsharing.model.SplitBillResponse;
import com.wechat.pay.java.service.profitsharing.model.SplitBillTarType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.check.emuns.CallLogBizType;
import so.dian.huashan.collection.enums.MchTypeEnum;
import so.dian.huashan.collection.manager.ThirdCallLogManager;
import so.dian.huashan.collection.mapper.dos.ThirdCallLogDO;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.common.utils.ZipUtil;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;

@Slf4j
@Component
public class WeixinpartnerBillApi {

    private static final String fileCsvExt = ".csv";

    static final int TAG_LENGTH_BIT = 128;

    @Autowired
    private ProfitsharingService profitsharingService;

    @Autowired
    private AbstractHttpClient httpClient;

    @Autowired
    private BillDownloadService downloadService;
    @Autowired
    private ThirdCallLogManager thirdCallLogManager;

    /**
     * 下载分账账单
     * @param billDate      账单日期
     * @param directoryPath 账单存储的文件夹路径
     */
    public String downloadSplitBill(Date billDate, String directoryPath) throws Exception {
        SplitBillRequest request = new SplitBillRequest();
        request.setBillDate(DateUtil.format(billDate, NORM_DATE_PATTERN));
        request.setTarType(SplitBillTarType.GZIP);
        ThirdCallLogDO thirdCallLogDO = initThirdCallLogDO(JSON.toJSONString(request), CallLogBizType.FUNDS_ACCOUNT_SHARING.getCode());
        SplitBillResponse splitBillResponse = null;
        try {
            splitBillResponse = profitsharingService.splitBill(request);
            thirdCallLogDO.setHttpStatusCode(200);
        } catch (ServiceException serviceException) {
            log.warn("分账账单下载失败 cause:", serviceException);
            thirdCallLogDO.setHttpStatusCode(serviceException.getHttpStatusCode());
            thirdCallLogDO.setFailReason(serviceException.getErrorMessage());
            thirdCallLogDO.setThirdErrorCode(serviceException.getErrorCode());
            thirdCallLogManager.insert(thirdCallLogDO);
            if ("NO_STATEMENT_EXIST".equals(serviceException.getErrorCode())) {
                return null;
            }
            throw new RuntimeException(serviceException.getErrorMessage());
        }
        InputStream is = httpClient.download(splitBillResponse.getDownloadUrl());

        String unzipFilePath = directoryPath + "split_bill" + fileCsvExt;
        ZipUtil.unGzip(is, unzipFilePath);
        // 添加日志
        thirdCallLogManager.insert(thirdCallLogDO);
        return unzipFilePath;
    }

    private ThirdCallLogDO initThirdCallLogDO(String requestJson, Integer bizType) {
        ThirdCallLogDO thirdCallLogDO = new ThirdCallLogDO();
        thirdCallLogDO.setThirdCallTime(System.currentTimeMillis());
        thirdCallLogDO.setRequestJson(requestJson);
        thirdCallLogDO.setBizType(bizType);
        thirdCallLogDO.setCallDate(DateUtil.parseDateYyyyMMdd2Int(new Date()));
        thirdCallLogDO.setDeleted(0);
        thirdCallLogDO.setGmtCreate(System.currentTimeMillis());
        thirdCallLogDO.setGmtUpdate(System.currentTimeMillis());
        return thirdCallLogDO;
    }

    /**
     * 下载资金账单
     *
     * @param billDate      账单日期
     * @param directoryPath 账单存储的文件夹路径
     */
    public List<String> downloadFundBill(Date billDate, String directoryPath, Integer mchType) throws Exception {
        List<String> billFilePaths = new ArrayList<>();
        if (MchTypeEnum.XIAODIAN.getCode().equals(mchType)) {
            String xdBillFilePath = downloadXdFundBill(billDate, directoryPath);
            if (StringUtils.isNotBlank(xdBillFilePath)) {
                billFilePaths.add(xdBillFilePath);
            }
        } else {
            billFilePaths = downloadSubFundBill(billDate, directoryPath);
        }
        return billFilePaths;
    }

    /**
     * 下载二级商户资金账单
     *
     * @param billDate      账单日期
     * @param directoryPath 账单存储的文件夹路径
     */
    private List<String> downloadSubFundBill(Date billDate, String directoryPath) throws Exception {
        GetAllSubMchFundFlowBillRequest request = new GetAllSubMchFundFlowBillRequest();
        request.setBillDate(DateUtil.format(billDate, NORM_DATE_PATTERN));
        request.setAccountType(AccountType.ALL);
        request.setAlgorithm(Algorithm.AEAD_AES_256_GCM);
        request.setTarType(TarType.GZIP);

        List<String> billFilePaths = Lists.newArrayList();
        QueryEncryptBillEntity fundFlowBill = null;
        ThirdCallLogDO thirdCallLogDO = initThirdCallLogDO(JSON.toJSONString(request), CallLogBizType.FUNDS_MERCHANT.getCode());
        try {
            fundFlowBill = downloadService.getAllSubMchFundFlowBill(request);
            thirdCallLogDO.setHttpStatusCode(200);
            log.info("商户账单下载成功，request:{}, response:{}", JSON.toJSONString(request), JSON.toJSONString(fundFlowBill));
        } catch (ServiceException serviceException) {
            log.warn("商户账单下载失败 cause:", serviceException);
            thirdCallLogDO.setHttpStatusCode(serviceException.getHttpStatusCode());
            thirdCallLogDO.setFailReason(serviceException.getErrorMessage());
            thirdCallLogDO.setThirdErrorCode(serviceException.getErrorCode());
            thirdCallLogManager.insert(thirdCallLogDO);
            if ("NO_STATEMENT_EXIST".equals(serviceException.getErrorCode())) {
                return new ArrayList<>();
            }
            throw new RuntimeException(serviceException.getErrorMessage());
        }
        for (EncryptBillEntity entity : fundFlowBill.getDownloadBillList()) {
            InputStream inputStream = httpClient.download(entity.getDownloadUrl());
            InputStream decryptIs = decrypt(entity.getEncryptKey(), entity.getNonce(), inputStream);
            String unzipFilePath = directoryPath + "sub_fund_bill_" + entity.getBillSequence() + fileCsvExt;
            ZipUtil.unGzip(decryptIs, unzipFilePath);
            billFilePaths.add(unzipFilePath);
        }
        // 添加日志
        thirdCallLogManager.insert(thirdCallLogDO);
        return billFilePaths;
    }

    /**
     * 下载小电资金账单
     * * @param billDate      账单日期
     * @param directoryPath 账单存储的文件夹路径
     */
    private String downloadXdFundBill(Date billDate, String directoryPath) throws Exception {
        GetFundFlowBillRequest request = new GetFundFlowBillRequest();
        request.setBillDate(DateUtil.format(billDate, NORM_DATE_PATTERN));
        request.setTarType(TarType.GZIP);
        QueryBillEntity fundFlowBill;
        ThirdCallLogDO thirdCallLogDO = initThirdCallLogDO(JSON.toJSONString(request), CallLogBizType.FUNDS_XIAODIAN.getCode());
        try {
            fundFlowBill = downloadService.getFundFlowBill(request);
            thirdCallLogDO.setHttpStatusCode(200);
            log.info("小电账单下载成功，request:{}, response:{}", JSON.toJSONString(request), JSON.toJSONString(fundFlowBill));
        } catch (ServiceException serviceException) {
            log.warn("小电账单下载失败 cause:", serviceException);
            thirdCallLogDO.setHttpStatusCode(serviceException.getHttpStatusCode());
            thirdCallLogDO.setFailReason(serviceException.getErrorMessage());
            thirdCallLogDO.setThirdErrorCode(serviceException.getErrorCode());
            thirdCallLogManager.insert(thirdCallLogDO);
            if ("NO_STATEMENT_EXIST".equals(serviceException.getErrorCode())) {
                return null;
            }
            throw new RuntimeException(serviceException.getErrorMessage());
        }
        InputStream inputStream = httpClient.download(fundFlowBill.getDownloadUrl());
        String unzipFilePath = directoryPath + "xd_fund_bill" + fileCsvExt;
        ZipUtil.unGzip(inputStream, unzipFilePath);
        // 添加日志
        thirdCallLogManager.insert(thirdCallLogDO);
        return unzipFilePath;
    }

    private InputStream decrypt(String aesKey, String nonce, InputStream zipContent)
            throws GeneralSecurityException, IOException {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");

            SecretKeySpec key = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");
            GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, nonce.getBytes(StandardCharsets.UTF_8));

            cipher.init(Cipher.DECRYPT_MODE, key, spec);

            return IoUtil.toStream(cipher.doFinal(IOUtil.toByteArray(zipContent)));
        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            throw new IllegalStateException(e);
        } catch (InvalidKeyException | InvalidAlgorithmParameterException e) {
            throw new IllegalArgumentException(e);
        }
    }

}