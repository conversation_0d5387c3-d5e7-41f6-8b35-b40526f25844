package so.dian.huashan.collection.service.pipeline;

import lombok.extern.slf4j.Slf4j;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;

import static so.dian.huashan.common.enums.BizErrorCodeEnum.PIPELINE_PROCESS_FAIL;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/13 14:11
 * @description: 自动增量采集
 */
@Slf4j
public abstract class CollectionPipeline {

    public CollectionProcessResult process(PipelineBO pipelineBO) {

        Long voucherId = pipelineBO.getVoucherId();
        if (isSkip(pipelineBO)) {
            return CollectionProcessResult.success().voucherId(voucherId);
        }

        // 数据有效性判断
        if (!isValid(pipelineBO)) {
            // 抛异常
            throw BizException.create(PIPELINE_PROCESS_FAIL);
        }

        return doProcess(pipelineBO);
    }

    protected abstract boolean isSkip(PipelineBO pipelineBO);

    protected boolean isValid(PipelineBO pipelineBO) {
        return Boolean.TRUE;
    }

    protected abstract CollectionProcessResult doProcess(PipelineBO pipelineBO);


}
