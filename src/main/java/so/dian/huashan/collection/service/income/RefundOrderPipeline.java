package so.dian.huashan.collection.service.income;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.enums.BillSourceEnum;
import so.dian.huashan.common.mapper.lhc.PaymentMapper;
import so.dian.huashan.common.mapper.lhc.entity.PaymentDO;
import so.dian.huashan.common.mapper.lhc.entity.RefundOrderPaymentChannelMapRelationDO;

import javax.annotation.Resource;

/**
 * @author: miaoshu<PERSON>
 * @create: 2023/03/21 15:18
 * @description:
 */
@Slf4j
//@Service
public class RefundOrderPipeline extends CollectionPipeline {

    @Resource
    private IncomeOriginalInfoMapper incomeOriginalInfoMapper;

    @Resource
    private PaymentMapper paymentMapper;

    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        if(StringUtils.isBlank(pipelineBO.getBinLog().getAfter())){
            return true;
        }
        JSONObject data = JSON.parseObject(pipelineBO.getBinLog().getAfter());
        String status = data.getString("refund_status");
//        String id = data.getString("refund_order_no");
//        RefundOrderDO refundOrderBO = refundOrderMapper.getById(Long.parseLong(id));
        if(status.equals("2")){
            return false;
        }
        return true;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        RefundOrderPaymentChannelMapRelationDO refundOrderPaymentChannelMapRelationDO = JSON.parseObject(pipelineBO.getBinLog().getAfter(), RefundOrderPaymentChannelMapRelationDO.class);
        // 根据bizOrderNo + refundType 查询 payment表
        PaymentDO paymentDO = paymentMapper.selectByOrderNoAndPayType(refundOrderPaymentChannelMapRelationDO.getBizOrderNo(), Integer.valueOf(refundOrderPaymentChannelMapRelationDO.getRefundType()));
        IncomeOriginalInfoDO incomeOriginalInfoDO = new IncomeOriginalInfoDO();
        incomeOriginalInfoDO.setDataSource(3);
        incomeOriginalInfoDO.setPayType(paymentDO.getPayType());
        incomeOriginalInfoDO.setBizType(paymentDO.getBizType());
        Integer happenDate = paymentDO.getRefundTime() != null ? Integer.valueOf(LocalDateUtils.format(paymentDO.getRefundTime(), DatePattern.PURE_DATE_PATTERN)) : Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
        incomeOriginalInfoDO.setHappenDate(happenDate);
        if(StringUtils.isBlank(paymentDO.getPayNo())){
//            log.warn("支付单号为空，不处理这样的数据,id = {}",id);
            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId());
        }
        incomeOriginalInfoDO.setPayNo(paymentDO.getPayNo());
        if(paymentDO.getPayType().equals(16) && paymentDO.getPayTypeRoute().equals(1)){
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.BAOFU.getId());
        }else if(paymentDO.getPayType().equals(16) && paymentDO.getPayTypeRoute().equals(2)){
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.WEIMEI.getId());
        }else{
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.getByPayType(paymentDO.getPayType()).getId());
        }
        incomeOriginalInfoDO.setStatus(1);
        incomeOriginalInfoDO.setTradeType("out");
        incomeOriginalInfoDO.setTradeAmount(refundOrderPaymentChannelMapRelationDO.getRefundAmount().intValue());
        incomeOriginalInfoDO.setTradeTime(paymentDO.getRefundTime());

        // dataSource + bizType + payNo + tradeType + payWay
        String key  = incomeOriginalInfoDO.getDataSource() + "_" +incomeOriginalInfoDO.getBizType() + "_" + refundOrderPaymentChannelMapRelationDO.getRefundOrderNo() + "_" + incomeOriginalInfoDO.getTradeType() + "_" + incomeOriginalInfoDO.getPayWay();
        incomeOriginalInfoDO.setIdempotentNo(key);
        incomeOriginalInfoDO.setVoucherId(pipelineBO.getVoucherId());
        IncomeOriginalInfoDO incomeOriginalInfoDO1 = incomeOriginalInfoMapper.selectByIdempotentNo(key);
        if(incomeOriginalInfoDO1 == null){
            incomeOriginalInfoDO.init();
            incomeOriginalInfoMapper.insertSelective(incomeOriginalInfoDO);
        }else{
            incomeOriginalInfoDO.setId(incomeOriginalInfoDO1.getId());
            incomeOriginalInfoDO.setGmtUpdate(System.currentTimeMillis());
            incomeOriginalInfoMapper.updateByPrimaryKeySelective(incomeOriginalInfoDO);
        }

        return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(incomeOriginalInfoDO.getId());
    }
}
