package so.dian.huashan.collection.service.thirdparty.alipay;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.himalaya.able.EnumInterface;
import so.dian.himalaya.util.LocalEnumUtils;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.common.enums.BillResource;
import so.dian.huashan.common.enums.BizErrorCode;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.enums.TradeType;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

import static so.dian.huashan.common.constant.CommonConstants.MCH_ID_NAME;
import static so.dian.huashan.common.constant.CommonConstants.P_ID_NAME;

/**
 * 支付宝账务账单文件处理器
 *
 */
@Slf4j
@Service
@StepScope
public class AlipayFileItemProcessor implements ItemProcessor<AlipayBill, ThirdPartyBillDO> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    @Override
    public ThirdPartyBillDO process(@NonNull AlipayBill alipayAccountBill) {
        ThirdPartyBillDO billDO = new ThirdPartyBillDO();

        try {
            billDO.init();
            billDO.setFileId(alipayAccountBill.getBillFileId());
            billDO.setBillSource(BillResource.Alipay.getKey());
            billDO.setBizNo1(alipayAccountBill.getMerchantNo());
            billDO.setBizNo2(alipayAccountBill.getAccountSeqNo());
            billDO.setMchId(getMchId(ThirdpartyPayType.fromName(payTypeName)));
            if(StringUtils.isNotBlank(alipayAccountBill.getOccurTime())){
                billDO.setBillTime(DateUtil.parseDateTime(alipayAccountBill.getOccurTime()));
            }
            billDO.setPayNo(alipayAccountBill.getBizSeqNo());
            if(StringUtils.isNotBlank(alipayAccountBill.getInAmount()) &&
                    new BigDecimal(alipayAccountBill.getInAmount()).compareTo(BigDecimal.ZERO) > 0){
                billDO.setTradeType((TradeType.IN.getKey()));
                billDO.setTradeAmount(multiply100(new BigDecimal(alipayAccountBill.getInAmount())).intValue());
            }
            if(StringUtils.isNotBlank(alipayAccountBill.getOutAmount()) &&
                    new BigDecimal(alipayAccountBill.getOutAmount()).compareTo(BigDecimal.ZERO) < 0){
                billDO.setTradeType((TradeType.OUT.getKey()));
                billDO.setTradeAmount(multiply100(new BigDecimal(alipayAccountBill.getOutAmount()).abs()).intValue());
            }
            billDO.setBizType(getBizType(alipayAccountBill.getBizType()));
            billDO.setRemark(alipayAccountBill.getRemark());
        } catch (Exception e) {
            log.error("支付宝账务账单文件映射持久化对象时出现异常，alipayAccountBill:{}", alipayAccountBill);
            throw e;
        }

        return billDO;
    }

    /**
     * 金额转为分
     *
     */
    private BigDecimal multiply100(BigDecimal amount) {
        if(amount != null) {
            return amount.multiply(new BigDecimal("100"));
        }
        return null;
    }

    private Integer getBizType(String bizType) {

        BizTypeEnum bizTypeEnum = LocalEnumUtils.findByDesc(BizTypeEnum.class,bizType);
        if(Objects.nonNull(bizTypeEnum)){
            return bizTypeEnum.getCode();
        }else {
            log.error("未知的支付宝业务类型bizType = {}",bizType);
            return Integer.valueOf(BizErrorCode.UNKNOWN_BIZ_TYPE.getCode());
        }

    }

    @Getter
    @AllArgsConstructor
    private enum BizTypeEnum implements EnumInterface<BizTypeEnum> {
        PAY(1, "在线支付"),
        REFUND(2, "交易退款"),
        FEE(3, "收费"),
        REFUND_FEE(4, "退费"),
        TRANSFER(5, "转账"),
        OTHER(6, "其它"),
        COUPON(7, "红包保证金支付"),
        WITHDRAWAL(8, "提现"),
        ;

        private final Integer code;
        private final String desc;

        @Override
        public BizTypeEnum getDefault() {
            return null;
        }
    }

    private String getMchId(ThirdpartyPayType payType) {
        ApiConfig config = ApiConfig.getConfig(payType);
        if (Objects.isNull(config)) {
            return null;
        }

        return Optional.ofNullable(config.getParams().get(MCH_ID_NAME)).orElse(config.getParams().get(P_ID_NAME));
    }
}
