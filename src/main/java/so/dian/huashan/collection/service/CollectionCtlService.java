package so.dian.huashan.collection.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.controller.request.CacheEvictReq;
import so.dian.huashan.collection.facade.CollectionPipelineFacade;
import so.dian.huashan.collection.facade.CollectionVoucherFacade;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.collection.service.entity.KafkaInfoBO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.pipeline.ExecutorCommand;
import so.dian.huashan.common.enums.PropagateModel;

import javax.annotation.Resource;
import java.util.Objects;

import static so.dian.huashan.common.constant.CacheNameConsts.BRAKE_CACHE_NAME;
import static so.dian.huashan.common.constant.CommonConstants.COLLECTION_STOP_FLAG;
import static so.dian.huashan.common.enums.PropagateModel.LOCAL;

/**
 * @author: miaoshuai
 * @create: 2023/09/28 18:09
 * @description:
 */
@Slf4j
@Service
public class CollectionCtlService {

    @Autowired
    private KafkaListenerEndpointRegistry registry;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private CollectionVoucherFacade voucherFacade;

    @Autowired
    private CollectionPipelineFacade pipelineFacade;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.listener-brake.topic}")
    private String topic;

    @Value("${rocketmq.listener-brake.tag}")
    private String tag;

    public boolean consumerBrake(String consumerId, Boolean pause, String propagateModelStr) {
        if (StrUtil.isBlank(consumerId) || Objects.isNull(pause))
            return false;

        PropagateModel propagateModel = PropagateModel.from(propagateModelStr);
        if (Objects.isNull(propagateModel))
            throw new IllegalArgumentException("传播模式不合法");

        MessageListenerContainer listenerContainer = registry.getListenerContainer(consumerId);
        if (Objects.isNull(listenerContainer)) {
            log.error("消费者[{}]不存在", consumerId);
            return false;
        }

        if (LOCAL.equals(propagateModel)) {
            if (pause && !listenerContainer.isPauseRequested()) {
                listenerContainer.pause();
                log.info("消费者[{}]暂停成功", consumerId);
            }
            if (!pause && listenerContainer.isPauseRequested()) {
                listenerContainer.resume();
                log.info("消费者[{}]唤醒成功", consumerId);
            }
            return Boolean.TRUE;
        }

        RMap<String, Boolean> brakeFlag = redissonClient.getMap(BRAKE_CACHE_NAME);
        brakeFlag.put(consumerId, pause);
        Message<String> message = MessageBuilder.withPayload(consumerId).build();
        SendResult sendResult = rocketMQTemplate.syncSend(StrUtil.join(StrUtil.COLON, topic, tag), message);
        log.info("制动消息发送结果：{}", sendResult.toString());
        return true;
    }

    public KafkaInfoBO consumerMetric(String consumerId) {
        MessageListenerContainer listenerContainer = registry.getListenerContainer(consumerId);
        if (Objects.isNull(listenerContainer)) {
            log.error("消费者[{}]不存在", consumerId);
            return null;
        }

        return KafkaInfoBO.builder()
                .isPauseRequested(listenerContainer.isPauseRequested())
                .isContainerPaused(listenerContainer.isContainerPaused())
                .isAutoStartup(listenerContainer.isAutoStartup())
                .isRunning(listenerContainer.isRunning())
                .build();
    }

    public Boolean skipCollection(String bizDomainCode) {
        if (StrUtil.isBlank(bizDomainCode))
            return Boolean.FALSE;

        RBucket<Boolean> bucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, COLLECTION_STOP_FLAG, bizDomainCode));
        if (bucket.get()) {
            return bucket.getAndSet(Boolean.FALSE);
        }
       return bucket.getAndSet(Boolean.TRUE);
    }

    public void directProcess(Long voucherId) {
        if (Objects.isNull(voucherId))
            return;

        CollectionVoucherBO voucherBO = voucherFacade.find(voucherId);
        CollectionPipelineBO pipelineBO = pipelineFacade.findByTableName(voucherBO.getVoucher().getTable());
        if (Objects.nonNull(pipelineBO)) {
            CollectionPipeline pipeline = SpringUtil.getBean(pipelineBO.getStrategyCode(), CollectionPipeline.class);
            ExecutorCommand command = new ExecutorCommand(pipeline);
            CollectionProcessResult processResult = command.execute(voucherBO);
            log.info("直接处理一个凭证成，凭证ID:{}, 处理结果:{}", voucherId, JSON.toJSONString(processResult));
        }
    }

    public Boolean cacheEvict(CacheEvictReq cacheEvictReq) {

        String name = cacheEvictReq.getName();
        Object key;
        if (String.class.equals(cacheEvictReq.getKeyClass())) {
            key = cacheEvictReq.getKey();
        }else {
            key = JSON.parseObject(cacheEvictReq.getKey(), cacheEvictReq.getKeyClass());
        }
        RMapCache<Object, Object> mapCache = redissonClient.getMapCache(name, new JsonJacksonCodec());

        if (Objects.nonNull(key)) {
            mapCache.remove(key);
        }else {
            mapCache.delete();
        }

        return Boolean.TRUE;
    }
}
