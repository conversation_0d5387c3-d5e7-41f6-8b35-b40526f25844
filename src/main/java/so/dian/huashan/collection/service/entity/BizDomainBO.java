package so.dian.huashan.collection.service.entity;

import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import so.dian.huashan.collection.mapper.BizDomainDO;

import java.util.Objects;
import java.util.Optional;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/08 16:56
 * @description:
 */
@NoArgsConstructor
@Setter
@Getter
public class BizDomainBO {

    private Long id;

    private String code;

    private String name;

    private DomainConfigBO config;

    @Builder
    BizDomainBO(Long id, String code, String name, DomainConfigBO config) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.config = config;
    }

    public static BizDomainBO from(BizDomainDO bizDomainDO) {
        if (Objects.isNull(bizDomainDO))
            return null;

        DomainConfigBO configBO = JSON.parseObject(bizDomainDO.getConfig(), DomainConfigBO.class);
        BizDomainBO bizDomainBO = new BizDomainBO();
        bizDomainBO.id = bizDomainDO.getId();
        bizDomainBO.code = bizDomainDO.getCode();
        bizDomainBO.name = bizDomainDO.getName();
        bizDomainBO.config = configBO;
        return bizDomainBO;
    }

    public BizDomainDO toDO() {

        BizDomainDO bizDomainDO = new BizDomainDO();
        bizDomainDO.setId(id);
        bizDomainDO.setCode(code);
        bizDomainDO.setName(name);
        bizDomainDO.setConfig(Optional.ofNullable(config).map(JSON::toJSONString).orElse(null));
        return bizDomainDO;
    }
}
