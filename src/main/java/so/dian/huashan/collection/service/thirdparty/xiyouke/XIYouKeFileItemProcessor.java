package so.dian.huashan.collection.service.thirdparty.xiyouke;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.enums.DeletedEnum;
import so.dian.huashan.check.emuns.payBill.BillSourceEnum;
import so.dian.huashan.check.emuns.payBill.PayBillStatus;
import so.dian.huashan.check.emuns.payBill.PayBillThirdStatus;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.collection.mapper.param.PayBillParam;
import so.dian.huashan.common.enums.TradeType;
import so.dian.huashan.common.mapper.yandang.ChannelMapper;
import so.dian.huashan.common.mapper.yandang.TradeOrderMapper;
import so.dian.huashan.common.utils.DateUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
@Service
@StepScope
public class XIYouKeFileItemProcessor implements ItemProcessor<XiYouKeBill, PayBillThirdDO> {
    @Override
    public PayBillThirdDO process(XiYouKeBill xiYouKeBill) throws Exception {
        PayBillThirdDO thirdDO = new PayBillThirdDO();
        thirdDO.setFileId(xiYouKeBill.getBillFileId());
        thirdDO.setRemark(xiYouKeBill.getRemark());
        thirdDO.setStatus(PayBillStatus.INIT.getCode());
        thirdDO.setSource(BillSourceEnum.XIYOUKE.getCode());
        String code = TradeType.getKeyByDesc(xiYouKeBill.getTradeType());
        thirdDO.setTradeType(code);
        if (xiYouKeBill.getOuterOrderStatus().contains("发放失败")){
            thirdDO.setThirdStatus(PayBillThirdStatus.FAIL.getCode());
        } else {
            thirdDO.setThirdStatus(PayBillThirdStatus.SUCCESS.getCode());
            if (StringUtils.isNotBlank(code)&& TradeType.IN.getKey().equals(code)){
                thirdDO.setThirdStatus(PayBillThirdStatus.REFUND.getCode());
            }
        }
        thirdDO.setTradeNo(xiYouKeBill.getTradeNo());
        thirdDO.setChannelTradeNo(xiYouKeBill.getOutOrderNo());
        thirdDO.setGmtUpdate(System.currentTimeMillis());
        thirdDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
        BigDecimal bigDecimal =  new BigDecimal(xiYouKeBill.getTradeAmount()).multiply(new BigDecimal(100));
        thirdDO.setTradeAmount(bigDecimal.longValue());
        thirdDO.setTradeTime(DateUtil.parseShortDateString(xiYouKeBill.getReleaseTime()));

        PayBillParam billParam = new PayBillParam();
        billParam.setTradeNo(xiYouKeBill.getTradeNo());
        return thirdDO;
    }
}
