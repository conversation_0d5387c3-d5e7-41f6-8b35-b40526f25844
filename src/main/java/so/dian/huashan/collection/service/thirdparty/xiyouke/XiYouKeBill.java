package so.dian.huashan.collection.service.thirdparty.xiyouke;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
@Data
@ToString
public class XiYouKeBill implements Serializable {

    /**
     * 渠道订单号
     */
    private String outOrderNo;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 服务商
     */
    private String serviceProvider;
    /**
     *  姓名
     */
    private String name;
    /**
     * 银行卡
     */
    private String bankCard;
    /**
     *  电话
     */
    private String telephone;

    /**
     *  身份证
     */
    private String iDCard;
    /**
     * 交易金额
     */
    private String tradeAmount;
    /**
     * 服务费
     */
    private String serviceCharge;
    /**
     * 外部订单状态
     */
    private String outerOrderStatus;

    /**
     * 收支类型
     */
    private String tradeType;
    /**
     * 发放通道
     */
    private String distributionChannel;
    /**
     *  创建时间
     */
    private String createTime;

    /**
     * 来源
     */
    private String source;
    /**
     * 发放时间
     */
    private String releaseTime;
    /**
     * 外部的支付流水号
     */
    private String outerPayNo;
    /**
     * 反馈结果
     */
    private String feedBackResults;
    /**
     * 回单
     */
    private String receipt;
    /**
     * 订单号
     */
    private String tradeNo;

    /**
     * 备注
     */
    private String remark;

    private Long billFileId;

}
