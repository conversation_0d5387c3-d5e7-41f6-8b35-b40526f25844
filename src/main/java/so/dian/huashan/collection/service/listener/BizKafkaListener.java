package so.dian.huashan.collection.service.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.service.BizMessageExecuteService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miaoshuai
 * @create: 2023/12/22 14:50
 * @description:
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "spring.kafka.biz.listener", name = "enable", havingValue = "true")
public class BizKafkaListener {

    @Resource
    private BizMessageExecuteService bizMessageExecuteService;


    @KafkaListener(id = "settleControlOrder",
            topics = {"${spring.kafka.control.consumer.topic}"},
            containerFactory = "bizKafkaContainerFactory",
            groupId = "${spring.kafka.control.consumer.group-id}"
    )
    public void onBizMessage(List<ConsumerRecord<String, String>> datas, Acknowledgment acknowledgment) {
        log.debug("接收到抽单池消息，消息体：{}", JSON.toJSONString(datas.stream().map(ConsumerRecord::value).collect(Collectors.toList())));
        for(ConsumerRecord<String, String> data : datas){
            try {
                bizMessageExecuteService.executeControlOrder(data.value());
            }catch (Exception e) {
                log.error(BILLING_SETTLE, ">>>清结算抽单池数据消费异常，内容：{}", data.value(),e);
            }
        }
        acknowledgment.acknowledge();
    }
}
