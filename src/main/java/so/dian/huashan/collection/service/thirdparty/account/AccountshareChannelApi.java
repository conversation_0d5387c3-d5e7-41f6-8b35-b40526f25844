package so.dian.huashan.collection.service.thirdparty.account;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.service.thirdparty.AbstractChannelApi;
import so.dian.huashan.collection.service.thirdparty.BillRequestParam;
import so.dian.huashan.collection.service.thirdparty.weixin.partner.WeixinpartnerBillApi;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.Collections;
import java.util.List;

@Slf4j
@Service
public class AccountshareChannelApi extends AbstractChannelApi {
    @Autowired
    private WeixinpartnerBillApi weixinPartnerBillApi;
    public List<String> toPullBillFile(BillRequestParam requestParam) throws Exception {
        String directory = weixinPartnerBillApi.downloadSplitBill(requestParam.getBillDate(), getDirectory(requestParam));
        if (StringUtils.isBlank(directory)){
            return Collections.emptyList();
        }
        return Collections.singletonList(directory);

    }

}
