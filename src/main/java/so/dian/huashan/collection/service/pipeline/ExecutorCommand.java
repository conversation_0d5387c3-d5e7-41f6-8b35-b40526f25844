package so.dian.huashan.collection.service.pipeline;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import so.dian.huashan.collection.service.CollectionVoucherService;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.enums.CollectionVoucherStatus;
import so.dian.huashan.framework.transaction.ManualTransaction;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import static so.dian.huashan.common.exception.LogMarkerFactory.COLLECTION_DATA_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/12/26 10:35
 * @description:
 */
@Slf4j
public class ExecutorCommand {

    protected final CollectionVoucherService voucherService;

    private final CollectionPipeline pipeline;

    public ExecutorCommand(CollectionPipeline pipeline) {
        this.voucherService = SpringUtil.getBean(CollectionVoucherService.class);
        this.pipeline = Objects.requireNonNull(pipeline, "pipeline must not null");
    }

    public CollectionProcessResult execute(CollectionVoucherBO voucherBO) {
        AtomicReference<CollectionProcessResult> resultRef = new AtomicReference<>();
        resultRef.set(doExecute(voucherBO));
        CollectionProcessResult processResult = resultRef.get();
        log.debug("管道处理凭证结束，凭证ID:{}, 处理结果:{}", voucherBO.getId(), JSON.toJSONString(processResult));
        return processResult;
    }

    private CollectionProcessResult doExecute(CollectionVoucherBO voucherBO) {
        if (Objects.isNull(voucherBO))
            return CollectionProcessResult.success();

        boolean seized = voucherService.seizeVoucher(voucherBO.getId(), voucherBO.getVersion(), CollectionVoucherStatus.IN_PROCESS);
        if (!seized) {
            return CollectionProcessResult.success().voucherId(voucherBO.getId());
        }

        CollectionProcessResult process = null;
        try {
            PipelineBO pipelineBO = new PipelineBO(voucherBO.getVoucher(), voucherBO.getId());
            process = pipeline.process(pipelineBO);
        }catch (Exception e) {
            log.error(COLLECTION_DATA_MARKER, "管道处理失败, 凭证ID:{}", voucherBO.getId(), e);
            process = CollectionProcessResult.fail();
        }finally {
            CollectionVoucherBO complate = complete(voucherBO, process);
            if (Objects.nonNull(complate))
                voucherService.modify(complate);
        }

        return process;
    }

    protected CollectionVoucherBO complete(CollectionVoucherBO voucherBO, CollectionProcessResult process) {
        if (Objects.isNull(process))
            return null;

        CollectionVoucherBO.CollectionVoucherBOBuilder voucherBOBuilder = CollectionVoucherBO.builder().id(voucherBO.getId());
        if (process.getSuccess()) {
            voucherBOBuilder.status(CollectionVoucherStatus.SUCCESS);
        }else {
            voucherBOBuilder.status(CollectionVoucherStatus.FAIL);
        }
        voucherBOBuilder.remark(voucherBO.getStatus().getDesc());

        return voucherBOBuilder.build();
    }
}
