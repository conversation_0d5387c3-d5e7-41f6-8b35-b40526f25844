package so.dian.huashan.collection.service.entity;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import so.dian.huashan.collection.mapper.dos.CollectionVoucherDO;
import so.dian.huashan.common.enums.CollectionVoucherStatus;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/25 09:49
 * @description:
 */
@Builder
@Setter
@Getter
public class CollectionVoucherBO {

    public static final String WELL_NUMBER = "#";

    private Long id;

    private String source;

    private CollectionVoucherStatus status;

    private Integer retryTime;

    private BinlogBO voucher;

    private Date receiveTime;

    private String remark;

    private Integer version;

    public static CollectionVoucherBO from(BinlogBO binlogBO) {
        if (Objects.isNull(binlogBO))
            return null;

        return CollectionVoucherBO.builder()
                .source(StrUtil.join(WELL_NUMBER, binlogBO.getDbName(), binlogBO.getTable()))
                .status(CollectionVoucherStatus.INITIAL)
                .receiveTime(new Date())
                .retryTime(0)
                .version(0)
                .voucher(binlogBO)
                .remark(CollectionVoucherStatus.INITIAL.getDesc())
                .build();
    }

    public static CollectionVoucherBO from(CollectionVoucherDO voucherDO) {
        if (Objects.isNull(voucherDO))
            return null;

        BinlogBO binlogBO = JSON.parseObject(voucherDO.getVoucher(), BinlogBO.class);
        binlogBO.setOriginalContent(voucherDO.getVoucher());
        return CollectionVoucherBO.builder()
                .id(voucherDO.getId())
                .source(voucherDO.getSource())
                .status(CollectionVoucherStatus.from(voucherDO.getStatus()))
                .retryTime(voucherDO.getRetryTime())
                .voucher(binlogBO)
                .version(voucherDO.getVersion())
                .receiveTime(voucherDO.getReceiveTime())
                .remark(voucherDO.getRemark())
                .build();
    }

    public CollectionVoucherDO toDO() {
        CollectionVoucherDO voucherDO = new CollectionVoucherDO();
        voucherDO.setId(getId());
        voucherDO.setSource(getSource());
        voucherDO.setStatus(Optional.ofNullable(getStatus()).map(CollectionVoucherStatus::getCode).orElse(null));
        voucherDO.setRetryTime(getRetryTime());
        voucherDO.setVoucher(Optional.ofNullable(getVoucher()).map(BinlogBO::getOriginalContent).orElse(null));
        voucherDO.setReceiveTime(getReceiveTime());
        voucherDO.setRemark(getRemark());
        voucherDO.setVersion(getVersion());
        return voucherDO;
    }
}
