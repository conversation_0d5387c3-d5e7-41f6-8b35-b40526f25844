package so.dian.huashan.collection.service.thirdparty.weixin;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class WeixinBill implements Serializable {

    private static final long serialVersionUID = -4940717436114184875L;

    /** 记账时间 */
    private String            accountTime;
    /** 微信支付业务单号 */
    private String            bizNo;
    /** 资金流水单号 */
    private String            seqNo;
    /** 业务名称 */
    private String            bizName;
    /** 业务类型 */
    private String            bizType;
    /** 收支类型 **/
    private String            tradeType;
    /** 收支金额(元) **/
    private String            tradeAmount;
    /** 账户结余(元) **/
    private String            balance;
    /** 资金变更提交申请人 **/
    private String            applicantName;
    /** 备注 **/
    private String            remark;
    /** 业务凭证号 **/
    private String            bizVoucherNo;

    private Long billFileId;

}
