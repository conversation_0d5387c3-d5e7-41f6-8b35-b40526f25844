package so.dian.huashan.collection.service.pipeline;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.mapper.SettleDetailOriginalMapper;
import so.dian.huashan.collection.mapper.dos.SettleDetailOriginalDO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.mapper.lhc.entity.SettleDetailBO;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class IotPipeline extends CollectionPipeline{
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private SettleDetailOriginalMapper settleDetailOriginalMapper;
    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        log.info("IotPipeline isSkip is checked");
        if(StringUtils.isBlank(pipelineBO.getBinLog().getAfter())){
            return true;
        }
        JSONObject data = JSON.parseObject(pipelineBO.getBinLog().getAfter());

        String status = data.getString("status");
        String amountOutput = data.getString("amount_output");

        if (!status.equals("4") || "0".equals(amountOutput)) {
            return true;
        }
        return false;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        SettleDetailBO settleDetailBO = JSON.parseObject(pipelineBO.getBinLog().getAfter(), SettleDetailBO.class);
        log.info("IotPipeline doProcess get settleDetailBO:{}",JSON.toJSON(settleDetailBO));
        if (Objects.isNull(settleDetailBO.getSettleDetailNo())){
            log.warn("IotPipeline settleDetailNo is null get voucherId:{}",pipelineBO.getVoucherId());
            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId());
        }
        SettleDetailOriginalDO settleDetailOriginalDO = init(settleDetailBO);
        RLock lock = redissonClient.getLock(StrUtil.join(StrUtil.COLON, "iot_collection", settleDetailBO.getSettleDetailNo()));
        try {
            if (!lock.tryLock(30, TimeUnit.SECONDS)) {
                throw new RuntimeException("等待锁超时");
            }

            SettleDetailOriginalDO oldSettle = settleDetailOriginalMapper.selectBySettleDetailNo(String.valueOf(settleDetailBO.getSettleDetailNo()));
            log.info("IotPipeline get settleDetailNo:{}, oldSettle:{}",settleDetailBO.getSettleDetailNo(),oldSettle);
            if (Objects.isNull(oldSettle)){
                settleDetailOriginalDO.setGmtCreate(System.currentTimeMillis());
                settleDetailOriginalDO.setGmtUpdate(System.currentTimeMillis());
                settleDetailOriginalMapper.insert(settleDetailOriginalDO);
                return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(settleDetailBO.getId());
            }
            // 关键字段匹配
            if (checkUpdate(oldSettle, settleDetailOriginalDO)) {
                settleDetailOriginalDO.setId(oldSettle.getId());
                settleDetailOriginalDO.setGmtUpdate(System.currentTimeMillis());
                settleDetailOriginalMapper.updateByPrimaryKeySelective(settleDetailOriginalDO);
            }


        } catch (Exception e){
            log.error("IotPipeline  error.{}",JSON.toJSON(settleDetailBO) , e);
            throw new RuntimeException("结算明细采集失败");
        } finally {
            lock.unlock();
        }
        return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(settleDetailBO.getId());
    }

    private Boolean checkUpdate(SettleDetailOriginalDO oldSettle,SettleDetailOriginalDO settleDetailOriginalDO){
        if (Objects.isNull(oldSettle)||Objects.isNull(settleDetailOriginalDO)){
            return true;
        }

        if (!Objects.equals(oldSettle.getTpSettleNo(),settleDetailOriginalDO.getTpSettleNo())){
            return true;
        }
        if (!Objects.equals(oldSettle.getAmountOutput(),settleDetailOriginalDO.getAmountOutput())){
            return true;
        }
        if (!Objects.equals(oldSettle.getTpSourceId(),settleDetailOriginalDO.getTpSourceId())){
            return true;
        }
        if (!Objects.equals(oldSettle.getTpReceiveId(),settleDetailOriginalDO.getTpReceiveId())){
            return true;
        }
        return false;
    }

    private SettleDetailOriginalDO init(SettleDetailBO settleDetailBO){
        SettleDetailOriginalDO settleDetailOriginalDO = new SettleDetailOriginalDO();
        settleDetailOriginalDO.setCheckStatus(0);
        if (Objects.nonNull(settleDetailBO.getSettleDetailNo())){
            settleDetailOriginalDO.setSettleDetailNo(String.valueOf(settleDetailBO.getSettleDetailNo()));
        }
        settleDetailOriginalDO.setTpSettleNo(settleDetailBO.getTpSettleNo());
        settleDetailOriginalDO.setAmountOutput(settleDetailBO.getAmountOutput());
        settleDetailOriginalDO.setTpSourceId(settleDetailBO.getTpSourceId());
        settleDetailOriginalDO.setTpReceiveId(settleDetailBO.getTpTargetId());
        settleDetailOriginalDO.setDeleted(0);
        return settleDetailOriginalDO;
    }
}
