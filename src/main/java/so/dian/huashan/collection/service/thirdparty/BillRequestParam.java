package so.dian.huashan.collection.service.thirdparty;

import lombok.Builder;
import lombok.Getter;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.Date;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/20 11:41
 * @description:
 */
@Getter
@Builder
public class BillRequestParam {

    private ApiConfig config;

    private Date billDate;

    public ThirdpartyPayType getPayType() {
        return config.getPayType();
    }
}
