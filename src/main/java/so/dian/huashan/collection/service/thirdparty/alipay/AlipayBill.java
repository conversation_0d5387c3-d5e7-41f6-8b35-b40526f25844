package so.dian.huashan.collection.service.thirdparty.alipay;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class AlipayBill implements Serializable {

    private static final long serialVersionUID = -4940717436114184875L;

    /** 账务流水号 */
    private String            accountSeqNo;
    /** 业务流水号 */
    private String            bizSeqNo;
    /** 商户单号 */
    private String            merchantNo;
    /** 商品名称 */
    private String            productName;
    /** 发生时间 */
    private String            occurTime;
    /** 对方账号 **/
    private String            accountNo;
    /** 收支金额(元) **/
    private String            inAmount;
    /** 支出金额(元) **/
    private String            outAmount;
    /** 账户余额 **/
    private String            balance;
    /** 交易渠道 **/
    private String            channel;
    /** 业务类型 **/
    private String            bizType;
    /** 备注 **/
    private String            remark;

    private Long billFileId;
}
