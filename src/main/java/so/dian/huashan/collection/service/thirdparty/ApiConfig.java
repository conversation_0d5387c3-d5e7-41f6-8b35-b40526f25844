package so.dian.huashan.collection.service.thirdparty;

import com.google.common.collect.Maps;
import lombok.Data;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/17 17:36
 * @description:
 */
@Data
public class ApiConfig {

    private String requestUrl;

    private String channelId;
    private Integer mchType;
    private ThirdpartyPayType payType;

    private Map<String, String> params;

    private static Map<ThirdpartyPayType, ApiConfig> configMap = Maps.newConcurrentMap();

    public static void addConfig(ThirdpartyPayType payType, ApiConfig config) {
        configMap.put(payType, config);
    }

    public static ApiConfig getConfig(ThirdpartyPayType payType) {
        return configMap.get(payType);
    }

    public static List<ApiConfig> getAll() {
        return new ArrayList<>(configMap.values());
    }
}
