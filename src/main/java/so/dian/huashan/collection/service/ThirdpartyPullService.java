package so.dian.huashan.collection.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import so.dian.himalaya.common.exception.BizException;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huangshan.service.TaskParamRecordService;
import so.dian.huangshan.service.val.ExecuteBaseResult;
import so.dian.huashan.collection.mapper.ThirdPartyBillMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.common.enums.BatchJob;
import so.dian.huashan.common.enums.ThirdpartyChannel;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.facade.LushanFacade;
import so.dian.huashan.common.mapper.yandang.ChannelMapper;
import so.dian.huashan.common.mapper.yandang.dos.ChannelDO;
import so.dian.huashan.common.val.IncomeCheckParam;
import so.dian.huashan.model.dto.PullJobDTO;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static so.dian.huashan.common.enums.BizErrorCode.START_BATCH_JOB_FAIL;

/**
 * @author: miaoshuai
 * @create: 2023/03/22 17:30
 * @description:
 */
@Slf4j
@Service
public class ThirdpartyPullService implements TaskParamRecordService<IncomeCheckParam, ExecuteBaseResult> {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private ApplicationContext applicationContext;

    @Value("${huashan.biz.collection-registry-id}")
    private Long collectionRegistryId;

    @Resource
    private ThirdPartyBillMapper thirdPartyBillMapper;

    @Resource
    private LushanFacade lushanFacade;
    @Resource
    private ChannelMapper channelMapper;

    @Transactional(value = "huashan")
    @GetMapping("transa")
    public void testTransactional() {

        log.info("开始执行");
        for (int i = 0; i < 10; i ++) {
            ThirdPartyBillDO billDO = new ThirdPartyBillDO();
            billDO.init();
            billDO.setFileId((long) i);
            billDO.setCheckDate(20230112);
            if (i == 5) {
                throw new RuntimeException();
            }
            billDO.setTradeType("in");
            billDO.setBillTime(new Date());
            billDO.setPayNo("ggg");
            billDO.setTradeAmount(12);
            billDO.setBizType(1);
            billDO.setBillSource(1);
            billDO.setStatus(0);
            billDO.setRemark("test");
            thirdPartyBillMapper.insertSelective(billDO);
            log.info("id : {}", billDO.getId());
        }
    }

    /**
     * 推入job
     */
    public void pullXiYouKeJob(PullJobDTO pullJobDTO){
        log.info("pullJob get pullJobDTO:{}",pullJobDTO);
        if (Objects.isNull(pullJobDTO)||Objects.isNull(pullJobDTO.getPayType())){
            log.error("pullJob is null pullJobDTO:{}",pullJobDTO);
            return;
        }
        Job job = get(pullJobDTO.getPayType().getChannel());
        JobParametersBuilder jobParameterBulider = new JobParametersBuilder();
        jobParameterBulider.addString("payType", pullJobDTO.getPayType().name());
        Date beginDate = LocalDateUtils.offsetDay(LocalDateUtils.beginOfDay(DateTime.now()), -1);
        if (StringUtils.isNotBlank(pullJobDTO.getBeginDate())) {
            beginDate = DateUtil.parse(pullJobDTO.getBeginDate(),"yyyy-MM-dd HH:mm:ss");
        }
        if (StringUtils.isNotBlank(pullJobDTO.getUrl())){
            jobParameterBulider.addString("url", pullJobDTO.getUrl());
        }
        // 申请单号 拦截
        String[] strings = pullJobDTO.getPartnerOrderId().split("_");
        String channelId = strings[strings.length-1];
        jobParameterBulider.addString("channelId", channelId);
        ChannelDO channelDO  = channelMapper.selectByPrimaryKey(Long.valueOf(channelId));
        if (Objects.isNull(channelDO)){
            log.error("ThirdpartyPullService get channelId:{}",channelId);
            throw new RuntimeException("账户不存在");
        }
        jobParameterBulider.addString("settleSubjectId", String.valueOf(channelDO.getSettleSubjectId()));
        jobParameterBulider.addString("settleSubjectType", String.valueOf(channelDO.getSettleSubjectType()));
        jobParameterBulider.addString("billDate", DateUtil.formatDate(beginDate));
        jobParameterBulider.addLong("collectionRegistryId", collectionRegistryId);
        jobParameterBulider.addLong("randomTime", System.currentTimeMillis());
        try {
            log.info("开始启动批处理！！！, 执行参数：{}", jobParameterBulider.toJobParameters());
            JobExecution execution = jobLauncher.run(job, jobParameterBulider.toJobParameters());
            log.info("批处理任务启动成功, instanceId: {}", execution.getJobInstance().getInstanceId());
        }catch (Exception e) {
            log.error("批量拉取三方账单任务启动异常", e);
        }
    }

    public void pullFundJob(@NonNull ThirdpartyPayType payType, String param, Integer mchType,String mchId){
        Job job = get(payType.getChannel());
        JobParametersBuilder jobParameterBulider = new JobParametersBuilder();
        jobParameterBulider.addString("payType", payType.name());
        Date beginDate = LocalDateUtils.offsetDay(LocalDateUtils.beginOfDay(DateTime.now()), -1);
        if (StringUtils.isNotBlank(param)) {
            beginDate = LocalDateUtils.parse(param,"yyyyMMdd");
        }
        jobParameterBulider.addString("billDate", DateUtil.formatDate(beginDate));
        jobParameterBulider.addString("mchType", String.valueOf(mchType));
        jobParameterBulider.addString("mchId", mchId);
        jobParameterBulider.addLong("collectionRegistryId", collectionRegistryId);
        jobParameterBulider.addLong("randomTime", System.currentTimeMillis());

        try {
            log.info("开始启动批处理！！！, 执行参数：{}", jobParameterBulider.toJobParameters());
            JobExecution execution = jobLauncher.run(job, jobParameterBulider.toJobParameters());
            log.info("批处理任务启动成功, instanceId: {}", execution.getJobInstance().getInstanceId());
            if (!ExitStatus.COMPLETED.equals(execution.getExitStatus()))
                throw BizException.create(START_BATCH_JOB_FAIL);
        }catch (Exception e) {
            log.error("批量拉取三方账单任务启动异常", e);
            throw BizException.create(START_BATCH_JOB_FAIL);
        }

    }
    public void pull(@NonNull ThirdpartyPayType payType,String param) {
        Job job = get(payType.getChannel());
        JobParametersBuilder jobParameterBulider = new JobParametersBuilder();
        jobParameterBulider.addString("payType", payType.name());
        Date beginDate = LocalDateUtils.offsetDay(LocalDateUtils.beginOfDay(DateTime.now()), -1);
        if (StringUtils.isNotBlank(param)) {
            beginDate = LocalDateUtils.parse(param,"yyyyMMdd");
        }
        jobParameterBulider.addString("billDate", DateUtil.formatDate(beginDate));
        jobParameterBulider.addLong("collectionRegistryId", collectionRegistryId);
        jobParameterBulider.addLong("randomTime", System.currentTimeMillis());

        try {
            log.info("开始启动批处理！！！, 执行参数：{}", jobParameterBulider.toJobParameters());
            JobExecution execution = jobLauncher.run(job, jobParameterBulider.toJobParameters());
            log.info("批处理任务启动成功, instanceId: {}", execution.getJobInstance().getInstanceId());
            if (!ExitStatus.COMPLETED.equals(execution.getExitStatus()))
                throw BizException.create(START_BATCH_JOB_FAIL);
        }catch (Exception e) {
            log.error("批量拉取三方账单任务启动异常", e);
            throw BizException.create(START_BATCH_JOB_FAIL);
        }
    }

    public void pullAll(IncomeCheckParam param) {
        List<ThirdpartyPayType> filterList = Arrays.asList(ThirdpartyPayType.CHANNEL_SHARE_ACCOUNT,ThirdpartyPayType.WEIXIN_PARTNER);
        List<ApiConfig> apiConfigs = apiConfigs(param);
        log.info("pullAll get apiConfigs:{}", JSON.toJSONString(apiConfigs));
        for (ApiConfig apiConfig : apiConfigs) {
            if (filterList.contains(apiConfig.getPayType())){
                continue;
            }
            pull(apiConfig.getPayType(), param.getBillDate());
        }
    }
    public void pullPayType(IncomeCheckParam param) {
        List<ApiConfig> apiConfigs = apiConfigs(param);
        log.info("pullPayType get apiConfigs:{}", JSON.toJSONString(apiConfigs));
        pull(apiConfigs.get(0).getPayType(), param.getBillDate());
    }

    private List<ApiConfig> apiConfigs(IncomeCheckParam param) {
        List<ApiConfig> all = ApiConfig.getAll();
        ThirdpartyPayType payType = ThirdpartyPayType.fromName(param.getPayType());
        if (Objects.isNull(payType))
            return all;

        return all.stream().filter(conf -> conf.getPayType().equals(payType)).collect(Collectors.toList());
    }

    private Job get(ThirdpartyChannel channel) {
        if (ThirdpartyChannel.WEIXIN.equals(channel)) {
            return applicationContext.getBean(BatchJob.WEXIN_BILL_JOB.getJobName(), Job.class);
        }
        if (ThirdpartyChannel.XIYOUKE.equals(channel)) {
            return applicationContext.getBean(BatchJob.XIYOUKE_BILL_JOB.getJobName(), Job.class);
        }
        if (ThirdpartyChannel.WEIXINPARTNER.equals(channel)) {
            return applicationContext.getBean(BatchJob.WEXIN_PARTNER_BILL_JOB.getJobName(), Job.class);
        }
        if (ThirdpartyChannel.ACCOUNTSHARE.equals(channel)) {
            return applicationContext.getBean(BatchJob.SHARE_ACCOUNT_BILL_JOB.getJobName(), Job.class);
        }

        return applicationContext.getBean(BatchJob.ALIPAY_BILL_JOB.getJobName(), Job.class);
    }

    @Override
    public ExecuteBaseResult execute(IncomeCheckParam param) {
        if (Objects.isNull(param)||StringUtils.isBlank(param.getPayType())){
            pullAll(param);
        } else {
            pullPayType(param);
        }
        return null;
    }
}
