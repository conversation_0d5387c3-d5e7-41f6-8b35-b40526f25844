package so.dian.huashan.collection.service.entity;

import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import so.dian.huashan.collection.enums.CollectionWay;
import so.dian.huashan.collection.mapper.dos.CollectionPipelineDO;

import java.util.Objects;
import java.util.Optional;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/25 10:21
 * @description:
 */
@NoArgsConstructor
@Setter
@Getter
public class CollectionPipelineBO {

    private Long id;

    private Long bizDomainId;

    private String strategyCode;

    private CollectionWay collectionWay;

    private String tableName;

    @Builder
    CollectionPipelineBO(Long id,
                         Long bizDomainId,
                         String strategyCode,
                         CollectionWay collectionWay,
                         String tableName) {
        this.id = id;
        this.bizDomainId = bizDomainId;
        this.strategyCode = strategyCode;
        this.collectionWay = collectionWay;
        this.tableName = tableName;
    }

    public static CollectionPipelineBO from(CollectionPipelineDO pipelineDO) {

        if (Objects.isNull(pipelineDO))
            return null;

        return CollectionPipelineBO.builder()
                .id(pipelineDO.getId())
                .bizDomainId(pipelineDO.getBizDomainId())
                .strategyCode(pipelineDO.getStrategyCode())
                .collectionWay(CollectionWay.from(pipelineDO.getCollectionWay()))
                .tableName(pipelineDO.getTableName())
                .build();
    }

    public CollectionPipelineDO toDO() {
        CollectionPipelineDO pipelineDO = new CollectionPipelineDO();
        pipelineDO.setId(id);
        pipelineDO.setBizDomainId(bizDomainId);
        pipelineDO.setStrategyCode(strategyCode);
        pipelineDO.setCollectionWay(Optional.ofNullable(collectionWay).map(CollectionWay::getCode).orElse(null));
        pipelineDO.setTableName(tableName);
        return pipelineDO;
    }

}
