package so.dian.huashan.collection.service.pipeline;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.enums.DeletedEnum;
import so.dian.huashan.check.emuns.payBill.BillSourceEnum;
import so.dian.huashan.check.emuns.payBill.PayBillStatus;
import so.dian.huashan.check.emuns.payBill.PayBillTradeType;
import so.dian.huashan.collection.mapper.PayBillMapper;
import so.dian.huashan.collection.mapper.dos.PayBillDO;
import so.dian.huashan.collection.mapper.param.PayBillParam;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.mapper.lhc.entity.PayBillOuterBO;
import so.dian.huashan.common.mapper.yandang.TradeOrderMapper;
import so.dian.huashan.common.mapper.yandang.dos.TransOrderDO;
import so.dian.huashan.framework.task.ShardTaskService;


import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class PayBillPipeline extends CollectionPipeline{

    @Resource
    private PayBillMapper payBillMapper;
    @Resource
    private TradeOrderMapper tradeOrderMapper;

    @Resource
    private RedissonClient redissonClient;

    private static final List<Integer> PAY_CHANNEL_LIST = Arrays.asList(14);
    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        log.info("PayBillPipeline isSkip is checked");
        if(StringUtils.isBlank(pipelineBO.getBinLog().getAfter())){
            return true;
        }
        JSONObject data = JSON.parseObject(pipelineBO.getBinLog().getAfter());

        String tradeStatus = data.getString("trade_state");

        if (!tradeStatus.equals("3") || !PAY_CHANNEL_LIST.contains(data.getInteger("pay_channel"))) {
            return true;
        }
        return false;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        PayBillOuterBO outerDO = JSON.parseObject(pipelineBO.getBinLog().getAfter(), PayBillOuterBO.class);
        log.info("PayBillPipeline  get outerDO:{}",JSON.toJSON(outerDO));
        if (Objects.isNull(outerDO.getPayNo())){
            log.warn("PayBillPipeline billNO is null get voucherId:{}",pipelineBO.getVoucherId());
            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId());
        }

        // 初始化
        PayBillDO payBillDO = init(outerDO,pipelineBO.getVoucherId());
        RLock lock = redissonClient.getLock(StrUtil.join(StrUtil.COLON, "pay_bill_collection", payBillDO.getTradeNo()));
        try {
            if (!lock.tryLock(30, TimeUnit.SECONDS)) {
                throw new RuntimeException("等待锁超时");
            }
            // 支付流水
            TransOrderDO transOrderDO = tradeOrderMapper.findByTradeNo(outerDO.getPayNo());
            if (Objects.nonNull(transOrderDO)) {
                payBillDO.setChannelTradeNo(transOrderDO.getOutPayNo());
            }
            PayBillDO oldPayBillDO = payBillMapper.getOne(PayBillParam.builder()
                    .tradeNo(outerDO.getPayNo())
                    .tradeType(PayBillTradeType.OUT.getCode())
                    .build());
            if (Objects.isNull(oldPayBillDO)) {
                payBillDO.setStatus(PayBillStatus.INIT.getCode());
                payBillDO.setGmtCreate(System.currentTimeMillis());
                payBillMapper.insertSelective(payBillDO);
                return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(payBillDO.getId());
            }
            if (!checkUpdate(payBillDO, oldPayBillDO)) {
                return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(payBillDO.getId());
            }
            log.info("更新支付流水单");
            payBillDO.setStatus(oldPayBillDO.getStatus());
            payBillDO.setId(oldPayBillDO.getId());
            payBillDO.setGmtCreate(oldPayBillDO.getGmtCreate());
            if (PayBillStatus.PROCESSED.getCode().equals(oldPayBillDO.getStatus())) {
                payBillDO.setStatus(PayBillStatus.PROCESSING.getCode());
            }

            payBillMapper.updateByPrimaryKeySelective(payBillDO);
            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(payBillDO.getId());

        }catch (Exception e) {
            log.error("sendGoodsOrder order error.{}",JSON.toJSON(outerDO) , e);
            throw new RuntimeException("付款单采集失败");
        } finally {
            lock.unlock();
        }
    }
    /**
     * 校验是否
     */
    private Boolean checkUpdate(PayBillDO payBillDO,PayBillDO oldPayBillDO){
        if (Objects.isNull(oldPayBillDO)||Objects.isNull(payBillDO)){
            return true;
        }

        if (!Objects.equals(payBillDO.getTradeAmount(),oldPayBillDO.getTradeAmount())){
            return true;
        }
        if (!Objects.equals(payBillDO.getTradeTime(),oldPayBillDO.getTradeTime())){
            return true;
        }
        if (!Objects.equals(payBillDO.getPaySubjectId(),oldPayBillDO.getPaySubjectId())){
            return true;
        }
        return false;
    }
    private PayBillDO init(PayBillOuterBO outerDO,Long voucherId){
        PayBillDO payBillDO = new PayBillDO();
        payBillDO.setVoucherId(voucherId);
        payBillDO.setPaySubjectId(outerDO.getSettleSubjectId());
        payBillDO.setPaySubjectType(outerDO.getSettleSubjectType());
        payBillDO.setTradeNo(outerDO.getPayNo());
        payBillDO.setTradeType(PayBillTradeType.OUT.getCode());
        if (Objects.nonNull(outerDO.getPayFinishTime())) {
            payBillDO.setTradeTime(new Date(outerDO.getPayFinishTime()));
        }
        payBillDO.setTradeAmount(outerDO.getPayAmount());
        payBillDO.setSource(BillSourceEnum.outerCode2Code(outerDO.getPayChannel()));
        payBillDO.setGmtUpdate(System.currentTimeMillis());
        payBillDO.setDeleted(outerDO.getDeleted());
        return payBillDO;
    }
}
