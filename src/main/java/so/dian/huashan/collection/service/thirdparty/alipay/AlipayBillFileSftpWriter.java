package so.dian.huashan.collection.service.thirdparty.alipay;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.scope.context.StepSynchronizationManager;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.mapper.ThirdPartyBillFileMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillFileDO;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.utils.ThirdPartyBillPathUtil;

import javax.annotation.Resource;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static so.dian.huashan.common.enums.ThirdpartyPayType.ALIPAY_H5;

/**
 * @Author: ahuang
 * @CreateTime: 2024-12-12 16:25
 * @Description: 支付宝账单文件 Writer
 * - 解析后的 AlipayBill 数据写入本地新文件
 * - 文件路径：defaultDirectory/sftp/alipay/商户号/YYYYMMDD/
 * - 文件名：商户号_YYYYMMDD_billFileId.csv
 */
@Slf4j
@Component
@StepScope
public class AlipayBillFileSftpWriter implements ItemWriter<AlipayBill> {

    @Resource
    private ThirdPartyBillFileMapper thirdPartyBillFileMapper;

    @Value("${huashan.thirdparty.bill.file:#{systemProperties['user.home']}}")
    protected String defaultDirectory;

    @Value("#{jobParameters['billDate']}")
    private String billDate; // 从 Job 参数中获取 billDate

    public static final String ALIPAY_BILL_HEADER = "账务流水号,业务流水号,商户订单号,商品名称,发生时间,对方账号,收入金额（+元）,支出金额（-元）,账户余额（元）,交易渠道,业务类型,备注";

    @Override
    public void write(List<? extends AlipayBill> items) throws Exception {
        if (items.isEmpty()) {
            log.info(">>> 没有账单数据需要写入");
            return;
        }

        // 1. 写入文件
        log.info(">>> 开始将账单数据写入临时文件");
        String directoryPath = ThirdPartyBillPathUtil.getSftpDirectoryPath(defaultDirectory, billDate);
        File directory = new File(directoryPath);
        if (!directory.exists() && !directory.mkdirs()) {
            log.error(">>> 创建目录失败: {}", directoryPath);
            throw new RuntimeException("无法创建目标目录: " + directoryPath);
        }

        //只有ALIPAY_H5 才会上传
        ApiConfig config = ApiConfig.getConfig(ALIPAY_H5);
        String pId = config.getParams().get(ChannelConstant.ALIPAY.P_ID.getParam());

        String fileName = pId + "_" + billDate.replace("-", "") + ".tmp";
        File tempFile = new File(directory, fileName);

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile, true))) {
            log.info(">>> 写入临时文件: {}", tempFile.getAbsolutePath());

            // 如果文件为空则写入表头
            if (tempFile.length() == 0) {
                writer.write(ALIPAY_BILL_HEADER);
                writer.newLine();
            }

            for (AlipayBill bill : items) {
                writer.write(convertBillToCsvLine(bill));
                writer.newLine();
            }
        }
        log.info(">>> 所有账单数据已写入临时文件");

        try {
            // 2. 新增金额汇总逻辑
            BigDecimal chunkAmount = items.stream()
                    .map(bill -> {
                        // 解析收入（去掉+号）
                        BigDecimal in = parseAmount(bill.getInAmount(), "+");
                        // 解析支出（去掉-号并转为正数）
                        BigDecimal out = parseAmount(bill.getOutAmount(), "-");
                        // 直接计算单条记录的净收入：收入 - 支出
                        return in.subtract(out);
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 3. 将累加值存入ExecutionContext
            StepExecution stepExecution = StepSynchronizationManager.getContext().getStepExecution();
            ExecutionContext executionContext = stepExecution.getExecutionContext();
            BigDecimal totalAmount = (BigDecimal) executionContext.get("totalAmount");
            totalAmount = (totalAmount != null) ? totalAmount : BigDecimal.ZERO;
            executionContext.put("totalAmount", totalAmount.add(chunkAmount));
        } catch (Exception e) {
            log.error("支付宝每日提现金额计算失败,billDate={}", billDate, e);
        }
    }

    private BigDecimal parseAmount(String amountStr, String symbol) {
        if (amountStr == null) return BigDecimal.ZERO;
        try {
            return new BigDecimal(amountStr.replace(symbol, ""));
        } catch (NumberFormatException e) {
            log.error("金额格式错误: {}", amountStr);
            return BigDecimal.ZERO;
        }
    }

    private String convertBillToCsvLine(AlipayBill bill) {
        return String.join(",",
                addTabToString(bill.getAccountSeqNo()),
                addTabToString(bill.getBizSeqNo()),
                addTabToString(bill.getMerchantNo()),
                safeString(bill.getProductName()),
                safeString(bill.getOccurTime()),
                addTabToString(bill.getAccountNo()),
                safeString(bill.getInAmount()),
                safeString(bill.getOutAmount()),
                safeString(bill.getBalance()),
                safeString(bill.getChannel()),
                safeString(bill.getBizType()),
                safeString(bill.getRemark()));
    }

    private String safeString(String value) {
        return value == null ? "" : value;
    }

    // 将制表符加到数字前后，防止自动将其转换为数字或科学计数法
    private String addTabToString(String value) {
        return value == null ? "\t" : "\t" + value + "\t";
    }
}