package so.dian.huashan.collection.service.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.service.CollectionVoucherService;
import so.dian.huashan.collection.service.entity.BinlogBO;
import so.dian.huashan.collection.service.pipeline.PipelineExecutor;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static so.dian.huashan.common.constant.CommonConstants.COLLECTION_STOP_FLAG;
import static so.dian.huashan.common.exception.LogMarkerFactory.COLLECTION_DATA_MARKER;
import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: miaoshuai
 * @create: 2023/03/14 19:07
 * @description:
 */
@Slf4j
@Component
@ConditionalOnProperty(prefix = "spring.kafka.listener", name = "enable", havingValue = "true")
public class KafkaAutoIncrementListener {

    private final static String INCOME_BILL = "income_bill";

    private final static String BILL_SETTLE = "bill_settle";

    private final static String PAY_BILL = "pay_bill";

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PipelineExecutor pipelineExecutor;

    @Autowired
    private CollectionVoucherService collectionVoucherService;
    @KafkaListener(id = "autoIncrement",
            groupId = "${spring.kafka.income.consumer.group-id}",
            topics = {"${spring.kafka.income.consumer.topic}"},
            containerFactory = "incomeKafkaContainerFactory"
    )
    public void incomeMessage(List<ConsumerRecord<String, String>> datas, Acknowledgment acknowledgment) {
        try {
            List<String> binlogContents = datas.stream().map(ConsumerRecord::value)
                    .collect(Collectors.toList());
            List<Long> voucherIds = processVoucher(INCOME_BILL, binlogContents);
            acknowledgment.acknowledge();
            log.debug("收入账消息保存并确定消费成功，消息凭证ID: {}", JSON.toJSONString(voucherIds));

            if (CollUtil.isNotEmpty(voucherIds))
                pipelineExecutor.execute(voucherIds);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "收入账binlog消息处理异常");
            throw e;
        }
    }

    @KafkaListener(id = "settleAutoIncrement",
            groupId = "${spring.kafka.settle.consumer.group-id}",
            topics = {"${spring.kafka.settle.consumer.topic}"},
            containerFactory = "settleKafkaContainerFactory"
    )
    public void settleMessage(List<ConsumerRecord<String, String>> datas, Acknowledgment acknowledgment) {
        try {
            List<String> binlogContents = datas.stream().map(ConsumerRecord::value)
                    .collect(Collectors.toList());
            List<Long> voucherIds = processVoucher(BILL_SETTLE, binlogContents);
            acknowledgment.acknowledge();
            log.debug("清结算消息保存并确定消费成功，消息凭证ID: {}", JSON.toJSONString(voucherIds));

            if (CollUtil.isNotEmpty(voucherIds))
                pipelineExecutor.execute(voucherIds);
        }catch (Exception e) {
            log.error(SYSTEM_ERROR, "清结算binlog消息处理异常");
            throw e;
        }
    }

    private List<Long> processVoucher(String bizDomainCode, List<String> binlogContents) {

        if (isStop(bizDomainCode)) {
            log.warn("业务域已经制动，不处理消息，业务域编码:{}", bizDomainCode);
            return Collections.emptyList();
        }

        List<BinlogBO> binlogBOS = binlogContents.stream()
                .map(binlogContent -> {
                    BinlogBO binlogBO = JSON.parseObject(binlogContent, BinlogBO.class);
                    binlogBO.setOriginalContent(binlogContent);
                    return binlogBO;
                })
                .collect(Collectors.toList());
        return collectionVoucherService.saveIfNecessary(binlogBOS, bizDomainCode);
    }

    /***
     * 制动判断, 此制动一旦设置就表示所有的消息能顺利的消费，但是不会执行任何业务逻辑
     * @param bizDomainCode 业务域编码
     */
    private boolean isStop(String bizDomainCode) {
        if (StrUtil.isBlank(bizDomainCode))
            return Boolean.FALSE;

        try {
            RBucket<Boolean> bucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, COLLECTION_STOP_FLAG, bizDomainCode));
            Boolean flag = bucket.get();
            if(Objects.isNull(flag)){
                return Boolean.FALSE;
            }
            return flag;
        }catch (Exception e){
            log.error(COLLECTION_DATA_MARKER, "制动判断异常",e);
            return Boolean.FALSE;
        }
    }

    @KafkaListener(id = "payBillCollect",
            topics = {"${spring.kafka.pay.consumer.topic}"},
            containerFactory = "payBillKafkaContainerFactory",
            groupId = "${spring.kafka.pay.consumer.group-id}"
    )
    public void payMessage(List<ConsumerRecord<String, String>> datas, Acknowledgment acknowledgment) {
        log.info("接收到付款单消息，消息体：{}", JSON.toJSONString(datas.stream().map(ConsumerRecord::value).collect(Collectors.toList())));

        List<String> binlogContents = datas.stream().map(ConsumerRecord::value)
                .collect(Collectors.toList());
        List<Long> voucherIds = processVoucher(PAY_BILL, binlogContents);
        acknowledgment.acknowledge();
        log.debug("接收到付款单消息并确定消费成功，消息凭证ID: {}", JSON.toJSONString(voucherIds));

        if (CollUtil.isNotEmpty(voucherIds))
            pipelineExecutor.execute(voucherIds);

    }


    @KafkaListener(id = "iotCollect",
            topics = {"${spring.kafka.iot.consumer.topic}"},
            containerFactory = "iotKafkaContainerFactory",
            groupId = "${spring.kafka.iot.consumer.group-id}"
    )
    public void iotMessage(List<ConsumerRecord<String, String>> datas, Acknowledgment acknowledgment) {
        log.info("接收到分账消息，消息体：{}", JSON.toJSONString(datas.stream().map(ConsumerRecord::value).collect(Collectors.toList())));

        List<String> binlogContents = datas.stream().map(ConsumerRecord::value)
                .collect(Collectors.toList());
        List<Long> voucherIds = processVoucher("iot_bill", binlogContents);
        acknowledgment.acknowledge();
        log.debug("接收到分账消息并确定消费成功，消息凭证ID: {}", JSON.toJSONString(voucherIds));

        if (CollUtil.isNotEmpty(voucherIds))
            pipelineExecutor.execute(voucherIds);

    }
}
