package so.dian.huashan.collection.service.thirdparty.xiyouke;

import cn.hutool.core.date.DatePattern;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.service.thirdparty.AbstractChannelApi;
import so.dian.huashan.collection.service.thirdparty.BillRequestParam;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.utils.HutoolPostUtil;

import java.util.*;

@Slf4j
@Service
public class XiyoukeChannelApi extends AbstractChannelApi {

    protected String getDestFilePath(BillRequestParam requestParam) {
        return getDirectory(requestParam) + "/"
                + this.getFileName(requestParam.getConfig().getPayType(), requestParam.getBillDate(), requestParam.getConfig().getChannelId());
    }

    protected String getFileName(ThirdpartyPayType payType, Date billDate, String param) {
        String billDateStr = LocalDateUtils.format(billDate, DatePattern.PURE_DATE_FORMAT);
        return payType.name().toLowerCase() + "_" + param + "_" + billDateStr + fileCsvExt;
    }

    public List<String> toPullBillFile(BillRequestParam requestParam) throws Exception {

        String billFilePath = getDestFilePath(requestParam);
        log.info("XiyoukeChannelApi get billFilePath:{}",billFilePath);
        Long fileSize = HutoolPostUtil.getDownload(requestParam.getConfig().getRequestUrl(), billFilePath);
        log.info("XiyoukeChannelApi get fileSize:{}",fileSize);
        List<String> billFiles = Lists.newArrayList();
        billFiles.add(billFilePath);

        return billFiles;
    }

}
