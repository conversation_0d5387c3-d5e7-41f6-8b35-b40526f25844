package so.dian.huashan.collection.service.thirdparty.account;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.enums.DeletedEnum;
import so.dian.huashan.collection.mapper.dos.WxSplitBillDetailDO;
import so.dian.huashan.common.utils.DateUtil;

import java.math.BigDecimal;

@Slf4j
@Service
@StepScope
public class AccountshareFileItemProcessor implements ItemProcessor<AccountshareBill, WxSplitBillDetailDO> {
    @Override
    public WxSplitBillDetailDO process(AccountshareBill accountShareBill) throws Exception {
        WxSplitBillDetailDO wxSplitBillDetailDO = new WxSplitBillDetailDO();
        wxSplitBillDetailDO.setSettleDetailNo(accountShareBill.getSpiltDescription());
        wxSplitBillDetailDO.setCheckStatus(0);
        wxSplitBillDetailDO.setSplitDate(DateUtil.parseLongDateString(accountShareBill.getSplitDate()));
        wxSplitBillDetailDO.setSplitInitiator(accountShareBill.getSplitInitiator());
        wxSplitBillDetailDO.setSplitSourceId(accountShareBill.getSplitSourceId());
        wxSplitBillDetailDO.setSplitReceiveId(accountShareBill.getSpiltReceiveId());
        wxSplitBillDetailDO.setSplitOrderNo(accountShareBill.getSpiltOrderNo());
        wxSplitBillDetailDO.setSplitDetailNo(accountShareBill.getSpiltDetailNo());
        wxSplitBillDetailDO.setMchSplitOrderNo(accountShareBill.getMchSplitOrderNo());
        BigDecimal orderAmt =  new BigDecimal(accountShareBill.getOrderAmt()).multiply(new BigDecimal(100));
        wxSplitBillDetailDO.setOrderAmt(orderAmt.longValue());
        BigDecimal spiltAmt =  new BigDecimal(accountShareBill.getSpiltAmt()).multiply(new BigDecimal(100));
        wxSplitBillDetailDO.setSplitAmt(spiltAmt.longValue());
        wxSplitBillDetailDO.setBizType(accountShareBill.getBizType());
        wxSplitBillDetailDO.setDeleted(DeletedEnum.NOT_DELETED.getCode());
        return wxSplitBillDetailDO;
    }
}
