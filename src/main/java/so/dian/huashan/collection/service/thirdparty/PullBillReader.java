package so.dian.huashan.collection.service.thirdparty;

import cn.hutool.core.date.DatePattern;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemReader;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.common.enums.BizErrorCode;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.Objects;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/03/17 17:38
 * @description:
 */
@Slf4j
@Service
@StepScope
public class PullBillReader implements ItemReader<BillRequestParam> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    @Value("#{jobParameters[billDate]}")
    private String billDate;

    @Value("#{jobParameters[url]}")
    private String url;
    @Value("#{jobParameters[channelId]}")
    private String channelId;
    @Value("#{jobParameters[mchType]}")
    private String mchType;
    private volatile boolean read;

    @Override
    public BillRequestParam read() {

        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (Objects.isNull(payType)) {
            log.warn("渠道支付类型为空，直接跳过结束");
            return null;
        }

        ApiConfig config = ApiConfig.getConfig(payType);
        if (ThirdpartyPayType.XIYOUKE.equals(payType)){
            config = new ApiConfig();
            config.setRequestUrl(url);
            config.setChannelId(channelId);
            config.setPayType(payType);
        } else {
            if (ThirdpartyPayType.WEIXIN_PARTNER.equals(payType)){
                config.setMchType(Integer.valueOf(mchType));
            }
            if (Objects.isNull(config)) {
                throw BizException.create(BizErrorCode.UNKNOWN_CONFIG);
            }
        }
        if (read) {
            return null;
        }

        log.info("账单拉取参数开始读取");
        read = true;
        return BillRequestParam.builder().config(config)
                .billDate(DateUtil.parse(billDate, DatePattern.NORM_DATE_PATTERN)).build();
    }
}
