package so.dian.huashan.collection.service.thirdparty.weixin.partner;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.MultiResourceItemReader;
import org.springframework.batch.item.support.SynchronizedItemStreamReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.enums.MchTypeEnum;
import so.dian.huashan.collection.service.entity.ThirdPartyBillFileBO;
import so.dian.huashan.collection.service.thirdparty.ChannelBillApiService;
import so.dian.huashan.collection.service.thirdparty.weixin.WeixinBill;
import so.dian.huashan.collection.service.thirdparty.weixin.WeixinBillFieldSetMapper;
import so.dian.huashan.common.enums.ThirdpartyChannel;
import so.dian.huashan.common.enums.ThirdpartyPayType;

import java.util.*;

/**
 * @author: miaoshuai
 * @create: 2023/03/24 14:07
 * @description:
 */
@Slf4j
@Service
@StepScope
public class WeixinpartnerBillFileReader extends SynchronizedItemStreamReader<WeixinpartnerBill> {

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    @Value("#{jobParameters[billDate]}")
    private String billDate;

    @Value("#{jobParameters[mchType]}")
    private String mchType;

    @Autowired
    @Qualifier("channelBillApiService")
    private ChannelBillApiService billApiService;

    private boolean isLoadFile;

    private final Map<String, ThirdPartyBillFileBO> pathAdFile = new HashMap<>();

    private final MultiResourceItemReader<WeixinpartnerBill> multiResourceReader = new MultiResourceItemReader<>();

    @Override
    public synchronized WeixinpartnerBill read() throws Exception {
        if (!isLoadFile) {
            log.warn("微信账单文件没有加载");
            return null;
        }

        WeixinpartnerBill weixinPartnerBill = super.read();
        if (!Objects.isNull(weixinPartnerBill)) {
            String filename = Optional.ofNullable(multiResourceReader.getCurrentResource()).map(Resource::getFilename).orElse(null);
            weixinPartnerBill.setBillFileId(pathAdFile.get(filename).getId());
        }
        return weixinPartnerBill;
    }

    @Override
    public void afterPropertiesSet() {

        FlatFileItemReader<WeixinpartnerBill> flatFileItemReader = new FlatFileItemReader<>();
        multiResourceReader.setResources(new Resource[0]);
        multiResourceReader.setDelegate(flatFileItemReader);
        setDelegate(multiResourceReader);

        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (!Objects.isNull(payType) && ThirdpartyChannel.WEIXINPARTNER.equals(payType.getChannel())) {

            WeixinpartnerBillFieldSetMapper weixinPartnerBillFieldSetMapper = new WeixinpartnerBillFieldSetMapper();
            flatFileItemReader.setLineMapper(weixinPartnerBillFieldSetMapper);

            Integer billDateInt = Integer.valueOf(DateUtil.format(DateUtil.parseDate(billDate), DatePattern.PURE_DATE_PATTERN));
            List<ThirdPartyBillFileBO> billFiles = billApiService.listBillFile(payType, billDateInt);
            if (CollectionUtils.isNotEmpty(billFiles)) {
                List<Resource> resources = Lists.newArrayList();
                for (ThirdPartyBillFileBO billFile : billFiles) {
                    if (billFile.getFilePath().contains("total")) {
                        continue;
                    }
                    if (!mchType.equals(billFile.getFilePath().split("_")[3])){
                        continue;
                    }
                    Resource resource = new FileSystemResource(billFile.getFilePath());
                    resources.add(resource);
                    pathAdFile.put(resource.getFilename(), billFile);
                }
                multiResourceReader.setResources(resources.toArray(new Resource[]{}));
                isLoadFile = true;
                log.info("微信账单文件加载完成");
            }

        }
    }

}
