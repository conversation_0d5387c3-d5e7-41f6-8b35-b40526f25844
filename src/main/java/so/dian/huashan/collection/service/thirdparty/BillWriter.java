package so.dian.huashan.collection.service.thirdparty;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.mapper.ThirdPartyBillMapper;
import so.dian.huashan.collection.mapper.dos.ThirdPartyBillDO;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 账务账单文件落库
 *
 */
@Slf4j
@Service
@StepScope
public class BillWriter implements ItemWriter<ThirdPartyBillDO> {

    @Resource
    private ThirdPartyBillMapper thirdPartyBillMapper;

    @Value("#{jobParameters[billDate]}")
    private String billDate;

    @Override
    public void write(List<? extends ThirdPartyBillDO> items) {

        if (CollUtil.isEmpty(items)) {
            return;
        }
        Date start = DateUtil.date();
        List<ThirdPartyBillDO> List = items.stream().map(item -> {
            item.setStatus(0);
            Integer checkDate = Integer.valueOf(DateUtil.format(DateUtil.parseDate(billDate), DatePattern.PURE_DATE_PATTERN));
            item.setCheckDate(checkDate);
            return item;
        }).collect(Collectors.toList());

        thirdPartyBillMapper.insertBatch(List);
        log.info(">>> 写入一批数据结束, 数据大小：{}, 耗时：{}", Optional.ofNullable(items).map(java.util.List::size).orElse(0), DateUtil.formatBetween(start, DateUtil.date()));
    }
}
