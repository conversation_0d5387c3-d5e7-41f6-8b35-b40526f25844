package so.dian.huashan.collection.service.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.Executor;

import static com.alibaba.nacos.api.PropertyKeyConst.NAMESPACE;
import static com.alibaba.nacos.api.PropertyKeyConst.SERVER_ADDR;

/**
 * @author: miaoshuai
 * @create: 2023/03/20 10:47
 * @description:
 */
@Slf4j
@Component
public class ThirdpartyConfigListener implements ApplicationListener<ContextRefreshedEvent>, Listener {

    private ConfigService configService;

    private static final String DATA_ID = "huashan-thirdparty-config.json";

    private static final String GROUP = "DEFAULT_GROUP";

    private static final String LOCAL_CONFIG = "classpath:huashan-thirdparty-config-local.json";

    @Value("${spring.cloud.nacos.config.server-addr}")
    private String configAddr;

    @Value("${spring.cloud.nacos.config.namespace}")
    private String namespace;

    @Value("${spring.cloud.nacos.config.enabled}")
    private Boolean enabled;

    @PostConstruct
    public void init() throws NacosException {
        if (enabled) {
            Properties properties = new Properties();
            properties.put(SERVER_ADDR, Objects.toString(this.configAddr, ""));
            properties.put(NAMESPACE, Objects.toString(this.namespace, ""));
            configService = NacosFactory.createConfigService(properties);
        }
    }

    @Override
    public void onApplicationEvent(@NonNull ContextRefreshedEvent event) {

        try {
            if (Objects.isNull(configService)) {
                InputStream in = this.getClass().getClassLoader().getResourceAsStream(DATA_ID);
                StringWriter writer = new StringWriter();
                IOUtils.copy(in, writer, StandardCharsets.UTF_8.name());
                String config = writer.toString();
                receiveConfigInfo(config);
            }else {
                String config = configService.getConfig(DATA_ID, GROUP, 3000L);
                receiveConfigInfo(config);
                configService.addListener(DATA_ID, GROUP, this);
            }

        } catch (NacosException e) {
            log.error("ConfigService can't add Listener for dataId : " + DATA_ID + " , groupId : " + GROUP, e);
            throw new RuntimeException(e);
        } catch (IOException e) {
            log.error("Load config file is fail, file:[{}]", LOCAL_CONFIG, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Executor getExecutor() {
        return null;
    }

    @Override
    public void receiveConfigInfo(String configInfo) {
        List<ApiConfig> apiConfigs = JSON.parseArray(configInfo, ApiConfig.class);
        apiConfigs.forEach(apiConfig -> ApiConfig.addConfig(apiConfig.getPayType(), apiConfig));
    }
}
