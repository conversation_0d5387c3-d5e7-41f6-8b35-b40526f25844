package so.dian.huashan.collection.service.thirdparty.alipay;

import cn.hutool.core.util.StrUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;

/**
 * @Author: ahuang
 * @CreateTime: 2024-12-12 14:48
 * @Description:前端分账过滤专用 只返回前端分账订单
 */
@Slf4j
@Service
@StepScope
public class AlipayBillFilterProcessor implements ItemProcessor<AlipayBill, AlipayBill> {

    /**
     * 在 Spring Batch 中，当 ItemProcessor 的 process 方法返回 null 时，该记录会自动被过滤掉，不会传递到 ItemWriter。
     * 因此，ItemWriter 不需要额外判断 null。
     * @param alipayBill
     * @return
     */
    @Override
    public AlipayBill process(@NonNull AlipayBill alipayBill) {
        try {
            String merchantNo = alipayBill.getMerchantNo();
            // 过滤条件：只保留前端分账的订单 订单前缀PFZ，退款订单前缀RFZ
            if (StrUtil.isNotBlank(merchantNo) && (merchantNo.startsWith("PFZ") || merchantNo.startsWith("RFZ"))) {
                return alipayBill; // 保留符合条件的记录
            } else {
                return null; // 返回 null 表示过滤掉此记录
            }
        } catch (Exception e) {
            log.error("过滤支付宝账单时出现异常，alipayBill:{}", alipayBill, e);
            throw e;
        }
    }
}
