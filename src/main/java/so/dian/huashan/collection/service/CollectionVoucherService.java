package so.dian.huashan.collection.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.huashan.collection.facade.CollectionVoucherFacade;
import so.dian.huashan.collection.mapper.dos.CollectionVoucherDO;
import so.dian.huashan.collection.service.entity.BinlogBO;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.common.enums.CollectionVoucherStatus;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: miaoshuai
 * @create: 2023/12/25 09:38
 * @description:
 */
@Slf4j
@Service
public class CollectionVoucherService {

    @Autowired
    private CollectionVoucherFacade collectionVoucherFacade;

    @Autowired
    private CollectionPipelineService pipelineService;

    public List<CollectionVoucherBO> listRetryableVoucher(Long minId, Integer queryCount, List<Byte> statuses) {

        List<CollectionVoucherStatus> voucherStatuses = Lists.newArrayList();
        if (CollUtil.isEmpty(statuses)) {
            voucherStatuses.add(CollectionVoucherStatus.FAIL);
            voucherStatuses.add(CollectionVoucherStatus.INITIAL);
        }else {
            voucherStatuses.addAll(statuses.stream().map(CollectionVoucherStatus::from).collect(Collectors.toList()));
        }

        return collectionVoucherFacade.listRetryableVoucher(minId, queryCount, voucherStatuses);
    }

    public boolean seizeVoucher(Long voucherId, Integer version, CollectionVoucherStatus status) {
        if (Objects.isNull(voucherId) || Objects.isNull(version) || Objects.isNull(status))
            return Boolean.FALSE;

        int update = collectionVoucherFacade.update(voucherId, status, version);
        if (update > 0)
            return Boolean.TRUE;

        return Boolean.FALSE;
    }

    public void modify(CollectionVoucherBO voucherBO) {
        if (Objects.isNull(voucherBO) || Objects.isNull(voucherBO.getId()))
            return;
        collectionVoucherFacade.update(voucherBO);
    }

    @Transactional
    public List<Long> saveIfNecessary(List<BinlogBO> binlogBOS, String bizDomainCode) {
        if (CollUtil.isEmpty(binlogBOS) || StrUtil.isBlank(bizDomainCode))
            return Collections.emptyList();

        List<String> tables = pipelineService.loadTables(bizDomainCode);
        if (CollUtil.isEmpty(tables)) {
            log.warn("查询不到相应的管道采集表,业务编码：{}", bizDomainCode);
            return Collections.emptyList();
        }

        List<CollectionVoucherBO> voucherBOS = binlogBOS.stream()
                .filter(binlogBO -> tables.stream().anyMatch(table -> table.equals(binlogBO.getTable())))
                .map(CollectionVoucherBO::from)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(voucherBOS)) {
            return Collections.emptyList();
        }

        List<CollectionVoucherDO> voucherDOS = voucherBOS.stream().map(CollectionVoucherBO::toDO).collect(Collectors.toList());
        collectionVoucherFacade.batchSave(voucherDOS);

        return voucherDOS.stream().map(CollectionVoucherDO::getId).collect(Collectors.toList());
    }
}
