package so.dian.huashan.collection.service.thirdparty.alipay;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import so.dian.huashan.common.constant.ChannelConstant;

/**
 * 支付宝账务账单文件账单csv文件字段映射
 *
 * <AUTHOR>
 * @date 2020-09-11 17:49
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备********号
 */
@Slf4j
public class AlipayBillFieldSetMapper extends DefaultLineMapper<AlipayBill> implements FieldSetMapper<AlipayBill> {

    private static final String reg = "^\\d+$";

    public AlipayBillFieldSetMapper() {
        DelimitedLineTokenizer lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setDelimiter(",");
        lineTokenizer.setStrict(false);
        lineTokenizer.setNames(ChannelConstant.alipayAccountBillColumn.split(","));
        setLineTokenizer(lineTokenizer);
        setFieldSetMapper(this);
    }

    @Override
    @NonNull
    public AlipayBill mapFieldSet(@NonNull FieldSet fieldSet) {

        //第一列不是纯数字跳过。
        if (!fieldSet.readString("accountSeqNo").matches(reg)) {
            log.info("跳过行：" + JSON.toJSONString(fieldSet));
            throw new FlatFileParseException(fieldSet.readString("accountSeqNo"), StrUtil.EMPTY);
        }

        AlipayBill result = new AlipayBill();
        result.setAccountSeqNo(getTextString(fieldSet, "accountSeqNo"));
        result.setBizSeqNo(getTextString(fieldSet, "bizSeqNo"));
        result.setMerchantNo(getTextString(fieldSet, "merchantNo"));
        result.setProductName(getTextString(fieldSet, "productName"));
        result.setOccurTime(getTextString(fieldSet, "occurTime"));
        result.setAccountNo(getTextString(fieldSet, "accountNo"));
        result.setInAmount(getTextString(fieldSet, "inAmount"));
        result.setOutAmount(getTextString(fieldSet, "outAmount"));
        result.setBalance(getTextString(fieldSet, "balance"));
        result.setChannel(getTextString(fieldSet, "channel"));
        result.setBizType(getTextString(fieldSet, "bizType"));
        result.setRemark(getTextString(fieldSet, "remark"));

        return result;
    }

    private String getTextString(FieldSet fieldSet, String key) {
        String replace = fieldSet.readString(key).replace("`", "");
        return StrUtil.isBlank(replace) ? null : replace;
    }





}
