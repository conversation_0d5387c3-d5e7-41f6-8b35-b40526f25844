package so.dian.huashan.collection.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import so.dian.huashan.collection.controller.request.PullThirdBillReq;
import so.dian.huashan.collection.facade.CollectionVoucherFacade;
import so.dian.huashan.collection.service.entity.BinlogBO;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.pipeline.ExecutorCommand;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.utils.DateUtil;

import java.util.Objects;

import static so.dian.huashan.common.exception.LogMarkerFactory.COLLECTION_DATA_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/09/28 10:32
 * @description:
 */
@Slf4j
@Service
public class ManualCompensateService {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ThirdpartyPullService thirdpartyPullService;

    @Autowired
    private CollectionVoucherFacade voucherFacade;

    @Autowired
    private CollectionPipelineService pipelineService;

    public CollectionProcessResult compensateBinlog(String binlogContent) {
        if (StrUtil.isBlank(binlogContent))
            return CollectionProcessResult.fail();

        BinlogBO binlogBO = JSON.parseObject(binlogContent, BinlogBO.class);
        CollectionVoucherBO voucherBO = CollectionVoucherBO.from(binlogBO);
        voucherFacade.save(voucherBO);
        PipelineBO pipelineBO = new PipelineBO(binlogBO, voucherBO.getId());
        if (!pipelineBO.verify()) {
            log.warn(COLLECTION_DATA_MARKER, "手动数据采集处理,参数校验失败,表名{}", pipelineBO.getBinLog().getTable());
            return CollectionProcessResult.fail();
        }

        CollectionPipelineBO collectionPipelineBO = pipelineService.find(pipelineBO.getBinLog().getTable());
        if (Objects.isNull(collectionPipelineBO)) {
            log.warn(COLLECTION_DATA_MARKER, "手动数据采集处理,没有获取到对应的管道注册信息,表名{}", pipelineBO.getBinLog().getTable());
            return CollectionProcessResult.fail();
        }

        CollectionPipeline collectionPipeline = applicationContext.getBean(collectionPipelineBO.getStrategyCode(), CollectionPipeline.class);
        ExecutorCommand command = new ExecutorCommand(collectionPipeline);
        return command.execute(voucherBO);
    }

    public void pullThirdBill(PullThirdBillReq billReq) {
        ThirdpartyPayType payType = ThirdpartyPayType.fromName(billReq.getPayType());
        Integer indexDate = billReq.getStartBillDate();
        for (;;) {
            log.info("手动拉取三方账单，payType:{}, billDate:{}",payType.name(), indexDate);
            thirdpartyPullService.pull(payType, indexDate.toString());
            if (indexDate.compareTo(billReq.getEndBillDate()) >= 0)
                break;
            indexDate = DateUtil.parseDateYyyyMMdd2Int(DateUtil.offsetDay(DateUtil.format2DateYyyyMMdd(indexDate), 1));
        }
        log.info("手动拉取三方账单结束");
    }
}
