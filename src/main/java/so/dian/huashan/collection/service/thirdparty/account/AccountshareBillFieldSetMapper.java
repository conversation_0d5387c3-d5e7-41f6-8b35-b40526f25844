package so.dian.huashan.collection.service.thirdparty.account;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import so.dian.common.logger.util.StringUtils;
import so.dian.huashan.common.constant.ChannelConstant;

import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class AccountshareBillFieldSetMapper extends DefaultLineMapper<AccountshareBill> implements FieldSetMapper<AccountshareBill> {

    public AccountshareBillFieldSetMapper() {
        DelimitedLineTokenizer lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setDelimiter(",");
        lineTokenizer.setStrict(false);
        lineTokenizer.setNames(ChannelConstant.channelShareAccountBillColumn.split(","));
        setLineTokenizer(lineTokenizer);
        setFieldSetMapper(this);
    }
    @Override
    @NonNull
    public AccountshareBill mapFieldSet(@NonNull FieldSet fieldSet) {

        AccountshareBill result = new AccountshareBill();
        if (!fieldSet.readString("splitDate").contains("-")) {
            log.info("跳过行：" + JSON.toJSONString(fieldSet));
            throw new FlatFileParseException(fieldSet.readString("splitDate"), StrUtil.EMPTY);
        }
        //渠道订单号
        result.setSplitDate(getTextString(fieldSet, "splitDate"));
        //企业名称
        result.setSplitInitiator(getTextString(fieldSet, "splitInitiator"));
        //服务商
        result.setSplitSourceId(getTextString(fieldSet, "splitSourceId"));
        //姓名
        result.setWeixinOrderNo(getTextString(fieldSet, "weixinOrderNo"));
        //银行卡
        result.setSpiltOrderNo(getTextString(fieldSet, "spiltOrderNo"));
        //电话
        result.setSpiltDetailNo(getTextString(fieldSet, "spiltDetailNo"));
        //身份证
        result.setMchSplitOrderNo(getTextString(fieldSet, "mchSplitOrderNo"));
        //交易金额
        result.setOrderAmt(getTextString(fieldSet, "orderAmt"));
        //服务费
        result.setSpiltReceiveId(getTextString(fieldSet, "spiltReceiveId"));
        //外部订单状态
        result.setSpiltAmt(getTextString(fieldSet, "spiltAmt"));
        // 收支类型
        result.setBizType(getTextString(fieldSet, "bizType"));
        //发放通道
        result.setStatusStr(getTextString(fieldSet, "statusStr"));
        //创建时间
        result.setSpiltDescription(getTextString(fieldSet, "spiltDescription"));
        //来源
        result.setRemark(getTextString(fieldSet, "remark"));
        return result;
    }

    private String getTextString(FieldSet fieldSet, String key) {
        String replace = fieldSet.readString(key).replace("`", "");
        return StrUtil.isBlank(replace) ? null : replace;
    }
}
