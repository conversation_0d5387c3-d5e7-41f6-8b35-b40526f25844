package so.dian.huashan.collection.service.pipeline;

import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.CollectionVoucherBO;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/26 11:06
 * @description:
 */
public class RetryExecutorCommand extends ExecutorCommand {
    public RetryExecutorCommand(CollectionPipeline pipeline) {
        super(pipeline);
    }

    @Override
    protected CollectionVoucherBO complete(CollectionVoucherBO voucherBO, CollectionProcessResult process) {
        CollectionVoucherBO modifyVoucherBO = super.complete(voucherBO, process);
        modifyVoucherBO.setRetryTime(voucherBO.getRetryTime() + 1);
        return modifyVoucherBO;
    }
}
