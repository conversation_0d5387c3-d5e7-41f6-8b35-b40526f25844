package so.dian.huashan.collection.service.income;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.entity.BinlogBO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.enums.BillSourceEnum;
import so.dian.huashan.common.mapper.lhc.entity.HeraTransOrderBO;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: miao<PERSON><PERSON>
 * @create: 2023/03/21 15:18
 * @description:
 */
@Slf4j
@Service
public class HeraTransOrderPipeline extends CollectionPipeline {

    @Resource
    private IncomeOriginalInfoMapper incomeOriginalInfoMapper;

    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        if(StringUtils.isBlank(pipelineBO.getBinLog().getAfter())){
            return true;
        }
        JSONObject data = JSON.parseObject(pipelineBO.getBinLog().getAfter());
        String tradeStatus = data.getString("trade_status");
        if(!tradeStatus.equals("3")){
            return true;
        }
        return false;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        BinlogBO binLog = pipelineBO.getBinLog();
        Long voucherId = pipelineBO.getVoucherId();
        HeraTransOrderBO heraTransOrderBO = JSON.parseObject(binLog.getAfter(), HeraTransOrderBO.class);
        IncomeOriginalInfoDO incomeOriginalInfoDO = new IncomeOriginalInfoDO();
        incomeOriginalInfoDO.setDataSource(2);
        incomeOriginalInfoDO.setPayType(heraTransOrderBO.getPayType());
        incomeOriginalInfoDO.setBizType(heraTransOrderBO.getBizType());
        Integer happenDate = heraTransOrderBO.getPayTime() != null ? Integer.valueOf(LocalDateUtils.format(heraTransOrderBO.getPayTime(), DatePattern.PURE_DATE_PATTERN)) : Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
        incomeOriginalInfoDO.setHappenDate(happenDate);
        if(StringUtils.isBlank(heraTransOrderBO.getOutPayNo())){
//            log.warn("支付单号为空，不处理这样的数据,id = {}",id);
            return CollectionProcessResult.success().voucherId(voucherId);
        }
        incomeOriginalInfoDO.setPayNo(heraTransOrderBO.getOutPayNo());
        if(heraTransOrderBO.getPayChannel().equals(1)){
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.WECHAT.getId());
        }else if(heraTransOrderBO.getPayChannel().equals(2)){
            incomeOriginalInfoDO.setPayWay(BillSourceEnum.ALIPAY.getId());
        }else{
            incomeOriginalInfoDO.setPayWay(heraTransOrderBO.getPayChannel());
        }
        incomeOriginalInfoDO.setStatus(1);
        incomeOriginalInfoDO.setTradeType("out");
        incomeOriginalInfoDO.setTradeAmount(heraTransOrderBO.getPayAmount().intValue());
        incomeOriginalInfoDO.setTradeTime(heraTransOrderBO.getPayTime() == null ? new Date() : heraTransOrderBO.getPayTime());
        incomeOriginalInfoDO.setTradeNo(heraTransOrderBO.getTradeNo());
        incomeOriginalInfoDO.setDianPayNo(heraTransOrderBO.getTradeNo());
        // dataSource + bizType + payNo + tradeType + payWay
        String key  = incomeOriginalInfoDO.getDataSource() + "_" +incomeOriginalInfoDO.getBizType() + "_" + incomeOriginalInfoDO.getPayNo() + "_" + incomeOriginalInfoDO.getTradeType() + "_" + incomeOriginalInfoDO.getPayWay();

        incomeOriginalInfoDO.setIdempotentNo(key);
        incomeOriginalInfoDO.setVoucherId(voucherId);
        // 查询数据库
        IncomeOriginalInfoDO incomeOriginalInfoDO1 = incomeOriginalInfoMapper.selectByIdempotentNo(key);
        if(incomeOriginalInfoDO1 == null){
            incomeOriginalInfoDO.init();
            incomeOriginalInfoMapper.insertSelective(incomeOriginalInfoDO);
        }else{
            incomeOriginalInfoDO.setId(incomeOriginalInfoDO1.getId());
            incomeOriginalInfoDO.setGmtUpdate(System.currentTimeMillis());
            incomeOriginalInfoMapper.updateByPrimaryKeySelective(incomeOriginalInfoDO);
        }

        return CollectionProcessResult.success().voucherId(voucherId).addIncomeOriginalId(incomeOriginalInfoDO.getId());
    }
}
