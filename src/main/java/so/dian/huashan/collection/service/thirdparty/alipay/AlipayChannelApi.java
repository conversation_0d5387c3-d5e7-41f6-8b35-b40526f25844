package so.dian.huashan.collection.service.thirdparty.alipay;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayDataDataserviceBillDownloadurlQueryRequest;
import com.alipay.api.response.AlipayDataDataserviceBillDownloadurlQueryResponse;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import so.dian.himalaya.boot.util.EnvUtils;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.collection.service.thirdparty.AbstractChannelApi;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.collection.service.thirdparty.BillRequestParam;
import so.dian.huashan.common.constant.ChannelConstant;
import so.dian.huashan.common.enums.BizErrorCode;
import so.dian.huashan.common.utils.HutoolPostUtil;
import so.dian.huashan.common.utils.ThirdPartyBillPathUtil;
import so.dian.huashan.common.utils.ZipUtil;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 支付宝渠道api
 */
@Slf4j
@Service
public class AlipayChannelApi extends AbstractChannelApi {

    private static final String   format       = "json";

    private static final String   charset      = "gbk";

    private static final String  SIGNCUSTOMER = "signcustomer";

    private static final String PUBLIC_CERT_APPID = "2019040363771382,2021005106623226,2021005108667174,2021005128648185";

    //支付宝账单不存在
    private static final String NON_EXISTENT_CODE = "40004";

    @Value("${huashan.thirdparty.bill.delete-zip:true}")
    private Boolean deleteZip;

    @Override
    public List<String> toPullBillFile(BillRequestParam requestParam) throws Exception {
        // 账单文件路径
        String billFile = getDestFilePath(requestParam);
        List<String> billFiles = Lists.newArrayList();
        // 压缩文件路径
        String billZipFile = getZipDestFilePath(requestParam);

        // 账期存放的目录
        String billDateDirectory = getDirectory(requestParam);

        String downloadUrl = getBillDownloadUrl(requestParam);

        if(StringUtils.isEmpty(downloadUrl)){
            return Lists.newArrayList();
        }
        Long fileSize = HutoolPostUtil.getDownload(downloadUrl, billZipFile);

        log.info("支付宝账单文件拉取，下载链接：{}, 文件大小：{} ", downloadUrl, fileSize);

        //获取zip文件 解压
        File file = ZipUtil.getFileByExt(billDateDirectory, fileZipExt);
        if (!Objects.isNull(file) && file.exists()) {
            ZipUtil.unzip(file.getAbsolutePath(), billDateDirectory, Charset.forName(charset));

            if (deleteZip) {
                boolean delete = file.delete();
                log.info("支付宝账单压缩文件删除结果[{}]，billDateDirectory:[{}]", delete, billDateDirectory);
            }
        }

        List<File> fileList = loadFiles(requestParam);
        if (CollectionUtils.isNotEmpty(fileList)) {
            int i =1;
            for (File dfile : fileList) {
                log.info("拉取到的支付宝账单文件名[{}]", dfile.getName());

                if (!fileCsvExt.equals(ZipUtil.getFileExtName(dfile.getName())) && deleteZip) {
                    boolean delete = dfile.delete();
                    log.info("支付宝账单压缩文件删除结果[{}]，file:[{}]", delete, dfile.getAbsolutePath());
                    continue;
                }

                if (fileZipExt.equals(ZipUtil.getFileExtName(dfile.getName())))
                    continue;

                if (!(dfile.getName().contains("(")) && !(dfile.getName().contains(")"))) {
                    // 将账单文件名修改为：alipay_67_20170504.csv
                    String tmpBillFile = billFile;
                    tmpBillFile = tmpBillFile.replace(".", "_" + i + ".");
                    String fileName = tmpBillFile;
                    ZipUtil.renameFileName(dfile, fileName);
                    billFiles.add(fileName);
                    i++;

                } else {
                    // 将汇总表文件名修改为：alipay_67_20170504_total.csv
                    String totalBillFilePath = getTotalDestFilePath(requestParam);
                    ZipUtil.renameFileName(dfile, totalBillFilePath);
                    billFiles.add(totalBillFilePath);
                }
            }
        }

        return billFiles;
    }

    private String getTotalDestFilePath(BillRequestParam requestParam) {
        String destFilePath = getDestFilePath(requestParam);
        return destFilePath.replace(".", "_total.");
    }

    private String getBillDownloadUrl(BillRequestParam requestParam) throws AlipayApiException {


        AlipayClient alipayClient = getAlipayClient(requestParam);

        AlipayDataDataserviceBillDownloadurlQueryRequest request = new AlipayDataDataserviceBillDownloadurlQueryRequest();
        String billDate = DateUtil.format(requestParam.getBillDate(), DatePattern.NORM_DATE_FORMAT);

        Map<String, String> bizMap = new HashMap<>();
        bizMap.put("bill_type", SIGNCUSTOMER);
        bizMap.put("bill_date", billDate);

        request.setBizContent(JSON.toJSONString(bizMap));
        Map<String, String> paramMap = requestParam.getConfig().getParams();

        String appId = paramMap.get(ChannelConstant.ALIPAY.APP_ID.getParam());

        AlipayDataDataserviceBillDownloadurlQueryResponse response;
        if(PUBLIC_CERT_APPID.contains(appId)){
            response = alipayClient.certificateExecute(request);
        }else {
            response = alipayClient.execute(request);
        }

        if (response.isSuccess()) {
            log.info("response.getBillDownloadUrl(=)" + response.getBillDownloadUrl());
            return response.getBillDownloadUrl();
        } else if(NON_EXISTENT_CODE.equals(response.getCode())){
            log.info("对应账单不存在 response.getBillDownloadUrl(=)" + response.getBillDownloadUrl());
            return response.getBillDownloadUrl();
        } else {
            log.error("调用支付宝接口时返回失败，billDate:{}, response:{}", billDate, response.getCode());
            throw BizException.create(BizErrorCode.BILL_ALIPAY_CALL_ERROR);
        }
    }

    private AlipayClient getAlipayClient(BillRequestParam requestParam) throws AlipayApiException {
        ApiConfig config = requestParam.getConfig();
        Map<String, String> paramMap = config.getParams();

        String appId = paramMap.get(ChannelConstant.ALIPAY.APP_ID.getParam());

        boolean real=  EnvUtils.isReal()||EnvUtils.isPre();
        String privateKey = "";
        String publicKey = "";
//        if(!real && StringUtils.isNotBlank(paramMap.get(ChannelConstant.ALIPAY.PRIVATE_KEY_PATH.getParam()))){
//            privateKey = this.getClass().getClassLoader().getResource(paramMap.get(ChannelConstant.ALIPAY.PRIVATE_KEY_PATH.getParam())).getFile();
//            publicKey = this.getClass().getClassLoader().getResource(paramMap.get(ChannelConstant.ALIPAY.PUBLIC_KEY_PATH.getParam())).getFile();
//        }else{
            privateKey = paramMap.get(ChannelConstant.ALIPAY.PRIVATE_KEY_PATH.getParam());
            publicKey = paramMap.get(ChannelConstant.ALIPAY.PUBLIC_KEY_PATH.getParam());
//        }
        String appPublicKeyPath = "";
        String alipayPublicKeyPath = "";
        String rootKeyPath = "";
        appPublicKeyPath = paramMap.get(ChannelConstant.ALIPAY.APP_PUBLIC_KEY_PATH.getParam());
        alipayPublicKeyPath = paramMap.get(ChannelConstant.ALIPAY.ALIPAY_PUBLIC_KEY_PATH.getParam());
        rootKeyPath = paramMap.get(ChannelConstant.ALIPAY.ROOT_KEY_PATH.getParam());
        AlipayClient alipayClient;

        if(PUBLIC_CERT_APPID.contains(appId)){
            CertAlipayRequest certAlipayRequest = new CertAlipayRequest();

            //设置网关地址
            certAlipayRequest.setServerUrl(config.getRequestUrl());
            //设置应用Id
            certAlipayRequest.setAppId(appId);
            //设置应用私钥
            certAlipayRequest.setPrivateKey(privateKey);
            //设置请求格式，固定值json
            certAlipayRequest.setFormat("json");
            //设置字符集
            certAlipayRequest.setCharset("UTF-8");
            //设置签名类型
            certAlipayRequest.setSignType("RSA2");

            if("2021005128648185".equals(appId)){
                certAlipayRequest.setCertContent(appPublicKeyPath);
                certAlipayRequest.setRootCertContent(rootKeyPath);
                certAlipayRequest.setAlipayPublicCertContent(alipayPublicKeyPath);
            }else{
                //设置支付宝公钥证书路径
                certAlipayRequest.setAlipayPublicCertPath(alipayPublicKeyPath);
                //设置支付宝根证书路径
                certAlipayRequest.setRootCertPath(rootKeyPath);
                //设置应用公钥证书路径
                certAlipayRequest.setCertPath(appPublicKeyPath);
            }
            //构造client
            alipayClient = new DefaultAlipayClient(certAlipayRequest);

        }else{
            alipayClient = new DefaultAlipayClient(config.getRequestUrl(), appId, privateKey, format,
                    charset, publicKey,
                    paramMap.get(ChannelConstant.ALIPAY.ENCRYPT_ALGORITHM.getParam()));
        }
        return alipayClient;
    }


    @Getter
    @AllArgsConstructor
    public enum BillFileTypeEnum {

       BILL_FILE("FILE", "账单文件"), BILL_TOTAL_FILE("TOTAL_FILE", "汇总账单文件"),;

        private final String code;
        private final String status;
    }

}
