package so.dian.huashan.collection.service.income;

import cn.hutool.core.date.DatePattern;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import so.dian.himalaya.util.LocalDateUtils;
import so.dian.huashan.collection.mapper.IncomeOriginalInfoMapper;
import so.dian.huashan.collection.mapper.dos.IncomeOriginalInfoDO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.enums.BillSourceEnum;
import so.dian.huashan.common.mapper.lhc.PaymentMapper;
import so.dian.huashan.common.mapper.lhc.entity.OfflinePayDO;
import so.dian.huashan.common.mapper.lhc.entity.PaymentDO;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: miaoshuai
 * @create: 2023/03/21 15:18
 * @description:
 */
@Slf4j
@Service
public class OfflinePayPipeline extends CollectionPipeline {

    @Resource
    private IncomeOriginalInfoMapper incomeOriginalInfoMapper;

    @Resource
    private PaymentMapper paymentMapper;

    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        return false;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {

        OfflinePayDO offlinePayDO = JSON.parseObject(pipelineBO.getBinLog().getAfter(), OfflinePayDO.class);
        IncomeOriginalInfoDO incomeOriginalInfoDO = new IncomeOriginalInfoDO();
        incomeOriginalInfoDO.setDataSource(4);
        incomeOriginalInfoDO.setPayType(offlinePayDO.getPayType());
        List<PaymentDO> paymentDOS = paymentMapper.selectByOrderNo(offlinePayDO.getOrderNo());
        if(CollectionUtils.isNotEmpty(paymentDOS)){
            PaymentDO paymentDO = paymentDOS.get(0);
            incomeOriginalInfoDO.setBizType(paymentDO.getBizType());
        }else{
            incomeOriginalInfoDO.setBizType(offlinePayDO.getPayType());
        }
        Integer happenDate = offlinePayDO.getPayTime() != null ? Integer.valueOf(LocalDateUtils.format(offlinePayDO.getPayTime(), DatePattern.PURE_DATE_PATTERN)) : Integer.valueOf(LocalDateUtils.format(LocalDateUtils.date(), DatePattern.PURE_DATE_PATTERN));
        incomeOriginalInfoDO.setHappenDate(happenDate);
        if(StringUtils.isBlank(offlinePayDO.getPayNo())){
//            log.warn("支付单号为空，不处理这样的数据,id = {}",id);
            return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId());
        }
        incomeOriginalInfoDO.setPayNo(offlinePayDO.getPayNo());
        incomeOriginalInfoDO.setPayWay(BillSourceEnum.getByPayType(offlinePayDO.getPayType()).getId());
        incomeOriginalInfoDO.setStatus(1);
        incomeOriginalInfoDO.setTradeType("in");
        incomeOriginalInfoDO.setTradeAmount(offlinePayDO.getPayAmount().intValue());
        incomeOriginalInfoDO.setTradeTime(offlinePayDO.getPayTime());
        incomeOriginalInfoDO.setTradeNo(offlinePayDO.getOrderNo());
        incomeOriginalInfoDO.setDianPayNo(offlinePayDO.getOrderNo());
        // dataSource + bizType + payNo + tradeType + payWay
        String key  = incomeOriginalInfoDO.getDataSource() + "_" +incomeOriginalInfoDO.getBizType() + "_" + incomeOriginalInfoDO.getPayNo() + "_" + incomeOriginalInfoDO.getTradeType() + "_" + incomeOriginalInfoDO.getPayWay();
        incomeOriginalInfoDO.setIdempotentNo(key);
        incomeOriginalInfoDO.setVoucherId(pipelineBO.getVoucherId());
        IncomeOriginalInfoDO incomeOriginalInfoDO1 = incomeOriginalInfoMapper.selectByIdempotentNo(key);
        if(incomeOriginalInfoDO1 == null){
            incomeOriginalInfoDO.init();
            incomeOriginalInfoMapper.insertSelective(incomeOriginalInfoDO);
        }else{
            incomeOriginalInfoDO.setId(incomeOriginalInfoDO1.getId());
            incomeOriginalInfoDO.setGmtUpdate(System.currentTimeMillis());
            incomeOriginalInfoMapper.updateByPrimaryKeySelective(incomeOriginalInfoDO);
        }

        return CollectionProcessResult.success().voucherId(pipelineBO.getVoucherId()).addIncomeOriginalId(incomeOriginalInfoDO.getId());
    }
}
