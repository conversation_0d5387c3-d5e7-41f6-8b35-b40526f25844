package so.dian.huashan.collection.service.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.StepExecution;
import org.springframework.batch.core.StepExecutionListener;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.facade.DataCenterFacade;
import so.dian.huashan.common.facade.DoveFacade;
import so.dian.huashan.common.facade.remote.DataCenterClient;
import so.dian.huashan.common.utils.ThirdPartyBillPathUtil;
import so.dian.huashan.framework.sftp.SftpService;
import so.dian.mail.vo.MailInfoVo;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static org.springframework.batch.core.BatchStatus.COMPLETED;

/**
 * @Author: ahuang
 * @CreateTime: 2024-12-13 15:48
 * @Description: Step Listener for Alipay Bill Processing
 */
@Slf4j
@StepScope
@Component
public class AlipayBillStepListener implements StepExecutionListener {

    @Value("${huashan.thirdparty.bill.file:#{systemProperties['user.home']}}")
    protected String defaultDirectory;

    @Value("#{jobParameters['billDate']}")
    private String billDate; // 从 Job 参数中获取 billDate

    @Value("#{jobParameters[payType]}")
    private String payTypeName;

    @Value("${sftp.remote.alipay.directory}")
    private String remoteDirectory;

    @Value("${sftp.recipient-emails}")
    private String recipientEmails;

    @Resource
    private SftpService sftpService;

    @Resource
    private DoveFacade doveFacade;

    @Resource
    private DataCenterFacade dataCenterFacade;

    @Override
    public void beforeStep(StepExecution stepExecution) {
        log.info("alipay账单上传sftp：{}, 步骤名称:{}, 任务实例ID：{}, 执行实例ID：{}",
                stepExecution.getJobExecution().getJobInstance().getJobName(),
                stepExecution.getStepName(),
                stepExecution.getJobExecution().getJobInstance().getInstanceId(),
                stepExecution.getJobExecutionId());
    }

    @Override
    public ExitStatus afterStep(StepExecution stepExecution) {
        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (ThirdpartyPayType.ALIPAY_H5 == payType) {
            if (stepExecution.getStatus() == COMPLETED) {
                // 任务执行成功，重命名临时文件为正式文件
                log.info("任务执行成功，开始重命名临时文件为正式文件...");
                try {
                    if (renameTempFilesToCsv()) {
                        log.info("将alipay前端对账文件上传至sftp");
                        String directoryPath = ThirdPartyBillPathUtil.getSftpDirectoryPath(defaultDirectory, billDate);
                        File baseDir = new File(directoryPath); // 根目录
                        File[] tempFiles = baseDir.listFiles((dir, name) -> name.endsWith(".csv"));
                        //写入的时候都写在一个文件里了
                        sftpService.uploadFile(tempFiles[0], remoteDirectory);

                        try {
                            BigDecimal alipayAmount = (BigDecimal) stepExecution.getExecutionContext().get("totalAmount");
                            log.info(">>> 总入账金额汇总结果: {}", alipayAmount);

                            Map<String, BigDecimal> channelTypeAmountMap = dataCenterFacade.fetchDataByCode();
                            //小电钱包支付订单金额
                            BigDecimal walletAmount = channelTypeAmountMap.get(DataCenterClient.ChannelTypeEnum.XD_Wallet.name());
                            log.info(">>> 小电钱包支付订单金额结果: {}", walletAmount);
                            BigDecimal iotAlipayAmount = channelTypeAmountMap.get(DataCenterClient.ChannelTypeEnum.IOT_Alipay.name());
                            log.info(">>> 小电iot支付订单金额结果: {}", iotAlipayAmount);

                            BigDecimal totalAmount = walletAmount != null ? alipayAmount.add(walletAmount) : alipayAmount;
                            // 格式化 wallet 显示内容
                            String walletDisplay = walletAmount != null ? walletAmount.toPlainString() : "未知";
                            // 格式化 iotAlipay 显示内容
                            String iotAlipayDisplay = iotAlipayAmount != null ? iotAlipayAmount.toPlainString() : "未知";

                            //发送邮件
                            MailInfoVo mail = new MailInfoVo();
                            mail.setTitle(LocalDate.now() + "前端分账支付宝+小电钱包支付订单提现至平安银行数据");
                            mail.setTo(recipientEmails);
                            mail.setHtml(String.format("需要操作提现日期: %s </br>" +
                                            "提现金额产生日期: %s </br>" +
                                            "提现金额(单位元): %s </br>" +
                                            "提现接收资金帐户:平安银行(卡尾号:0941)</br></br>" +
                                            "其中 </br>" +
                                            "支付宝订单流水汇总金额(单位元): %s，其中IOT订单金额(单位元):%s </br>" +
                                            "小电钱包支付订单金额(单位元): %s </br>" +
                                            "提现支付宝帐户:<EMAIL></br></br> " +
                                            "备注:前端分账资金</br>",
                                    LocalDate.now(),
                                    billDate,
                                    totalAmount.toPlainString(),
                                    alipayAmount.toPlainString(),
                                    iotAlipayDisplay,
                                    walletDisplay
                            ));
                            File file = tempFiles[0];
                            if (file.exists() && file.isFile()) {
                                Map<String, byte[]> fileStreams = new HashMap<>();
                                byte[] fileContent = Files.readAllBytes(file.toPath());
                                fileStreams.put(file.getName(), fileContent);
                                mail.setFileStreams(fileStreams);
                            }
                            doveFacade.sendMail(mail);
                            log.info("支付宝每日提现金额发送邮件成功,billDate={}", billDate);
                        } catch (Exception e) {
                            log.error("支付宝每日提现金额发送邮件失败,billDate={}", billDate, e);
                        }
                    }
                } catch (Exception e) {
                    log.error("上传异常sftp,billDate={}", billDate, e);
                }
            } else {
                // 任务执行失败，清理临时文件
                log.error("alipay账单上传sftp失败：{}, 步骤名称:{}, 任务实例ID：{}, 执行实例ID：{}",
                        stepExecution.getJobExecution().getJobInstance().getJobName(),
                        stepExecution.getStepName(),
                        stepExecution.getJobExecution().getJobInstance().getInstanceId(),
                        stepExecution.getJobExecutionId());
                try {
                    cleanTempFiles();
                } catch (Exception e) {
                    log.error("cleanTempFiles异常,billDate={}", billDate, e);
                }
            }
        }
        return stepExecution.getExitStatus();
    }

    /**
     * 清理临时文件（任务失败时调用）
     */
    private void cleanTempFiles() {
        String directoryPath = ThirdPartyBillPathUtil.getSftpDirectoryPath(defaultDirectory, billDate);
        File baseDir = new File(directoryPath); // 根目录
        File[] tempFiles = baseDir.listFiles((dir, name) -> name.endsWith(".tmp"));

        if (tempFiles != null) {
            for (File tempFile : tempFiles) {
                if (tempFile.delete()) {
                    log.info(">>> 已成功删除临时文件: {}", tempFile.getAbsolutePath());
                } else {
                    log.warn(">>> 临时文件删除失败: {}", tempFile.getAbsolutePath());
                }
            }
        } else {
            log.info(">>> 未找到任何需要删除的临时文件");
        }
    }

    /**
     * 重命名临时文件为正式文件（任务成功时调用）
     */
    private Boolean renameTempFilesToCsv() {
        String directoryPath = ThirdPartyBillPathUtil.getSftpDirectoryPath(defaultDirectory, billDate);
        File baseDir = new File(directoryPath); // 根目录
        File[] tempFiles = baseDir.listFiles((dir, name) -> name.endsWith(".tmp"));

        if (tempFiles != null) {
            for (File tempFile : tempFiles) {
                File finalFile = new File(tempFile.getAbsolutePath().replace(".tmp", ".csv"));
                if (tempFile.renameTo(finalFile)) {
                    log.info(">>> 已成功重命名文件: {} -> {}", tempFile.getAbsolutePath(), finalFile.getAbsolutePath());
                } else {
                    log.error(">>> 文件重命名失败: {}", tempFile.getAbsolutePath());
                }
            }
        } else {
            log.info(">>> 未找到任何需要重命名的临时文件");
            return false;
        }
        return true;
    }
}