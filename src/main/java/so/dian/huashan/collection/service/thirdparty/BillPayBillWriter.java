package so.dian.huashan.collection.service.thirdparty;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import so.dian.huashan.check.emuns.payBill.PayBillStatus;
import so.dian.huashan.check.emuns.payBill.PayBillThirdStatus;
import so.dian.huashan.collection.manager.PayBillThirdManager;
import so.dian.huashan.collection.mapper.PayBillThirdMapper;
import so.dian.huashan.collection.mapper.dos.PayBillThirdDO;
import so.dian.huashan.collection.mapper.param.PayBillThirdParam;
import so.dian.huashan.common.mapper.yandang.ChannelMapper;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@StepScope
public class BillPayBillWriter implements ItemWriter<PayBillThirdDO> {

    @Resource
    private PayBillThirdMapper payBillThirdMapper;
    @Autowired
    private PayBillThirdManager payBillThirdManager;
    @Value("#{jobParameters[channelId]}")
    private String channelId;
    @Value("#{jobParameters[settleSubjectId]}")
    private String settleSubjectId;
    @Value("#{jobParameters[settleSubjectType]}")
    private String settleSubjectType;

    @Override
    public void write(List<? extends PayBillThirdDO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        Map<String,String> cancelExcelParam = new HashMap<>();
        // 空间换时间
        List<String> tradeNos = items.stream().map(PayBillThirdDO::getTradeNo).collect(Collectors.toList());
        Map<String,PayBillThirdDO> payBillThirdDOMap = getOldMap(tradeNos);
        List<PayBillThirdDO> insertList = new ArrayList<>();
        List<PayBillThirdDO> updateList = new ArrayList<>();
        List<Long> errorFileIds = new ArrayList<>();
        Date start = DateUtil.date();
        for (PayBillThirdDO payBillThirdDO : items) {
            if (StringUtils.isBlank(payBillThirdDO.getTradeType())){
                log.info("tradeType is null channelId:{},fileId:{}",channelId,payBillThirdDO.getFileId());
                errorFileIds.add(payBillThirdDO.getFileId());
                continue;
            }
            if (StringUtils.isBlank(payBillThirdDO.getChannelTradeNo())){
                continue;
            }
            if (StringUtils.isBlank(payBillThirdDO.getTradeNo())){
                log.error("BillPayBillWriter get tradeNo is null,channelId:{},payBillThirdDO:{}",channelId, JSON.toJSON(payBillThirdDO));
                continue;
            }
            if (PayBillThirdStatus.FAIL.getCode().equals(payBillThirdDO.getThirdStatus())){
                continue;
            }
            String key = payBillThirdDO.getChannelTradeNo()+"_"+payBillThirdDO.getTradeType();
            String tradeNo = cancelExcelParam.get(key);
            if (StringUtils.isNotBlank(tradeNo)){
                log.error("BillPayBillWriter get tradeNo:{},channelTradeNo:{},tradeType:{}",tradeNo,payBillThirdDO.getChannelTradeNo(),payBillThirdDO.getTradeType());
                throw new RuntimeException("有重复的交易单号"+tradeNo);
            }
            // excel 不允许有多条三方交易单和收支类型一样的数据
            cancelExcelParam.put(key,payBillThirdDO.getTradeNo());
            payBillThirdDO.setChannelId(Long.valueOf(channelId));
            payBillThirdDO.setPaySubjectId(Long.valueOf(settleSubjectId));
            payBillThirdDO.setPaySubjectType(Integer.valueOf(settleSubjectType));
            PayBillThirdDO oldThirdDO = payBillThirdDOMap.get(payBillThirdDO.getTradeNo()+"_"+payBillThirdDO.getTradeType());
            if (Objects.isNull(oldThirdDO)){
                payBillThirdDO.setGmtCreate(System.currentTimeMillis());
                payBillThirdDO.setGmtUpdate(System.currentTimeMillis());
                insertList.add(payBillThirdDO);
                continue;
            }
            if (!checkUpdate(payBillThirdDO,oldThirdDO)){
                continue;
            }
            if (PayBillStatus.PROCESSED.getCode().equals(oldThirdDO.getStatus())){
                payBillThirdDO.setStatus(PayBillStatus.PROCESSING.getCode());
                log.info("数据继续处理，原底表数据状态为已处理，财务可能已经出完账了 tradeNo:{}",payBillThirdDO.getTradeNo());
            }
            payBillThirdDO.setGmtUpdate(System.currentTimeMillis());
            payBillThirdDO.setId(oldThirdDO.getId());
            updateList.add(payBillThirdDO);
        }
        if (!CollectionUtils.isEmpty(errorFileIds)){
            log.error("僖游客有未知的收入类型fileIds:{}",errorFileIds);
        }
        // 批量添加和更新
        payBillThirdManager.batchSaveAndUpdate(insertList,updateList);
        log.info(">>> 写入一批数据结束, 数据大小：{}, 耗时：{},channelId:{}", Optional.ofNullable(items).map(java.util.List::size).orElse(0), DateUtil.formatBetween(start, DateUtil.date()), channelId);
    }

    /**
     * 获取老的map
     */
    private Map<String,PayBillThirdDO> getOldMap(List<String> tradeNos){
        if (CollectionUtils.isEmpty(tradeNos)){
            return new HashMap<>();
        }
        PayBillThirdParam payBillThirdParam = new PayBillThirdParam();
        payBillThirdParam.setTradeNos(tradeNos);
        List<PayBillThirdDO> payBillThirdDOList = payBillThirdMapper.selectListByParam(payBillThirdParam);
        if (CollectionUtils.isEmpty(payBillThirdDOList)){
            return new HashMap<>();
        }
        return payBillThirdDOList.stream().collect(Collectors.toMap(k-> k.getTradeNo()+"_"+k.getTradeType(), Function.identity(), (k1, k2) -> k1));
    }
    private Boolean checkUpdate(PayBillThirdDO payBilThirdlDO,PayBillThirdDO oldPayThirdDO){
        if (Objects.isNull(oldPayThirdDO)||Objects.isNull(payBilThirdlDO)){
            return true;
        }

        if (!Objects.equals(payBilThirdlDO.getTradeAmount(),oldPayThirdDO.getTradeAmount())){
            return true;
        }
        if (!Objects.equals(payBilThirdlDO.getTradeTime(),oldPayThirdDO.getTradeTime())){
            return true;
        }
        if (!Objects.equals(payBilThirdlDO.getPaySubjectId(),oldPayThirdDO.getPaySubjectId())){
            return true;
        }
        return false;
    }
}
