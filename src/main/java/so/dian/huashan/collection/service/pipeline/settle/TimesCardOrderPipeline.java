package so.dian.huashan.collection.service.pipeline.settle;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.check.emuns.settle.SettleOrderStatus;
import so.dian.huashan.check.emuns.settle.SettleOriginalStatus;
import so.dian.huashan.collection.mapper.BillSettleOrderOriginalMapper;
import so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.common.enums.OrderBizType;
import so.dian.huashan.common.mapper.lhc.entity.TimesCardOrderDO;

import java.util.Objects;

import static so.dian.huashan.common.enums.BizErrorCodeEnum.SETTLE_ORDER_STATUS_NOT_EXIST;
import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miaoshuai
 * @create: 2023/12/18 10:06
 * @description: 清结算业务-orders_box表binlog日志采集
 */
@Slf4j
@Component
public class TimesCardOrderPipeline extends CollectionPipeline {

    @Autowired
    private BillSettleOrderOriginalMapper settleOrderOriginalMapper;

    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        TimesCardOrderDO before = JSON.parseObject(pipelineBO.getBinLog().getBefore(), TimesCardOrderDO.class);
        TimesCardOrderDO after = JSON.parseObject(pipelineBO.getBinLog().getAfter(), TimesCardOrderDO.class);

        if (checkStatus(before, after)) {
            // 0元订单不处理
            if (after.getPayAmount() <= 0)
                return Boolean.TRUE;

            // 非【前端分账订单】不处理
            if (!isFrontDivideOrder(after))
                return Boolean.TRUE;

            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean checkStatus(TimesCardOrderDO before, TimesCardOrderDO after) {

        Integer beforeStatus = before.getStatus();
        Integer afterStatus = after.getStatus();
        if (SettleOrderStatus.CARD_PAY.getCode().equals(afterStatus)
                && !SettleOrderStatus.CARD_PAY.getCode().equals(beforeStatus))
            return Boolean.TRUE;

        if (SettleOrderStatus.CARD_REFUND.getCode().equals(afterStatus)
                && !SettleOrderStatus.CARD_REFUND.getCode().equals(beforeStatus))
            return Boolean.TRUE;

        if (SettleOrderStatus.CARD_REFUND.getCode().equals(beforeStatus)
                && SettleOrderStatus.CARD_REFUND.getCode().equals(afterStatus)
                && !after.getRefundAmount().equals(before.getRefundAmount()))
            return Boolean.TRUE;

        return Boolean.FALSE;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        TimesCardOrderDO after = JSON.parseObject(pipelineBO.getBinLog().getAfter(), TimesCardOrderDO.class);
        BillSettleOrderOriginalDO orderOriginalDO = new BillSettleOrderOriginalDO();
        orderOriginalDO.setStatus(SettleOriginalStatus.INITIALIZE.getCode());
        orderOriginalDO.setVoucherId(pipelineBO.getVoucherId());
        orderOriginalDO.setOrderNo(after.getOrderNo());
        orderOriginalDO.setOrderStatus(after.getStatus());
        orderOriginalDO.setOrderAmount(after.getOrderAmount());
        orderOriginalDO.setVersion(0);
        orderOriginalDO.setBizType(OrderBizType.TIMES_CARD_ORDER.getKey());

        SettleOrderStatus orderStatus = SettleOrderStatus.from(after.getStatus());
        if (Objects.isNull(orderStatus)) {
            log.error(BILLING_SETTLE, "times_cad_order表binlog采集状态不符合存表要求, order_no:[{}]", after.getOrderNo());
            throw BizException.create(SETTLE_ORDER_STATUS_NOT_EXIST);
        }

        if (SettleOrderStatus.CARD_PAY.equals(orderStatus)) {
            orderOriginalDO.setPayAmount(after.getPayAmount());
            orderOriginalDO.setPayTime(after.getPayTime());
        }else {
            orderOriginalDO.setPayAmount(after.getRefundAmount());
            orderOriginalDO.setPayTime(after.getRefundTime());
        }

        orderOriginalDO.setLoanShopId(after.getShopId());
        orderOriginalDO.setPaySuccessTime(after.getPayTime());

        settleOrderOriginalMapper.insertSelective(orderOriginalDO);
        return CollectionProcessResult.success()
                .voucherId(pipelineBO.getVoucherId())
                .addIncomeOriginalId(orderOriginalDO.getId());
    }

    /**
     * 判断次卡订单是否为【前端分账订单】
     * @param timesCardOrderDO 次卡订单
     * @return 是否为【前端分账订单】
     */
    private boolean isFrontDivideOrder(TimesCardOrderDO timesCardOrderDO) {
        return timesCardOrderDO.getProfitSharding() == 2;
    }

}
