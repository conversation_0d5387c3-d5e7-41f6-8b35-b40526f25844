package so.dian.huashan.collection.service.thirdparty.xiyouke;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.batch.item.file.transform.FieldSet;
import so.dian.huashan.common.constant.ChannelConstant;

@Slf4j
public class XiYouKeBillFieldSetMapper extends DefaultLineMapper<XiYouKeBill> implements FieldSetMapper<XiYouKeBill> {

    public XiYouKeBillFieldSetMapper() {
        DelimitedLineTokenizer lineTokenizer = new DelimitedLineTokenizer();
        lineTokenizer.setDelimiter(",");
        lineTokenizer.setStrict(false);
        lineTokenizer.setNames(ChannelConstant.xiyoukeAccountBillColumn.split(","));
        setLineTokenizer(lineTokenizer);
        setFieldSetMapper(this);
    }
    @Override
    @NonNull
    public XiYouKeBill mapFieldSet(@NonNull FieldSet fieldSet) {

        XiYouKeBill result = new XiYouKeBill();
        if ((fieldSet.readString("outOrderNo").contains("订单"))) {
            log.info("跳过行：" + JSON.toJSONString(fieldSet));
            throw new FlatFileParseException(fieldSet.readString("outOrderNo"), StrUtil.EMPTY);
        }
        //渠道订单号
        result.setOutOrderNo(getTextString(fieldSet, "outOrderNo"));
        //企业名称
        result.setCompanyName(getTextString(fieldSet, "companyName"));
        //服务商
        result.setServiceProvider(getTextString(fieldSet, "serviceProvider"));
        //姓名
        result.setName(getTextString(fieldSet, "name"));
        //银行卡
        result.setBankCard(getTextString(fieldSet, "bankCard"));
        //电话
        result.setTelephone(getTextString(fieldSet, "telephone"));
        //身份证
        result.setIDCard(getTextString(fieldSet, "iDCard"));
        //交易金额
        result.setTradeAmount(getTextString(fieldSet, "tradeAmount"));
        //服务费
        result.setServiceCharge(getTextString(fieldSet, "serviceCharge"));
        //外部订单状态
        result.setOuterOrderStatus(getTextString(fieldSet, "outerOrderStatus"));
        // 收支类型
        result.setTradeType(getTextString(fieldSet, "tradeType"));
        //发放通道
        result.setDistributionChannel(getTextString(fieldSet, "distributionChannel"));
        //创建时间
        result.setCreateTime(getTextString(fieldSet, "createTime"));
        //来源
        result.setSource(getTextString(fieldSet, "source"));
        //发放时间
        result.setReleaseTime(getTextString(fieldSet, "releaseTime"));
        //外部的支付流水号
        result.setOuterPayNo(getTextString(fieldSet, "outerPayNo"));
        //来源
        result.setFeedBackResults(getTextString(fieldSet, "feedBackResults"));
        //回单
        result.setReceipt(getTextString(fieldSet, "receipt"));
        //交易号
        result.setTradeNo(getTextString(fieldSet, "tradeNo"));

        //备注
        result.setRemark(getTextString(fieldSet, "remark"));
        return result;
    }

    private String getTextString(FieldSet fieldSet, String key) {
        String replace = fieldSet.readString(key).replace("`", "");
        return StrUtil.isBlank(replace) ? null : replace;
    }
}
