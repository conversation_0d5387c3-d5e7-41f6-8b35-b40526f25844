package so.dian.huashan.collection.service.pipeline.settle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.check.emuns.settle.SettleOrderStatus;
import so.dian.huashan.check.emuns.settle.SettleOriginalStatus;
import so.dian.huashan.collection.config.BizProperties;
import so.dian.huashan.collection.mapper.BillSettleOrderOriginalMapper;
import so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO;
import so.dian.huashan.collection.service.pipeline.CollectionPipeline;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;
import so.dian.huashan.collection.service.entity.PipelineBO;
import so.dian.huashan.common.enums.OrderBizType;
import so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO;

import java.util.Objects;

import static so.dian.huashan.common.enums.BizErrorCodeEnum.SETTLE_ORDER_STATUS_NOT_EXIST;
import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miaoshuai
 * @create: 2023/12/18 10:06
 * @description: 清结算业务-orders_box表binlog日志采集
 */
@Slf4j
@Component
public class BillSettleOrdersBoxPipeline extends CollectionPipeline {

    private static final String HUAZHU_ORDER_PREFIX = "47";

    @Autowired
    private BillSettleOrderOriginalMapper settleOrderOriginalMapper;

    @Autowired
    private BizProperties bizProperties;

    @Override
    protected boolean isSkip(PipelineBO pipelineBO) {
        OrdersBoxDO before = JSON.parseObject(pipelineBO.getBinLog().getBefore(), OrdersBoxDO.class);
        OrdersBoxDO after = JSON.parseObject(pipelineBO.getBinLog().getAfter(), OrdersBoxDO.class);

        if (Objects.nonNull(bizProperties.getSettle().getCreateTime())) {
            DateTime createTime = new DateTime(bizProperties.getSettle().getCreateTime());
            if (createTime.after(after.getCreateTime())) {
                log.info(BILLING_SETTLE, "清结算数据采集，订单的创建时间小于指定的时间[{}], 订单号：{}", bizProperties.getSettle().getCreateTime(), after.getOrderNo());
                return Boolean.TRUE;
            }
        }
        if (checkStatus(before, after)) {
            // 华住订单判断
            if (after.getOrderNo().startsWith(HUAZHU_ORDER_PREFIX))
                return Boolean.TRUE;

            // 0元订单不处理
            if (after.getPayAmount() <= 0)
                return Boolean.TRUE;

            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean checkStatus(OrdersBoxDO before, OrdersBoxDO after) {

        Integer beforeStatus = before.getStatus();
        Integer afterStatus = after.getStatus();
        if (SettleOrderStatus.PAY.getCode().equals(afterStatus)
                && !SettleOrderStatus.PAY.getCode().equals(beforeStatus))
            return Boolean.TRUE;

        if (SettleOrderStatus.REFUND.getCode().equals(afterStatus)
                && !SettleOrderStatus.REFUND.getCode().equals(beforeStatus))
            return Boolean.TRUE;

        if (SettleOrderStatus.REFUND.getCode().equals(beforeStatus)
                && SettleOrderStatus.REFUND.getCode().equals(afterStatus)
                && !after.getRefundAmount().equals(before.getRefundAmount()))
            return Boolean.TRUE;

        return Boolean.FALSE;
    }

    @Override
    protected CollectionProcessResult doProcess(PipelineBO pipelineBO) {
        OrdersBoxDO after = JSON.parseObject(pipelineBO.getBinLog().getAfter(), OrdersBoxDO.class);
        BillSettleOrderOriginalDO orderOriginalDO = new BillSettleOrderOriginalDO();
        orderOriginalDO.setStatus(SettleOriginalStatus.INITIALIZE.getCode());
        orderOriginalDO.setVoucherId(pipelineBO.getVoucherId());
        orderOriginalDO.setOrderNo(after.getOrderNo());
        orderOriginalDO.setOrderStatus(after.getStatus());
        orderOriginalDO.setOrderAmount(after.getOrderAmount());
        orderOriginalDO.setVersion(0);
        orderOriginalDO.setBizType(OrderBizType.ORDER_BOX_ORDER.getKey());

        SettleOrderStatus orderStatus = SettleOrderStatus.from(after.getStatus());
        if (Objects.isNull(orderStatus)) {
            log.error(BILLING_SETTLE, "orders_box表binlog采集状态不符合存表要求, order_no:[{}]", after.getOrderNo());
            throw BizException.create(SETTLE_ORDER_STATUS_NOT_EXIST);
        }

        if (SettleOrderStatus.PAY.equals(orderStatus)) {
            orderOriginalDO.setPayAmount(after.getPayAmount());
            orderOriginalDO.setPayTime(after.getPayTime());
        }else {
            orderOriginalDO.setPayAmount(after.getRefundAmount());
            orderOriginalDO.setPayTime(after.getRefundTime());
        }

        orderOriginalDO.setLoanShopId(after.getLoanShopId());
        orderOriginalDO.setReturnBoxNo(after.getReturnBoxNo());
        orderOriginalDO.setLoanPriceInfo(after.getLoanPriceInfo());
        orderOriginalDO.setPaySuccessTime(after.getPayTime());

        settleOrderOriginalMapper.insertSelective(orderOriginalDO);

        if (orderStatus == SettleOrderStatus.PAY && usedTimeCard(after.getOrderNo())) {
            // 租赁订单状态=支付，并且使用过优惠券(次卡)
            BillSettleOrderOriginalDO timeCardVerify = buildTimeCardVerify(orderOriginalDO);
            settleOrderOriginalMapper.insertSelective(timeCardVerify);
            return CollectionProcessResult.success()
                    .voucherId(pipelineBO.getVoucherId())
                    .addIncomeOriginalId(orderOriginalDO.getId())
                    .addIncomeOriginalId(timeCardVerify.getId());
        }

        return CollectionProcessResult.success()
                .voucherId(pipelineBO.getVoucherId())
                .addIncomeOriginalId(orderOriginalDO.getId());
    }

    /**
     * 判断租赁订单是否使用过优惠券(次卡)
     * @param orderNo 租赁订单号
     * @return 是否使用过
     */
    private boolean usedTimeCard(String orderNo) {
        // 1.根据order_no(租赁订单号)查询lhc.payment(支付单表)
        // 2.过滤lhc.payment(支付单表)数据，若不存在biz_type=37(优惠券)的数据，则表示未使用过优惠券(次卡)
        // 3.根据biz_type=37(优惠券)的数据的biz_id(业务id)，查询thor.times_card_coupon_order_mapping(优惠券核销记录表)

        return false;
    }

    private BillSettleOrderOriginalDO buildTimeCardVerify(BillSettleOrderOriginalDO orderOriginalDO) {
        BillSettleOrderOriginalDO result = new BillSettleOrderOriginalDO();
        BeanUtil.copyProperties(orderOriginalDO, result);

//        result.setOrderNo(after.getOrderNo()); 应该可能要改成次卡订单号
        result.setBizType(OrderBizType.TIMES_CARD_VERIFY.getKey());
        return result;
    }

}
