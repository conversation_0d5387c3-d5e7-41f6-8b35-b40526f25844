package so.dian.huashan.collection.service.thirdparty;

import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/03/20 11:13
 * @description: 拉取三方账单
 */
@StepScope
@Service
public class BillPullProcessor implements ItemProcessor<BillRequestParam, List<String>> {

    @Resource
    private ChannelBillApi channelBillApiService;

    @Override
    public List<String> process(BillRequestParam requestParam) throws Exception {

        // 拉取账单文件:返回本地路径
        List<String> billFiles = channelBillApiService.pullBillFile(requestParam);
        return billFiles;
    }
}
