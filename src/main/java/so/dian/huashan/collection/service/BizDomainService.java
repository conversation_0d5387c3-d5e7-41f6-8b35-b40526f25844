package so.dian.huashan.collection.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.collection.controller.request.BizDomainReq;
import so.dian.huashan.collection.facade.BizDomainFacade;
import so.dian.huashan.collection.service.entity.BizDomainBO;
import so.dian.huashan.collection.service.entity.DomainConfigBO;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static so.dian.huashan.common.constant.CacheNameConsts.CACHE_DOMAIN_NAME;
import static so.dian.huashan.common.enums.BizErrorCode.BIZ_EXCEPTION;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/13 10:56
 * @description:
 */
@Slf4j
@Component
public class BizDomainService {

    @Autowired
    private BizDomainFacade bizDomainFacade;

    @Autowired
    private RedissonClient redissonClient;

//    @Cacheable(value = {CACHE_DOMAIN_NAME}, key = "#id", sync = true)
//    @CacheExpire(ttl = 60, unit = TimeUnit.DAYS)
    public BizDomainBO find(Long id) {
        if (Objects.isNull(id))
            return null;

//        if (1 == id) {
//            DomainConfigBO configBO = new DomainConfigBO();
//            configBO.setCollectionThreadPool("incomeBillExecutor");
//            return BizDomainBO.builder()
//                    .id(1L)
//                    .code("income_bill")
//                    .name("收入账")
//                    .config(configBO)
//                    .build();
//        }
//        if (30001 == id) {
//            DomainConfigBO configBO = new DomainConfigBO();
//            configBO.setCollectionThreadPool("billSettleExecutor");
//            return BizDomainBO.builder()
//                    .id(30001L)
//                    .code("bill_settle")
//                    .name("清结算")
//                    .config(configBO)
//                    .build();
//        }

        RBucket<BizDomainBO> domainRBucket = redissonClient.getBucket(StrUtil.join(StrUtil.COLON, CACHE_DOMAIN_NAME, id));
        BizDomainBO domainBO = domainRBucket.get();
        if (Objects.nonNull(domainBO))
            return domainBO;

        log.info("开始加载业务域数据,ID:{}", id);
        domainBO = bizDomainFacade.find(id);
        if (Objects.nonNull(domainBO))
            domainRBucket.set(domainBO, 60, TimeUnit.DAYS);
        return domainBO;
    }

//    @CacheEvict(value = {CACHE_DOMAIN_NAME}, key = "#id")
//    public void evictDomain(Long id) {}

    public Long save(BizDomainReq req) {
        if (Objects.isNull(req))
            return null;

        BizDomainBO domainBO = bizDomainFacade.findByCode(req.getCode());
        if (Objects.nonNull(domainBO) && Objects.isNull(req.getId()))
            throw BizException.create(BIZ_EXCEPTION, "对应的业务域编码已存在");

        DomainConfigBO configBO = JSON.parseObject(req.getConfig(), DomainConfigBO.class);
        domainBO = BizDomainBO.builder()
                .id(req.getId())
                .code(req.getCode())
                .name(req.getName())
                .config(configBO)
                .build();

        return bizDomainFacade.save(domainBO.toDO());
    }
}
