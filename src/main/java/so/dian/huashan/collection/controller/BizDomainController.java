package so.dian.huashan.collection.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.collection.controller.request.BizDomainReq;
import so.dian.huashan.collection.service.BizDomainService;

import javax.validation.Valid;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/12/25 18:07
 * @description:
 */
@RestController
public class BizDomainController {

    @Autowired
    private BizDomainService bizDomainService;

    @PostMapping("biz/domain/save")
    public BizResult<Long> save(@RequestBody @Valid BizDomainReq req) {
        return BizResult.create(bizDomainService.save(req));
    }
}
