package so.dian.huashan.collection.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.collection.adapter.CollectionCtlAdapter;
import so.dian.huashan.collection.controller.request.CacheEvictReq;
import so.dian.huashan.collection.service.CollectionCtlService;
import so.dian.huashan.collection.service.entity.KafkaInfoBO;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static so.dian.huashan.common.exception.LogMarkerFactory.SYSTEM_ERROR;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/09/28 18:09
 * @description: 管道收集控制
 */
@Slf4j
@RestController
public class CollectionCtlController {

    @Resource
    private CollectionCtlService collectionCtlService;

    @Resource
    private CollectionCtlAdapter collectionCtlAdapter;

    @GetMapping("collectionCtl/consumerBrake")
    public BizResult<String> consumerBrake(@RequestParam("consumerId") String consumerId,
                                           @RequestParam("pause") Boolean pause,
                                           @RequestParam(value = "propagateModel", defaultValue = "clustering") String propagateModel) {
        boolean consumerBrake = collectionCtlService.consumerBrake(consumerId, pause, propagateModel);
        return BizResult.create(StrUtil.format("消费者{}操作{}", pause ? "制动" : "唤醒", consumerBrake ? "成功" : "失败"));
    }

    @GetMapping("collectionCtl/consumerMetric")
    public BizResult<KafkaInfoBO> consumerMetric(@RequestParam("consumerId") String consumerId) {
        return BizResult.create(collectionCtlService.consumerMetric(consumerId));
    }

    @GetMapping("collectionCtl/skip/collection")
    public BizResult<Boolean> skipCollection(@RequestParam("bizDomainCode") String bizDomainCode) {
        Boolean skipCollection = collectionCtlService.skipCollection(bizDomainCode);
        return BizResult.create(skipCollection);
    }

    @PostMapping("collectionCtl/direct/process")
    public BizResult<String> directProcess(@RequestBody List<Long> voucherIds) {
        collectionCtlAdapter.batchAsyncVoucherProcess(voucherIds);
        return BizResult.create("请求成功");
    }

    @PostMapping("collectionCtl/cache/evict")
    public BizResult<Boolean> cacheEvict(@RequestBody @Valid CacheEvictReq cacheEvictReq) {
        try {
            Boolean cacheEvict = collectionCtlService.cacheEvict(cacheEvictReq);
            return BizResult.create(cacheEvict);
        }catch (Exception e) {
            log.info(SYSTEM_ERROR, "缓存删除失败, req:{}", JSON.toJSONString(cacheEvictReq), e);
            return BizResult.create(Boolean.FALSE);
        }
    }
}
