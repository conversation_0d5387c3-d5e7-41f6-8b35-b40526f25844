package so.dian.huashan.collection.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.collection.controller.request.CollectionPipelineReq;
import so.dian.huashan.collection.service.CollectionPipelineService;

import javax.validation.Valid;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/25 18:09
 * @description:
 */
@RestController
public class CollectionPipelineController {

    @Autowired
    private CollectionPipelineService pipelineService;

    @PostMapping("collection/pipeline/save")
    public BizResult<Long> save(@RequestBody @Valid CollectionPipelineReq req) {
        return BizResult.create(pipelineService.savePipeline(req));
    }
}
