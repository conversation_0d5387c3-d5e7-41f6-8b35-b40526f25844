package so.dian.huashan.collection.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpRequest;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.LoggingProducerListener;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.util.ResourceUtils;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.check.controller.req.SummaryJobReq;
import so.dian.huashan.check.controller.req.XiYouKeCollectionJobReq;
import so.dian.huashan.check.handler.context.AbstractCheckContext;
import so.dian.huashan.check.handler.impl.ChannelAccountingHandler;
import so.dian.huashan.check.job.PayBillSummaryJob;
import so.dian.huashan.check.job.income.processor.ChannelDiffCompleteProcessor;
import so.dian.huashan.check.job.settle.processor.SettleCheckCompleteProcessor;
import so.dian.huashan.check.mapper.ControlOrderCheckConsistencyMapper;
import so.dian.huashan.check.mapper.entity.ControlOrderCheckConsistencyDO;
import so.dian.huashan.collection.config.BizProperties;
import so.dian.huashan.collection.job.XiYouKeCollectionJob;
import so.dian.huashan.collection.service.BizDomainService;
import so.dian.huashan.collection.service.CollectionPipelineService;
import so.dian.huashan.collection.service.ThirdpartyPullService;
import so.dian.huashan.collection.service.entity.BizDomainBO;
import so.dian.huashan.collection.service.entity.CollectionPipelineBO;
import so.dian.huashan.collection.service.thirdparty.ApiConfig;
import so.dian.huashan.collection.service.thirdparty.xiyouke.XiyoukeChannelApi;
import so.dian.huashan.common.enums.ThirdpartyPayType;
import so.dian.huashan.common.facade.ControlOrderEsFacade;
import so.dian.huashan.common.mapper.contract.ContractShopDivideMapper;
import so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO;
import so.dian.huashan.common.mapper.customer.TenantMapper;
import so.dian.huashan.common.mapper.customer.dos.TenantDO;
import so.dian.huashan.common.mapper.emei.ProductDeviceInfoMapper;
import so.dian.huashan.common.mapper.emei.dto.ProductDeviceInfoDO;
import so.dian.huashan.common.mapper.lhc.PaymentMapper;
import so.dian.huashan.common.mapper.lhc.entity.PaymentDO;
import so.dian.huashan.common.mapper.newyork.dos.ShardingTradeOrderDO;
import so.dian.huashan.common.mapper.newyork.sharding.ShardingTradeOrderMapper;
import so.dian.huashan.common.mapper.oss.AgentMapper;
import so.dian.huashan.common.mapper.oss.dos.AgentDO;
import so.dian.huashan.common.mapper.polar.OrdersWideTableMapper;
import so.dian.huashan.common.mapper.shop.TenantShopMapper;
import so.dian.huashan.common.mapper.shop.dos.TenantShopDO;
import so.dian.huashan.common.mapper.tiantai.dos.PimSpuDO;
import so.dian.huashan.common.mapper.yandang.ChannelMapper;
import so.dian.huashan.common.mapper.yandang.dos.ChannelDO;
import so.dian.huashan.common.service.SpuService;
import so.dian.huashan.common.service.entity.ControlOrderBO;
import so.dian.huashan.common.service.entity.SpuInfoBO;
import so.dian.huashan.common.utils.AopProxyUtils;
import so.dian.huashan.common.val.IncomeCheckParam;
import so.dian.huashan.framework.task.entity.ShardCompleteMessage;
import so.dian.huashan.task.mapper.TaskSubMapper;
import so.dian.huashan.task.mapper.entity.TaskSubDO;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;

import static so.dian.huashan.common.exception.LogMarkerFactory.COLLECTION_DATA_MARKER;

/**
 * @author: miaoshuai
 * @create: 2023/03/21 14:15
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("test")
public class TestController {

    @Value("${huashan.thirdparty.bill.file:#{systemProperties['user.home']}}")
    private String defaultDirectory;

    @Autowired
    private ThirdpartyPullService thirdpartyPullService;

    @GetMapping("path")
    public String filePath() {
        log.info("defaultDirectory");
        return defaultDirectory;
    }

    @GetMapping("api/config/list")
    public List<ApiConfig> getApiConfs() {
        return ApiConfig.getAll();
    }

    @Autowired
    private BizDomainService bizDomainService;

    @Autowired
    private SettleCheckCompleteProcessor completeProcessor;

    @Resource
    private ControlOrderEsFacade controlOrderEsFacade;

    @Autowired
    private XiyoukeChannelApi xiYouKeChannelApi;
    @Autowired
    private PayBillSummaryJob payBillSummaryJob;
    @Autowired
    private ChannelAccountingHandler channelAccountingHandler;

    @Autowired
    private XiYouKeCollectionJob xiYouKeCollectionJob;

    @Autowired
    private TaskSubMapper taskSubMapper;
    @Resource
    private ChannelMapper channelMapper;

    /**
     * 付款对账明细（新）
     */
    @GetMapping("crm/test/get")
    public BizResult<Boolean> testGet() {
        try {
            xiYouKeChannelApi.toPullBillFile(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BizResult.create(true);
    }
    @PostMapping("test/summary/job")
    public BizResult<Boolean> summaryJob(@RequestBody SummaryJobReq summaryJobReq){
        payBillSummaryJob.summary(summaryJobReq.getStartTime(),summaryJobReq.getEndTime());
        return BizResult.create(true);
    }

    @PostMapping("test/xiyouke/collection/job")
    public BizResult<Boolean> xiYouKeCollectionJob(@RequestBody XiYouKeCollectionJobReq xiYouKeCollectionJobReq){
        if (Objects.isNull(xiYouKeCollectionJobReq)||Objects.isNull(xiYouKeCollectionJobReq.getId())){
            log.info("渠道账户不能为空");
            return BizResult.create(true);
        }
        ChannelDO channelDO =  channelMapper.selectByPrimaryKey(xiYouKeCollectionJobReq.getId());
        log.info(JSONObject.toJSONString(channelDO));
        // 申请账单
        xiYouKeCollectionJob.applyBill(channelDO,xiYouKeCollectionJobReq.getStartTime(), xiYouKeCollectionJobReq.getEndTime());
        return BizResult.create(true);
    }

    @GetMapping("crm/test/callback")
    public BizResult<Boolean> testCallback(HttpRequest request) {
        try {

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BizResult.create(true);
    }

    @PostMapping("es/control/orders")
    public List<ControlOrderBO> listControlOrder(@RequestBody List<String> orderNos) {
        log.info("开始查询ES抽单池数据");
        return controlOrderEsFacade.list(orderNos);
    }


    @Autowired
    private OrdersWideTableMapper ordersWideTableMapper;

    @PostMapping("controlOrder/control/orders")
    public List<ControlOrderBO> selectControlOrder(@RequestBody List<String> orderNos) {
        log.info("开始查询数据库抽单池数据");
        return ordersWideTableMapper.selectByOrderNos(orderNos);
    }

    @GetMapping("settleNotify")
    public void settleNotify() {
        ShardCompleteMessage message = ShardCompleteMessage.builder()
                .taskMasterId(1L)
                .allShardCount(2L)
                .successShardCount(2L)
                .failShardCount(0L)
                .jobName("settleIncrementCheckJob")
                .build();
        completeProcessor.process(message);
    }

    @GetMapping("get/bizDomain")
    public BizResult<BizDomainBO> getPipeline(@RequestParam("id") Long id) {
        return BizResult.create(bizDomainService.find(id));
    }

    @Autowired
    private CollectionPipelineService pipelineService;

    @GetMapping("list/bizDomain/table")
    public BizResult<List<String>> getPipeline(@RequestParam("code") String code) {
        return BizResult.create(pipelineService.loadTables(code));
    }

    @GetMapping("list/bizDomain/initTable")
    public BizResult<List<String>> initTable(@RequestParam("code") String code) {
        return BizResult.create(pipelineService.loadTablesTest(code));
    }

    @GetMapping("get/collectionPipeline")
    public BizResult<CollectionPipelineBO> findByTable(@RequestParam("table") String table) {
        log.error(COLLECTION_DATA_MARKER, "查询采集管道，table:{}", table);
        return BizResult.create(pipelineService.find(table));
    }

    @GetMapping("transa")
    public void testTransactional() {

        log.info("开始执行");
        thirdpartyPullService.testTransactional();
    }

    @GetMapping("pull/bill")
    public String pullBill(@RequestParam(name = "payType", required = false) String payTypeName,@RequestParam(name = "param", required = false) String param) {

        ThirdpartyPayType payType = ThirdpartyPayType.fromName(payTypeName);
        if (Objects.isNull(payType)) {
            IncomeCheckParam checkParam = new IncomeCheckParam(StrUtil.EMPTY, StrUtil.EMPTY);
            checkParam.setPayType(payTypeName);
            checkParam.setBillDate(param);
            thirdpartyPullService.pullAll(checkParam);
        }else {
            thirdpartyPullService.pull(payType, param);
        }

        return "success";
    }

    @RequestMapping("download/bill")
    public void downloadBillFile(HttpServletResponse response, HttpServletRequest request) {

        log.info("开始下载账单文件， 参数是：{}", getParam(request));
        try {
            String filePath = "/Users/<USER>/Desktop/test/bill/download/weixin_h5_20230917.csv";
            File billFile = ResourceUtils.getFile(filePath);
            InputStream inputStream = new FileInputStream(billFile);

            response.reset();
            response.setContentType("application/octet-stream");
            String filename = billFile.getName();
            response.addHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            byte[] b = new byte[1024];
            int len;
            //从输入流中读取一定数量的字节，并将其存储在缓冲区字节数组中，读到末尾返回-1
            while ((len = inputStream.read(b)) > 0) {
                outputStream.write(b, 0, len);
            }
            inputStream.close();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getParam(HttpServletRequest request) {

        try {
            StringBuffer data = new StringBuffer();
            BufferedReader reader = request.getReader();
            String line;
            while (null != (line = reader.readLine())) {
                data.append(line);
            }

            return data.toString();
        }catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }



    @GetMapping("check/acct")
    public String testCheckAcct(@RequestParam("taskSubId") Long taskSubId) {
        TaskSubDO taskSubDO = taskSubMapper.selectByPrimaryKey(taskSubId);
        AbstractCheckContext checkContext = AbstractCheckContext.builder().taskSubId(taskSubId).voucher(taskSubDO.getVoucher()).build();
//        channelAccountingHandler.process(checkContext);

        return "Finish!!!";
    }

    @Autowired
    private Environment env;

    private final Map<String, SendConfig> idAndConfigMap = new HashMap<>();

    @PostMapping("/send/binlog")
    public String sendMsg(@RequestBody String binlog, HttpServletRequest request) throws Exception{
        SendConfig config = getConfig(request.getHeader("serverId"));
        if (Objects.isNull(config))
            return "获取不到配置信息";

        ListenableFuture<SendResult<String, Object>> send = config.kafkaTemplate.send(config.topic, binlog);
        return send.get().getProducerRecord().toString();
    }

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @PostMapping("/send/mqMsg")
    public String sendMqMsg(@RequestBody String content) {
        Message<String> message = MessageBuilder.withPayload(content).build();
        org.apache.rocketmq.client.producer.SendResult sendResult = rocketMQTemplate.syncSend(StrUtil.join(StrUtil.COLON, "longhu", "ownership_change_894115376340668416"), message);
        return sendResult.getMsgId();
    }

    @AllArgsConstructor
    public static class SendConfig{
        private String topic;
        private KafkaTemplate<String, Object> kafkaTemplate;
    }

    private SendConfig getConfig(String serverId) {
        SendConfig config = idAndConfigMap.get(serverId);
        if (Objects.nonNull(config))
            return config;

        if ("income".equals(serverId)) {
            String binlogTopic = env.getProperty("spring.kafka.income.consumer.topic");
            String binlogServer = env.getProperty("spring.kafka.income.bootstrap-servers");
            KafkaTemplate<String, Object> kafkaTemplate = buildTemplate(binlogServer);

            config = new SendConfig(binlogTopic, kafkaTemplate);
            idAndConfigMap.put(serverId, config);
        }else if ("settle".equals(serverId)) {
            String binlogTopic = env.getProperty("spring.kafka.settle.consumer.topic");
            String binlogServer = env.getProperty("spring.kafka.settle.bootstrap-servers");
            KafkaTemplate<String, Object> kafkaTemplate = buildTemplate(binlogServer);

            config = new SendConfig(binlogTopic, kafkaTemplate);
            idAndConfigMap.put(serverId, config);
        }else if ("orderContro".equals(serverId)) {
            String binlogTopic = env.getProperty("spring.kafka.control.consumer.topic");
            String binlogServer = env.getProperty("spring.kafka.control.bootstrap-servers");
            KafkaTemplate<String, Object> kafkaTemplate = buildTemplate(binlogServer);

            config = new SendConfig(binlogTopic, kafkaTemplate);
            idAndConfigMap.put(serverId, config);
        }

        return config;
    }

    private KafkaTemplate<String, Object> buildTemplate(String binlogServer) {
        log.info("创建kafka连接，连接地址: {}", binlogServer);
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, binlogServer);
        props.put("key.serializer", StringSerializer.class);
        props.put("value.serializer", StringSerializer.class);
        DefaultKafkaProducerFactory<String, Object> factory = new DefaultKafkaProducerFactory<>(props);
        KafkaTemplate<String, Object> kafkaTemplate = new KafkaTemplate<>(factory);
        kafkaTemplate.setProducerListener(new LoggingProducerListener<>());
        return kafkaTemplate;
    }

    @Autowired
    private BizProperties bizProperties;

    @GetMapping("biz/properties")
    public BizResult<BizProperties> getProperties() throws Exception {
        return BizResult.create(AopProxyUtils.getTarget(bizProperties));
    }

    @Autowired
    private SpuService spuService;

    @GetMapping("spu/info/list")
    public BizResult<List<SpuInfoBO>> list(@RequestParam("deviceType") Integer deviceType) {
        List<SpuInfoBO> infoBOS = spuService.list(Collections.singletonList(deviceType));
        return BizResult.create(infoBOS);
    }

    @Resource
    private LoadingCache<String, PimSpuDO> spuCache;

    @GetMapping("print/spu/cache")
    public BizResult<String> printSpuCache() {
        CacheStats stats = spuCache.stats();
        return BizResult.create(stats.toString());
    }

    @Resource
    private ChannelDiffCompleteProcessor channelDiffCompleteProcessor;

    @GetMapping("channel/diff/notify")
    public void process() {
        channelDiffCompleteProcessor.process(ShardCompleteMessage.builder().build());
    }

    @Resource
    private ControlOrderCheckConsistencyMapper controlOrderCheckConsistencyMapper;
    @GetMapping("channel/test/check")
    public void testCheck() {
        ControlOrderCheckConsistencyDO controlOrderCheckConsistencyDO = controlOrderCheckConsistencyMapper.selectByPrimaryKey(167L);
        log.info("ControlOrderCheckConsistencyDO={}",JSONObject.toJSONString(controlOrderCheckConsistencyDO));
    }

    @Resource
    private ContractShopDivideMapper contractShopDivideMapper;
    @GetMapping("channel/test/contract")
    public void testContract() {
        ContractShopDivideDO controlOrderCheckConsistencyDO = contractShopDivideMapper.selectByPrimaryKey(167L);
        log.info("ContractShopDivideDO={}",JSONObject.toJSONString(controlOrderCheckConsistencyDO));
    }

    @Resource
    private TenantMapper tenantMapper;

    @GetMapping("channel/test/customer")
    public void testCustomer() {
        List<TenantDO> tenantDOList = tenantMapper.selectByIds(Arrays.asList(100L,101L));
        log.info("tenantDO={}",JSONObject.toJSONString(tenantDOList));
    }

    @Resource
    private ProductDeviceInfoMapper productDeviceInfoMapper;

    @GetMapping("channel/test/emei")
    public void emei() {
        List<ProductDeviceInfoDO> tenantDOList = productDeviceInfoMapper.selectByDeviceNos(Arrays.asList("p021705360753918"));
        log.info("ProductDeviceInfoDO={}",JSONObject.toJSONString(tenantDOList));
    }

    @Resource
    private PaymentMapper paymentMapper;
    @GetMapping("channel/test/lhc")
    public void lhc() {
        PaymentDO paymentDO = paymentMapper.selectByPrimaryKey(1000L);
        log.info("paymentDO={}",JSONObject.toJSONString(paymentDO));
    }

    @Resource
    private AgentMapper agentMapper;

    @GetMapping("channel/test/oss")
    public void oss() {
        List<AgentDO> list = agentMapper.selectByIds(Arrays.asList(1000L));
        log.info("AgentDO={}",JSONObject.toJSONString(list));
    }

    @Resource
    private ShardingTradeOrderMapper shardingTradeOrderMapper;

    @GetMapping("channel/test/newYork")
    public void newYork() {
        List<ShardingTradeOrderDO> list = shardingTradeOrderMapper.selectByBizOrderNo("111111");
        log.info("ShardingTradeOrderDO={}",JSONObject.toJSONString(list));
    }

    @Resource
    private TenantShopMapper tenantShopMapper;

    @GetMapping("channel/test/shop")
    public void shop() {
        List<TenantShopDO> list = tenantShopMapper.selectByShopIds(Arrays.asList(100L,101L));
        log.info("TenantShopDO={}",JSONObject.toJSONString(list));
    }
}
