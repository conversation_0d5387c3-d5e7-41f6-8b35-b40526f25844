package so.dian.huashan.collection.controller.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/25 17:12
 * @description:
 */
@Data
public class CollectionPipelineReq {

    private Long id;

    @NotNull(message = "业务域ID不能为空")
    private Long bizDomainId;

    @NotEmpty(message = "策略编码不能为空")
    private String strategyCode;

    @NotNull(message = "采集方式不能为空")
    private Byte collectionWay;

    @NotEmpty(message = "采集的表名不能为空")
    private String tableName;
}
