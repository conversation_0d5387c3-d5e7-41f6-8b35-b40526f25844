package so.dian.huashan.collection.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.himalaya.common.entity.BizResult;
import so.dian.huashan.collection.controller.request.PullThirdBillReq;
import so.dian.huashan.collection.service.ManualCompensateService;
import so.dian.huashan.collection.service.entity.CollectionProcessResult;

import javax.annotation.Resource;

/**
 * @author: mi<PERSON><PERSON><PERSON>
 * @create: 2023/09/28 10:31
 * @description: 手动补偿binlog消息
 */
@Slf4j
@RestController
public class ManualCompensateController {

    @Resource
    private ManualCompensateService manualCompensateService;

    @Resource
    private ThreadPoolTaskExecutor pullBillExecutor;

    @PostMapping("manual/compensate/binlog")
    public BizResult<CollectionProcessResult> compensateBinlog(@RequestBody String binlog) {
        CollectionProcessResult compensated = manualCompensateService.compensateBinlog(binlog);
        return BizResult.create(compensated);
    }

    @PostMapping("manual/pull/thirdBill")
    public BizResult<String> pullThirdBill(@RequestBody PullThirdBillReq billReq) {

        try {
            pullBillExecutor.submit(() -> manualCompensateService.pullThirdBill(billReq));
            return BizResult.create("拉取三方账单请求成功");
        }catch (Exception e) {
            log.error("拉取三方账单请求成功", e);
            return BizResult.create("拉取三方账单请求成功");
        }
    }

}
