package so.dian.huashan;

import cn.hutool.extra.spring.EnableSpringUtil;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import so.dian.himalaya.boot.aop.annotation.EnableRedisson;
import so.dian.himalaya.boot.configuration.HimalayaAutoConfiguration;
import so.dian.huashan.framework.datasource.MultipleDataSourceRegistrar;

@EnableRedisson
@Import({MultipleDataSourceRegistrar.class})
@SpringBootApplication(exclude = {
        HimalayaAutoConfiguration.class
})
@EnableSpringUtil
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"so.dian.huashan.common.facade.remote"})
@EnableBatchProcessing
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
