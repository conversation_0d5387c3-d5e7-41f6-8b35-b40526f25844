<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.4.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>so.dian.huashan</groupId>
    <artifactId>huashan</artifactId>
    <version>0.0.1-RELEASE</version>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>对账系统</description>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
        <dian-spring-boot-dependencies.version>2.4.2</dian-spring-boot-dependencies.version>
        <dian-spring-cloud-dependencies.version>2.4.6</dian-spring-cloud-dependencies.version>
        <agent-service.version>2.7.0-RELEASE</agent-service.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>so.dian.spring.boot</groupId>
                <artifactId>dian-spring-boot-dependencies</artifactId>
                <version>${dian-spring-boot-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>so.dian.spring.cloud</groupId>
                <artifactId>dian-spring-cloud-dependencies</artifactId>
                <version>${dian-spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2020.0.3</version> <!-- 确保使用与 Spring Boot 2.4.0 兼容的版本 -->
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>1.38.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-instrumentation-bom-alpha</artifactId>
                <version>2.3.0-alpha</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

   <dependencies>
       <dependency>
           <groupId>so.dian</groupId>
           <artifactId>dove-client</artifactId>
           <version>0.0.4.RELEASE</version>
       </dependency>

       <!-- 上传sftp -->
       <dependency>
           <groupId>com.jcraft</groupId>
           <artifactId>jsch</artifactId>
           <version>0.1.55</version>
       </dependency>

       <dependency>
           <groupId>com.github.wechatpay-apiv3</groupId>
           <artifactId>wechatpay-java</artifactId>
           <version>0.2.12</version>
       </dependency>
       <dependency>
           <groupId>org.elasticsearch.client</groupId>
           <artifactId>elasticsearch-rest-high-level-client</artifactId>
           <version>7.9.3</version>
       </dependency>

       <!-- opentelemetry start -->
       <dependency>
           <groupId>io.opentelemetry.instrumentation</groupId>
           <artifactId>opentelemetry-spring-boot-starter</artifactId>
       </dependency>
       <dependency>
           <groupId>io.opentelemetry.contrib</groupId>
           <artifactId>opentelemetry-samplers</artifactId>
           <version>1.33.0-alpha</version>
       </dependency>
       <dependency>
           <groupId>io.opentelemetry.instrumentation</groupId>
           <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
       </dependency>
       <dependency>
           <groupId>io.opentelemetry.instrumentation</groupId>
           <artifactId>opentelemetry-apache-httpclient-5.2</artifactId>
       </dependency>
       <dependency>
           <groupId>io.github.openfeign</groupId>
           <artifactId>feign-hc5</artifactId>
           <version>12.1</version>
       </dependency>
       <dependency>
           <groupId>so.dian.genesis</groupId>
           <artifactId>genesis-biz-api</artifactId>
           <version>1.0.0-RELEASE</version>
       </dependency>
       <dependency>
           <groupId>io.swagger</groupId>
           <artifactId>swagger-annotations</artifactId>
           <version>1.5.20</version>
           <scope>compile</scope>
       </dependency>
       <dependency>
           <groupId>io.github.openfeign</groupId>
           <artifactId>feign-core</artifactId>
           <version>12.1</version>
       </dependency>
       <dependency>
           <groupId>io.github.openfeign</groupId>
           <artifactId>feign-slf4j</artifactId>
           <version>12.1</version>
       </dependency>
       <dependency>
           <groupId>io.opentelemetry.instrumentation</groupId>
           <artifactId>opentelemetry-spring-kafka-2.7</artifactId>
       </dependency>
       <dependency>
           <groupId>io.opentelemetry.instrumentation</groupId>
           <artifactId>opentelemetry-rocketmq-client-4.8</artifactId>
       </dependency>
       <dependency>
           <groupId>io.opentelemetry.instrumentation</groupId>
           <artifactId>opentelemetry-jdbc</artifactId>
       </dependency>
       <!-- opentelemetry end -->

       <!-- mesh start-->
       <dependency>
           <groupId>so.dian.spring.cloud</groupId>
           <artifactId>mesh-spring-cloud-starter</artifactId>
       </dependency>
       <dependency>
           <groupId>so.dian.spring.boot</groupId>
           <artifactId>actuator-extension-spring-boot-starter</artifactId>
       </dependency>
       <dependency>
           <groupId>org.springframework.cloud</groupId>
           <artifactId>spring-cloud-starter-loadbalancer</artifactId>
       </dependency>
       <!-- mesh end-->

       <dependency>
           <groupId>com.alibaba</groupId>
           <artifactId>fastjson</artifactId>
           <version>1.2.83</version>
       </dependency>
       <dependency>
           <groupId>so.dian.agent</groupId>
           <artifactId>agent-service-api</artifactId>
           <version>${agent-service.version}</version>
           <exclusions>
               <exclusion>
                   <groupId>*</groupId>
                   <artifactId>*</artifactId>
               </exclusion>
           </exclusions>
       </dependency>
       <dependency>
           <groupId>org.projectlombok</groupId>
           <artifactId>lombok</artifactId>
           <optional>true</optional>
       </dependency>

       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-batch</artifactId>
       </dependency>
       <!--web-->
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-web</artifactId>
       </dependency>
       <!-- actuator检查检查 -->
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-actuator</artifactId>
       </dependency>
       <!-- nacos -->
       <dependency>
           <groupId>com.alibaba.cloud</groupId>
           <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
           <exclusions>
               <exclusion>
                   <groupId>com.alibaba.nacos</groupId>
                   <artifactId>nacos-client</artifactId>
               </exclusion>
           </exclusions>
       </dependency>
       <dependency>
           <groupId>com.alibaba.nacos</groupId>
           <artifactId>nacos-client</artifactId>
           <version>1.4.7</version>
           <exclusions>
               <exclusion>
                   <artifactId>simpleclient</artifactId>
                   <groupId>io.prometheus</groupId>
               </exclusion>
           </exclusions>
       </dependency>
       <dependency>
           <groupId>org.springframework.cloud</groupId>
           <artifactId>spring-cloud-starter-bootstrap</artifactId>
       </dependency>

       <dependency>
           <groupId>so.dian.fis.lushan</groupId>
           <artifactId>lushan-api</artifactId>
           <version>1.0.0-RELEASE</version>
       </dependency>

       <!-- xxl-job-->
       <dependency>
           <groupId>so.dian.xdjob</groupId>
           <artifactId>xdjob-boot-15x-starter</artifactId>
           <version>2021.03</version>
           <exclusions>
               <exclusion>
                   <artifactId>netty-all</artifactId>
                   <groupId>io.netty</groupId>
               </exclusion>
           </exclusions>
       </dependency>
       <dependency>
           <artifactId>netty-all</artifactId>
           <groupId>io.netty</groupId>
           <version>4.1.48.Final</version>
       </dependency>

       <dependency>
           <groupId>so.dian.huangshan</groupId>
           <artifactId>huangshan</artifactId>
           <version>1.0.0-RELEASE</version>
       </dependency>

       <!-- sls 接入-->
       <dependency>
           <groupId>so.dian.common</groupId>
           <artifactId>dian-logger</artifactId>
           <version>2.0.2</version>
       </dependency>

       <dependency>
           <groupId>cn.hutool</groupId>
           <artifactId>hutool-all</artifactId>
           <version>5.6.0</version>
       </dependency>

       <!-- 数据源 -->
       <dependency>
           <groupId>com.alibaba</groupId>
           <artifactId>druid-spring-boot-starter</artifactId>
           <version>1.2.20</version>
       </dependency>
       <dependency>
           <groupId>com.github.pagehelper</groupId>
           <artifactId>pagehelper-spring-boot-starter</artifactId>
           <version>1.2.12</version>
       </dependency>
       <dependency>
           <groupId>org.mybatis.spring.boot</groupId>
           <artifactId>mybatis-spring-boot-starter</artifactId>
           <version>1.3.2</version>
       </dependency>
       <dependency>
           <groupId>com.alibaba</groupId>
           <artifactId>druid</artifactId>
           <version>1.2.20</version>
       </dependency>
       <dependency>
           <groupId>com.mysql</groupId>
           <artifactId>mysql-connector-j</artifactId>
           <version>8.3.0</version>
       </dependency>
       <dependency>
           <groupId>org.springframework</groupId>
           <artifactId>spring-jdbc</artifactId>
       </dependency>
       <dependency>
           <groupId>org.postgresql</groupId>
           <artifactId>postgresql</artifactId>
       </dependency>
       <!-- himalaya -->
       <dependency>
           <groupId>so.dian.himalaya</groupId>
           <artifactId>himalaya-spring-boot-starter</artifactId>
           <version>1.1.6-RELEASE</version>
           <exclusions>
               <exclusion>
                   <artifactId>xxl-job-core</artifactId>
                   <groupId>so.dian.xxl.job</groupId>
               </exclusion>
           </exclusions>
       </dependency>

       <dependency>
           <groupId>com.google.guava</groupId>
           <artifactId>guava</artifactId>
           <version>20.0</version>
       </dependency>

       <!-- MQ -->
       <dependency>
           <groupId>org.springframework.kafka</groupId>
           <artifactId>spring-kafka</artifactId>
           <version>2.8.0</version>
       </dependency>

       <dependency>
           <groupId>org.apache.rocketmq</groupId>
           <artifactId>rocketmq-spring-boot-starter</artifactId>
           <version>2.2.0</version>
       </dependency>

       <dependency>
           <groupId>com.qcloud</groupId>
           <artifactId>cos_api</artifactId>
           <version>4.6</version>
           <exclusions>
               <exclusion>
                   <groupId>org.slf4j</groupId>
                   <artifactId>slf4j-log4j12</artifactId>
               </exclusion>
               <exclusion>
                   <groupId>log4j</groupId>
                   <artifactId>log4j</artifactId>
               </exclusion>
           </exclusions>
       </dependency>
       <!-- 第三方sdk-->
       <dependency>
           <groupId>com.alipay.sdk</groupId>
           <artifactId>alipay-sdk-java</artifactId>
           <version>4.40.13.ALL</version>
           <exclusions>
               <exclusion>
                   <groupId>commons-logging</groupId>
                   <artifactId>commons-logging</artifactId>
               </exclusion>
           </exclusions>
       </dependency>

       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-cache</artifactId>
       </dependency>

   </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>so.dian.ci.scanner.maven</groupId>
                <artifactId>ci-scanner-maven-plugin</artifactId>
                <version>1.0.16</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.2</version>
                <configuration>
                    <!--结果数据存放,配合execution:jacoco-site-->
                    <destFile>target/coverage-reports/jacoco-unit.exec</destFile>
                    <dataFile>target/coverage-reports/jacoco-unit.exec</dataFile>
                    <rules>
                        <rule implementation="org.jacoco.maven.RuleConfiguration">
                            <element>BUNDLE</element>
                            <limits>　　
                                <!-- 指定方法覆盖到50% -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>METHOD</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0</minimum>
                                </limit>
                                <!-- 指定分支覆盖到50% -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>BRANCH</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0</minimum>
                                </limit>
                                <!-- 指定类覆盖到100%，遗失类最多个数 -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>CLASS</counter>
                                    <value>MISSEDCOUNT</value>
                                    <maximum>10</maximum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <id>jacoco-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <!--这个check:对代码进行检测，控制项目构建成功还是失败-->
                    <execution>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                    <!--这个report:对代码进行检测，然后生成index.html在 target/site/index.html中可以查看检测的详细结果-->
                    <execution>
                        <id>jacoco-site</id>
                        <phase>package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.4.1</version>
                <configuration>
                  <containerizingMode>packaged</containerizingMode>
                  <container>
                    <entrypoint>/opt/jboss/container/java/run/run-java.sh</entrypoint>
                  </container>
                  <extraDirectories>
                    <paths>
                      <path>
                        <from>target/</from>
                        <includes>${project.build.finalName}.jar</includes>
                        <into>/deployments</into>
                      </path>
                        <!-- 确保 META-INF 目录的资源文件也包含在内 -->
                        <path>
                            <from>src/main/resources</from>  <!-- 本地路径 -->
                            <includes>alipay/*</includes>  <!-- 复制所有文件 -->
                            <into>/deployments</into>  <!-- 容器内路径 /tmp/alipay/ -->
                        </path>
                    </paths>
                  </extraDirectories>
                  <pluginExtensions>
                    <pluginExtension>
                      <implementation>
                        com.google.cloud.tools.jib.maven.extension.layerfilter.JibLayerFilterExtension</implementation>
                      <configuration
                        implementation="com.google.cloud.tools.jib.maven.extension.layerfilter.Configuration">
                        <filters>
                          <filter>
                            <!-- exclude all jib layers, which is basically anything in /app -->
                            <glob>/app/**</glob>
                          </filter>
                          <filter>
                            <!-- this is our fat jar, this should be kept by adding it into its own layer -->
                            <glob>/deployments/${project.build.finalName}.jar</glob>
                            <toLayer>jib-custom-fatJar</toLayer>
                          </filter>
                        </filters>
                      </configuration>
                    </pluginExtension>
                  </pluginExtensions>
                  <from>
                    <image>
                      quay.xiaodiankeji.net/openjdk/openjdk-8-runtime-skywalking@sha256:4115001cdff162df855650863d5c23877e3e01032ba538dc34e960b0fadd580e</image>
                    <platforms>
                      <platform>
                        <architecture>arm64</architecture>
                        <os>linux</os>
                      </platform>
                      <platform>
                        <architecture>amd64</architecture>
                        <os>linux</os>
                      </platform>
                    </platforms>
                  </from>
                  <to>
                    <image>quay.xiaodiankeji.net/dian-dev/${project.build.finalName}</image>
                    <auth>
                      <username>${env.REGISTRY_USR}</username>
                      <password>${env.REGISTRY_PSW}</password>
                    </auth>
                  </to>
                </configuration>
                <dependencies>
                  <dependency>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-layer-filter-extension-maven</artifactId>
                    <version>0.3.0</version>
                  </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <version>2.43.0</version>
                <configuration>
                  <ratchetFrom>origin/main</ratchetFrom>
                  <formats>
                    <format>
                      <includes>
                        <include>*.md</include>
                        <include>.gitignore</include>
                      </includes>
                      <trimTrailingWhitespace></trimTrailingWhitespace>
                      <endWithNewline></endWithNewline>
                      <indent>
                        <tabs>true</tabs>
                        <spacesPerTab>4</spacesPerTab>
                      </indent>
                    </format>
                  </formats>
                  <java>
                    <includes>
                      <include>src/main/java/**/*.java</include>
                      <include>src/test/java/**/*.java</include>
                    </includes>
                    <googleJavaFormat>
                      <style>GOOGLE</style>
                      <reflowLongStrings>true</reflowLongStrings>
                    </googleJavaFormat>
                    <importOrder>
                      <wildcardsLast>false</wildcardsLast>
                      <order>java|javax|jakarta,org.springframework,org,com,so.dian,,\#so.dian,\#</order>
                      <semanticSort>false</semanticSort>
                    </importOrder>
                    <removeUnusedImports></removeUnusedImports>
                    <formatAnnotations></formatAnnotations>
                  </java>
                  <pom>
                    <includes>
                      <include>pom.xml</include>
                    </includes>
                    <sortPom></sortPom>
                  </pom>
                </configuration>
            </plugin>
            <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-pmd-plugin</artifactId>
              <version>3.21.2</version>
            </plugin>
        </plugins>
    </build>
    <reporting>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
            <version>3.5.0</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-pmd-plugin</artifactId>
            <version>3.21.2</version>
            <reportSets>
              <reportSet>
                <id>aggregate</id>
                <!-- don't run aggregate in child modules -->
                <reports>
                  <report>aggregate-pmd</report>
                  <report>aggregate-cpd</report>
                </reports>
                <inherited>false</inherited>
              </reportSet>
              <reportSet>
                <id>default</id>
                <reports>
                  <report>pmd</report>
                  <report>cpd</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
    </reporting>
</project>